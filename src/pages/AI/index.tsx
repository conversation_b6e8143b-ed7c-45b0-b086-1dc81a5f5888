import { createRef, useEffect } from 'react';

export default () => {
  const ref = createRef<HTMLDivElement>();

  function initScript(container: HTMLDivElement) {
    const el = document.createElement('iframe');
    container.appendChild(el);
    const APP_ID = '40f44ad9-4c0c-427d-a7c0-b3868f3feed2';
    const APP_CODE = 'embed4LFeTE4roN47TTTjEmpa';
    el.style.width = '100%';
    el.style.height = '100%';
    el.style.border = 'none';
    const script = document.createElement('script');
    script.src =
      'https://agi-dev-platform-web.bj.bcebos.com/ai_apaas/embed/output/embedFullSDK.js?responseExpires=0';
    document.body.appendChild(script);

    script.onload = () => {
      const isMobile = () => {
        const userAgent = navigator.userAgent || navigator.vendor;
        return /android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
          userAgent.toLowerCase(),
        );
      };

      const type = isMobile() ? '' : 'full';
      const renderUrl = `https://wx.baeapps.com/api/ai_apaas/v1/web_embed/html?type=${type}&app_id=${APP_ID}&code=${APP_CODE}`;

      el.src = renderUrl;
    };
  }

  useEffect(() => {
    initScript(document.querySelector('#renderContent') as HTMLDivElement);
  }, []);

  return (

      <div
        ref={ref}
        style={{
          width: '100%',
          height: '100%',
        }}
        id="renderContent"
      ></div>
   
  );
};
