/* eslint-disable guard-for-in */

import { getTermColumn, getTermColumns } from '@/components/CommonColumn';
import { useModal } from '@/components/UseModal';
import {
  EvalutaionDetailParams,
  fetchEvalutaionDetail,
  getEvalutaionList,
  updateEvalutaionDetail,
} from '@/services/evaluation';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { PageContainer, ProFormSelect, ProTable } from '@ant-design/pro-components';

import { StarWidthCount } from '@/components/Icon/StarWithCount';
import { getMatrix } from '@/services/class';
import { useModel } from '@umijs/max';
import { Button, message } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { ChildEntity } from '../Child/data';
import { EvaluationDetail } from './EvaluationDetail';

export interface EvaluationProps {
  matrix1Id: number;
  matrix1Title: string;
  matrix2Id: number;
  matrix2Title: string;
  matrix3Id: number;
  matrix3Title: string;
  children: ChildEntity[];
  matrix1Count?: number;
  matrix2Count?: number;
  evaluationId: number;
}

const Evalution: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const proTableFormRef = useRef<ProFormInstance>();
  const [evaluationData, setEvaluationData] = useState<EvaluationProps[]>([]);
  const [AntModal, showModal, hideModal] = useModal();
  // 新增配置项
  const [activeParams, setActiveParams] = useState<EvalutaionDetailParams>();
  // const [activeChild, setActiveChild] = useState<any>();
  const [evaluationDetail, setEvaluationDetail] = useState<any>();
  const [evaluationSummary, setEvaluationSummary] = useState<any>({});
  // const [evaluationColumns, setEvaluationColumns] = useState<any>([]);
  const [columns, setColumns] = useState<ProColumns<API.RuleListItem>[]>([]);

  const optionsMap = useRef({
    matrixOptions: [],
  });

  const score = useRef(1);
  const { initialState } = useModel('@@initialState');
  const [detailVisible, setDetailVisible] = useState(false);

  const onCellClick = (record: EvaluationProps, child: ChildEntity) => {
    const ev = {
      evaluationId: record.evaluationId,
      childId: child.id,
      matrixId: record.matrix3Id,
    };
    setActiveParams(ev);
    fetchEvalutaionDetail(ev).then((res) => {
      setEvaluationDetail(res.data);
      score.current = res.data.score || 0;
      showModal();
      setDetailVisible(true);
    });
  };

  const baseColumn: ProColumns<API.RuleListItem>[] = [
    {
      title: '领域',
      dataIndex: 'matrix1Id',
      fixed: 'left',
      width: 100,
      render(_, record: any) {
        return {
          children: record.matrix1Title,
          props: {
            rowSpan: record.matrix1Count ? record.matrix1Count : 0,
          },
        };
      },
      renderFormItem: (_: any, prop: any) => {
        if (prop.type === 'form') {
          return null;
        }

        return (
          <ProFormSelect
            name="matrix1Id"
            allowClear={true}
            options={optionsMap.current.matrixOptions}
          />
        );
      },
    },
    {
      title: '维度',
      search: false,
      fixed: 'left',
      width: 100,
      dataIndex: 'matrix2TitleLabel',
      render(_, record: any) {
        return {
          children: record.matrix2Title,
          props: {
            rowSpan: record.matrix2Count ? record.matrix2Count : 0,
          },
        };
      },
    },
    {
      title: '子维度',
      width: 200,
      ellipsis: true,
      search: false,
      dataIndex: 'matrix3Title',
    },
  ];

  const updateTableData = (p: any) => {
    const params: any = {
      ...p,
    };
  
    if(!params.term){
      params.term = initialState?.currentClass[1]
      params.classId = initialState?.currentClass[0]
    }
    const initColumns = [...getTermColumns(proTableFormRef), ...baseColumn];
    getEvalutaionList({
      ...params,
      term: params.term,
      classId: params.classId,
    })
      .then((res) => {
        const evaluation = res.data.evaluation;
        setEvaluationSummary(evaluation);
        const data = res.data.records;
        if (data instanceof Array) {
          let start1Index = 0;
          let start2Index = 0;
          data.forEach((d: EvaluationProps, index: any) => {
            d.evaluationId = evaluation.id;
            const last1 = data[start1Index];
            const last2 = data[start2Index];
            const cur = data[index];
            if (last1.matrix1Title === cur.matrix1Title) {
              last1.matrix1Count = last1.matrix1Count ? last1.matrix1Count + 1 : 1;
              if (last2.matrix2Title === cur.matrix2Title) {
                last2.matrix2Count = last2.matrix2Count ? last2.matrix2Count + 1 : 1;
              } else {
                d.matrix2Count = 1;
                start2Index = index;
              }
            } else {
              d.matrix1Count = 1;
              start1Index = index;
              d.matrix2Count = 1;
              start2Index = index;
            }
          });

          if (data[0]) {
            const children = data[0].children || [];
            initColumns.push(
              ...children.map((child: any) => {
                return {
                  title: child.title,
                  dataIndex: child.id + '',
                  width: 80,
                  search: false,
                  renderText(_: any, record: EvaluationProps) {
                    const c = record.children.find((chi: any) => chi.id === child.id);
                    return c?.score ? (
                      <div>
                        <StarWidthCount
                          onClick={() => {
                            onCellClick(record, child);
                          }}
                          style={{ cursor: 'pointer', color: '#f1d749' }}
                          count={c.score}
                        />
                      </div>
                    ) : (
                      <PlusOutlined
                        onClick={() => {
                          onCellClick(record, child);
                        }}
                        style={{ cursor: 'pointer' }}
                      />
                    );
                  },
                };
              }),
            );
          }

          setEvaluationData(data);
          actionRef.current?.reload();
        }
      })
      .finally(() => {
        setColumns(initColumns);
      });
  };

  useEffect(() => {

  proTableFormRef.current?.setFieldValue('term', initialState?.currentClass[1])
  proTableFormRef.current?.setFieldValue('classId', initialState?.currentClass[0])
    getMatrix({ pid: 0 }).then((res) => {
      optionsMap.current.matrixOptions = [
        ...res.data.map((d: any) => ({
          label: d.title,
          value: d.id,
        })),
      ] as any
    });

  }, []);

  const onConfirmEvaluation = useCallback(
    (params?: EvalutaionDetailParams) => {
      updateEvalutaionDetail({
        ...params,
        score: score.current,
      }).then((response) => {
        hideModal();
        message.success(response.message || '评价成功');
        updateTableData(proTableFormRef.current?.getFieldsValue() || {});
        setDetailVisible(false);
      });
    },
    [score],
  );

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        formRef={proTableFormRef}
        rowKey="id"
        scroll={{ y: 500, x: 'max-content' }}
        pagination={false}
        expandable={{ showExpandColumn: false }}
        toolBarRender={() => []}
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,

          optionRender(config, prop, dom) {
            return [
              ...dom.reverse(),
              <Button
                type="primary"
                key="evalution"
                href={`/evaluation/evaluateDetailBatch/` + evaluationSummary.id}
                target="_blank"
              >
                批量评估
              </Button>,
              <Button
                type="primary"
                key="summary"
                href={`/evaluation/evalutaionStatistics/` + evaluationSummary.id}
                target="_blank"
              >
                统计
              </Button>,
            ];
          },
        }}
        dataSource={evaluationData as any}
        beforeSearchSubmit={(params: any) => {
         
          updateTableData({
            ...params,
          
          });
        }}
        columns={columns}
      />
      <AntModal title="儿童评估" onOk={() => onConfirmEvaluation(activeParams)} width={1000}>
        {detailVisible ? (
          <EvaluationDetail score={score} evaluationDetail={evaluationDetail} />
        ) : null}
      </AntModal>
    </PageContainer>
  );
};

export default Evalution;
