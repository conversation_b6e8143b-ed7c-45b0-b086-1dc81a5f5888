export interface EvaluationSummary {
  id: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string;
  updatedBy: any;
  title: string;
  searchKey: string;
  categoryId: number;
  subjectId: number;
  schoolId: number;
  classId: number;
  extra?: any;
  state: API.StateStatus;
}

export interface Answer {
  score: number;
  title: string;
  desc: string;
}

export interface EvaluationBatchRecord {
  child: {
    id: number;
    title: string;
  };
  lastestScore: number;
  maxScore: number;
  score: number;
}

export interface EvaluationBatchData {
  answers: Answer[];
  records: EvaluationBatchRecord[];
  evaluation: EvaluationSummary;
  result: number;
  total: number;
}

export const defaultInitData = (): EvaluationBatchData => ({
  answers: [],
  records: [],
  total: 0,
  result: 0,
  evaluation: {
    id: 0,
    createdAt: '',
    updatedAt: '',
    deletedAt: '',
    updatedBy: '',
    title: '',
    searchKey: '',
    categoryId: 0,
    subjectId: 0,
    schoolId: 0,
    classId: 0,
    state: 0,
  },
});
