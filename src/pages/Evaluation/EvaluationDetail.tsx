import { ActionType, ProTable } from '@ant-design/pro-components';
import { Descriptions } from 'antd';
import { useMemo, useRef, useState } from 'react';

interface EvaluationDetailProps {
  evaluationDetail?: any;
  score: any;
}

export const EvaluationDetail: React.FC<EvaluationDetailProps> = (props) => {
  const actionRef = useRef<ActionType>();

  const [selectedRowKeys, setSelectRowKeys] = useState<any[]>([props.score.current]);
  const columns = useMemo(() => {
    return [
      {
        title: '分数',
        dataIndex: 'score',
        width: '4em',
      },
      {
        title: '描述',
        dataIndex: 'desc',
        render(_: any, record: any) {
          return (
            <div>
              {record.descTitle ? <b>{record.descTitle}</b> : null}
              {Array.isArray(record.desc)
                ? record.desc.map((d: any, index: number) => (
                    <div key={index}>{`${index + 1}、${d.title}`}</div>
                  ))
                : record.desc + '-。'}
            </div>
          );
        },
      },
    ];
  }, props.evaluationDetail);

  const dataSource = useMemo(() => {
    if (!props.evaluationDetail) return [];
    return props.evaluationDetail.answers;
  }, [props.evaluationDetail]);

  return (
    <div className="EvaluationDetail">
      <div className="EvaluationDetail-select"></div>
      <div className="EvaluationDetail-desc">
        {props.evaluationDetail ? (
          <Descriptions>
            <Descriptions.Item label="儿童姓名">
              {props.evaluationDetail.child.title}
            </Descriptions.Item>
          </Descriptions>
        ) : null}
      </div>
      <div className="EvaluationDetail-table">
        {props.evaluationDetail ? (
          <ProTable<API.RuleListItem, API.PageParams>
            actionRef={actionRef}
            tableAlertRender={() => false}
            rowKey="score"
            onRow={(record: any) => ({
              onClick: () => {
                setSelectRowKeys([record.score]);
                props.score.current = record.score;
              },
            })}
            rowSelection={{
              type: 'radio',
              selectedRowKeys: selectedRowKeys,
              alwaysShowAlert: false,
            }}
            pagination={false}
            expandable={{ showExpandColumn: false }}
            search={false}
            dataSource={dataSource}
            columns={columns}
          />
        ) : null}
      </div>
    </div>
  );
};
