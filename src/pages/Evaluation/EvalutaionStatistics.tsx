/* eslint-disable guard-for-in */
import { evalutaionStatistics } from '@/services/evaluation';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';

import { useParams } from '@umijs/max';

import { Row } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ChildEntity } from '../Child/data';

export interface EvaluationProps {
  matrix1Id: number;
  matrix1Title: string;
  matrix2Id: number;
  matrix2Title: string;
  matrix3Id: number;
  matrix3Title: string;
  children: ChildEntity[];
  matrix1Count?: number;
  matrix2Count?: number;
  evaluationId: number;
}

const School: React.FC = () => {
  const params = useParams();
  const actionRef = useRef<ActionType>();
  const [evaluationData, setEvaluationData] = useState<EvaluationProps[]>([]);
  const [staticsData, setStaticsData] = useState<any>({});
  const baseColumn: ProColumns<API.RuleListItem>[] = [
    {
      title: '领域',
      dataIndex: 'matrix1Title',
      fixed: 'left',
      search: false,
      width: 100,
    },
    {
      title: '维度',
      search: false,
      fixed: 'left',
      width: 200,
      dataIndex: 'matrix2Title',
    },
    {
      title: '子维度',
      search: false,
      fixed: 'left',
      width: 300,
      ellipsis: true,
      dataIndex: 'matrix3Title',
    },
    {
      title: '达标人数',
      dataIndex: 'childResult',
    },
    {
      title: '达标率',
      sorter: (a, b) => {
        return parseInt(a.childPer) > parseInt(b.childPer) ? 1 : -1;
      },
      dataIndex: 'childPer',
    },
  ];

  useEffect(() => {
    evalutaionStatistics(params.id as any).then((res) => {
      const data = res.data.records;

      setStaticsData(res.data);
      if (data instanceof Array) {
        setEvaluationData(data);
        actionRef.current?.reload();
      }
    });
  }, [params.id]);

  const summaryText = useMemo(() => {
    return `当前年级：${staticsData.grade || '-'}。目标分数：${
      staticsData.targetScore || 0
    }。 子维度共计：${staticsData.total || 0}`;
  }, [staticsData]);
  return (
    <PageContainer>
      <Row style={{ background: '#fff', marginBottom: '12px', padding: '12px' }}>
        <span style={{ fontWeight: 'bold' }}>{summaryText}</span>
      </Row>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        scroll={{ y: 500, x: 'max-content' }}
        pagination={false}
        expandable={{ showExpandColumn: false }}
        toolBarRender={() => []}
        search={false}
        dataSource={evaluationData as any}
        columns={baseColumn}
      />
    </PageContainer>
  );
};

export default School;
