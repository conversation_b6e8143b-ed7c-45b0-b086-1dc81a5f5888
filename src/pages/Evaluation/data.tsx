import dayjs from 'dayjs';

export const yesOrNoOptions = [
  {
    label: '是',
    value: 1,
  },
  {
    label: '否',
    value: 0,
  },
];

export const EducationalEnum = {
  1: '未上过学',
  2: '小学毕业',
  3: '初中毕业',
  4: '高中',
  5: '职高或中专',
  6: '大专',
  7: '本科',
  8: '硕士研究生',
  9: '博士研究生',
};

export const EducationalOptions = [
  {
    label: '未上过学',
    value: 1,
  },
  {
    label: '小学毕业',
    value: 2,
  },
  {
    label: '初中毕业',
    value: 3,
  },
  {
    label: '高中',
    value: 4,
  },
  {
    label: '职高或中专',
    value: 5,
  },
  {
    label: '大专',
    value: 6,
  },
  {
    label: '本科',
    value: 7,
  },
  {
    label: '硕士研究生',
    value: 8,
  },
  {
    label: '博士研究生',
    value: 9,
  },
];

interface ChildEntity {
  title: string;
  sex: number;
  birthday: dayjs.Dayjs | null;
  entryTime: dayjs.Dayjs | null;
  symptoms: number;
  isSingle: number;
  fatherName: string;
  fatherMobile: string;
  fatherEducational: number | null;
  fatherProfession: string;
  fatherWorkspace: string;
  motherName: string;
  motherMobile: string;
  motherEducational: number | null;
  motherProfession: string;
  motherWorkspace: string;
  note: string;
  id?: string;
}

export const initData: ChildEntity = {
  title: '',
  sex: 1,
  symptoms: 1,
  birthday: null,
  entryTime: null,
  isSingle: 1,
  fatherName: '',
  fatherMobile: '',
  fatherEducational: 7,
  motherName: '',
  motherMobile: '',
  motherEducational: 7,
  fatherProfession: '',
  motherProfession: '',
  fatherWorkspace: '',
  motherWorkspace: '',
  note: '',
};
