import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Button } from 'antd';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { EvaluationBatchData } from './types';

interface HeaderNodeProps {
  evaluateDetailBatch: EvaluationBatchData;
  evaluationDate: dayjs.Dayjs;
  setEvaluationDate: any;
  onConfirm: any;
}

function getDescString(record: any) {
  return (
    <div>
      {record.descTitle ? <b>{record.descTitle}：</b> : null}
      {Array.isArray(record.desc)
        ? record.desc.map((d: any, index: number) => (
            <span key={index}>{`${index + 1}、${d.title}。`}</span>
          ))
        : record.desc}
    </div>
  );
}

export const HeaderNode: React.FC<HeaderNodeProps> = ({
  evaluateDetailBatch,

  onConfirm,
}) => {
  const scoreText = useMemo(() => {
    return `已完成 ${evaluateDetailBatch.result} / 总数 ${evaluateDetailBatch.total}`;
  }, [evaluateDetailBatch]);

  const columns: ProColumns<API.RuleListItem>[] = [
    {
      title: '分数',
      dataIndex: 'score',
    },
    {
      title: '来源',
      dataIndex: 'source',
      render(text, record) {
        return getDescString(record);
      },
    },
  ];

  return (
    <div style={{ width: '100%' }} className="HeaderNode">
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
        }}
      >
        <div style={{ height: '100%', display: 'flex', alignItems: 'center' }}>
          <span style={{ fontWeight: 'bold' }}>
            {evaluateDetailBatch.evaluation.title.split('_').join('   ')}
          </span>
          &nbsp;&nbsp; &nbsp;&nbsp;
          <span style={{ fontWeight: 'bold' }}>{scoreText}</span>
          &nbsp;&nbsp;
        </div>
        <Button type={'primary'} onClick={onConfirm}>
          提交
        </Button>
      </div>

      <div className="HeaderNode-table">
        <ProTable<API.RuleListItem, API.PageParams>
          rowKey={'key'}
          search={false}
          dataSource={evaluateDetailBatch.answers as any}
          columns={columns}
          scroll={{}}
          size="small"
          pagination={false}
          toolBarRender={false}
        />
      </div>
    </div>
  );
};
