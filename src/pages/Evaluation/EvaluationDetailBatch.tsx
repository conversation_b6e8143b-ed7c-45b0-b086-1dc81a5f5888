/* eslint-disable guard-for-in */

import { getMatrix } from '@/services/api';
import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { PageContainer, ProFormSelect, ProTable } from '@ant-design/pro-components';
import { message, Radio, Row } from 'antd';
import React, { useEffect, useRef, useState } from 'react';

import { MatrixOptions } from '@/components/CommonColumn';
import { StarWidthCount } from '@/components/Icon/StarWithCount';
import { batchEvalutaionList, batchEvalutaionSave } from '@/services/evaluation';
import { useParams } from '@umijs/max';
import dayjs from 'dayjs';
import { HeaderNode } from './HeaderNode';
import { defaultInitData, EvaluationBatchData } from './types';

const stableValue = 'stableValue';

function getDescString(record: any) {
  return (
    <div>
      {record.descTitle ? <b>{record.descTitle}</b> : null}
      {Array.isArray(record.desc)
        ? record.desc.map((d: any, index: number) => (
            <div key={index}>{`${index + 1}、${d.title}`}</div>
          ))
        : record.desc}
    </div>
  );
}

const School: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const params = useParams();
  const proTableFormRef = useRef<ProFormInstance>();

  const [isInited, setIsInited] = useState(false);
  const [evaluateDetailBatch, setEvaluationDetail] = useState(defaultInitData());

  const matrixOptions = useRef<MatrixOptions>({
    matrix1Options: [],
    matrix2Options: [],
    matrix3Options: [],
  });

  const [evaluationDate, setEvaluationDate] = useState(dayjs());

  const initOptions = async (props: { matrix1Id?: any; matrix2Id?: any; matrix3Id?: any }) => {
    const { matrix1Id, matrix2Id } = props;

    async function updateMatrixOptions(pid: any) {
      if (pid === stableValue) {
        return;
      }
      const options = await getMatrix({ pid });
      matrixOptions.current.matrix3Options = options.data.map((d: any) => ({
        label: d.title,
        value: d.id,
      }));
      proTableFormRef.current?.setFieldValue(
        'matrix3Id',
        matrixOptions.current.matrix3Options[0].value,
      );
    }

    if ('matrix1Id' in props) {
      if (!matrix1Id) {
        matrixOptions.current.matrix2Options = [];
        matrixOptions.current.matrix3Options = [];
        proTableFormRef.current?.setFieldValue('matrix2Id', '');
        proTableFormRef.current?.setFieldValue('matrix3Id', '');
        return;
      }
      const options = await getMatrix({ pid: matrix1Id });
      matrixOptions.current.matrix2Options = options.data.map((d: any) => ({
        label: d.title,
        value: d.id,
      }));
      matrixOptions.current.matrix3Options = [];
      proTableFormRef.current?.setFieldValue(
        'matrix2Id',
        matrixOptions.current.matrix2Options[0].value,
      );
      await updateMatrixOptions(options.data[0].id);
    }
    if ('matrix2Id' in props) {
      if (!matrix2Id) {
        matrixOptions.current.matrix3Options = [];
        proTableFormRef.current?.setFieldValue('matrix3Id', '');
        return;
      }
      await updateMatrixOptions(matrix2Id);
    }
  };

  const searchColumns: ProColumns<API.RuleListItem>[] = [
    {
      title: '领域',
      dataIndex: 'matrix1Id',
      hideInTable: true,
      key: 'matrix1Id',
      valueType: 'select',
      renderFormItem: (item: any, prop: any) => {
        if (prop.type === 'form') {
          return null;
        }

        return (
          <ProFormSelect
            name="matrix1Id"
            allowClear={false}
            onChange={(val) => initOptions({ matrix1Id: val })}
            options={matrixOptions.current.matrix1Options}
          />
        );
      },
    },
    {
      title: '维度',
      dataIndex: 'matrix2Id',
      hideInTable: true,
      key: 'matrix2Id',
      valueType: 'select',
      renderFormItem: (item: any, prop: any) => {
        if (prop.type === 'form') {
          return null;
        }
        return (
          <ProFormSelect
            onChange={(val) => initOptions({ matrix2Id: val })}
            name="matrix2Id"
            allowClear={false}
            options={matrixOptions.current.matrix2Options}
          />
        );
      },
    },
    {
      title: '子维度',
      dataIndex: 'matrix3Id',
      hideInTable: true,
      key: 'matrix3Id',
      valueType: 'select',
      renderFormItem: (item: any, prop: any) => {
        if (prop.type === 'form') {
          return null;
        }
        return (
          <ProFormSelect
            allowClear={false}
            name="matrix3Id"
            options={matrixOptions.current.matrix3Options}
          />
        );
      },
    },
  ];

  const cls: ProColumns<API.RuleListItem>[] = [
    {
      title: '儿童姓名',
      dataIndex: 'title',
      search: false,
      renderText(_, record: any) {
        return record.child.title;
      },
    },
    {
      title: '最新',
      search: false,
      dataIndex: 'lastestScore',
      render(_: any, record: any) {
        const s = record.lastestScore && record.lastestScore.score;

        return <StarWidthCount count={s} />;
      },
    },

    {
      title: '最高',
      search: false,
      dataIndex: 'maxScore',
      render(_: any, record: any) {
        const s = record.maxScore && record.maxScore.score;
        return <StarWidthCount count={s} />;
      },
    },
  ];

  const [columns, setColumns] = useState([...cls, ...searchColumns]);
  const updateTableData = (p?: any) => {
    const ps = {
      ...p,
      evaluationId: params.id,
      matrixId: proTableFormRef.current?.getFieldValue('matrix3Id'),
    };

    if (!ps.current) {
      ps.current = actionRef.current?.pageInfo?.current;
    }
    if (!ps.pageSize) {
      ps.pageSize = actionRef.current?.pageInfo?.pageSize;
    }
    delete ps.matrix3Id;
    delete ps.matrix2Id;
    delete ps.matrix1Id;

    return batchEvalutaionList(ps).then((response) => {
      const data: EvaluationBatchData = {
        ...response.data,
        records: response.data.records.map((r: any) => ({ ...r, score: r.score || '' })),
      };
      setEvaluationDetail(data);
      actionRef.current?.setPageInfo!({
        ...actionRef.current?.pageInfo,
        total: response.data.total,
      });
      setColumns([
        ...cls,
        ...searchColumns,
        ...data.answers.map(
          (answer) =>
            ({
              title: answer.title.substring(0, 2),
              dataIndex: answer.title,
              search: false,
              tooltip: getDescString(answer),
              render(_: any, record: any) {
                return (
                  <Radio
                    checked={record.score === answer.score}
                    onClick={() => {
                      setEvaluationDetail((d) => {
                        const records = d.records;
                        const index = d.records.findIndex((r) => r === record);
                        return {
                          ...d,
                          records: [
                            ...records.slice(0, index),
                            {
                              ...record,
                              score: answer.score,
                            },
                            ...records.slice(index + 1),
                          ],
                        };
                      });
                    }}
                  ></Radio>
                );
              },
            } as ProColumns<API.RuleListItem>),
        ),
      ]);
    });
  };

  async function initBaseData() {
    const options = await getMatrix({ pid: 0 });
    matrixOptions.current.matrix1Options = options.data.map((d: any) => ({
      label: d.title,
      value: d.id,
    }));

    proTableFormRef.current?.setFieldValue(
      'matrix1Id',
      matrixOptions.current.matrix1Options[0].value,
    );
    await initOptions({ matrix1Id: matrixOptions.current.matrix1Options[0].value });
    setTimeout(async () => {
      await updateTableData();
    })
    
  }

  useEffect(() => {
    initBaseData().finally(() => {
      setIsInited(true);
    });
  }, []);

  const onConfirm = () => {
    batchEvalutaionSave({
      matrixId: proTableFormRef.current?.getFieldValue('matrix3Id'),
      evaluationId: evaluateDetailBatch.evaluation.id,
      childrenScores: evaluateDetailBatch.records.map((r) => ({
        childId: r.child.id,
        score: r.score,
      })),
    }).then((response) => {
      if (response.status === 0) {
        message.success(response.message || '提交成功');
        updateTableData();
      } else {
        message.warning(response.message || '提交失败');
      }
    });
  };

  const rowKeyFn: any = (_: any, index: number) => index + '';
  return (
    <PageContainer>
      <Row style={{ background: '#fff', marginBottom: '12px', padding: '12px', width: '100%' }}>
        <HeaderNode
          key={'header'}
          evaluationDate={evaluationDate}
          evaluateDetailBatch={evaluateDetailBatch}
          onConfirm={onConfirm}
          setEvaluationDate={setEvaluationDate}
        />
      </Row>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        formRef={proTableFormRef}
        rowKey={rowKeyFn}
        pagination={false}
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        beforeSearchSubmit={(params: any) => {
          if (isInited) {
            updateTableData(params);
          }
        }}
        dataSource={evaluateDetailBatch.records as any}
        toolBarRender={() => []}
        columns={columns}
        scroll={{}}
      />
    </PageContainer>
  );
};

export default School;
