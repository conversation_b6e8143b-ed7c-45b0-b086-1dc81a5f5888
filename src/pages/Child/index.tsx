/* eslint-disable guard-for-in */

import {
  delChild,
  getChildList,
  updateChild,
  importChildFromUrl,
  getDownladTemplate,
  getImportTask,
} from '@/services/api';
import { PlusOutlined, DownloadOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, Image, message, Modal } from 'antd';
import React, { useRef, useState } from 'react';
import { useModel } from '@umijs/max';
import { timeColumn } from '@/components/ColumnRender';
import UploadTextComponent from './components/UploadText';

import { useGetEnumsByKey } from '@/services/enums';
import dayjs from 'dayjs';

const School: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [modalText, setModalText] = useState('');
  const [modalTitle, setModalTitle] = useState('查询文件状态中，请勿操作...');
  const { initialState } = useModel('@@initialState');

  const [client, setClient] = useState<any>();

  const handleOk = () => {
    actionRef?.current?.reload();
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const onLevelSchool = (entity: any) => {
    Modal.confirm({
      title: '是否确认离园',
      onOk() {
        updateChild({
          id: entity.id,
          state: 0,
        }).then((r) => {
          if (r.status === 0) {
            actionRef.current?.reload();
            message.success('离园成功');
          }
        });
      },
    });
  };

  // 下载模板
  const handleDownladTemplate = async () => {
    console.log('下载模板');
    if (!initialState?.currentUser?.currentClassId) {
      message.error('请先选择班级！');
      return;
    }
    const params = {
      classId: Number(initialState?.currentUser?.currentClassId),
    };
    const response = await getDownladTemplate(params);
    // 从 响应头中获取文件名

    console.log(response, 'response');
    const url = URL.createObjectURL(response.data);
    const a = document.createElement('a');
    a.href = url;
    a.download = '儿童批量录入模版_幼立方.xlsx'; // 自定义文件名
    a.click();
    URL.revokeObjectURL(url); // 释放资源
  };

  // 查询导入文件状态
  const handleGetImportTask = async (taskId: string) => {
    setIsModalOpen(true);
    setIsLoading(true);
    setModalTitle('查询文件状态中，请勿操作...');
    try {
      const response = await getImportTask(taskId);
      if (response.status === 0) {
        let task = response.data.status;
        if (task === 'COMPLETED') {
          setModalText(response.data.result);
          setModalTitle('文件导入成功');
          setIsLoading(false);
          return;
        } else if (task === 'FAILED') {
          message.error('文件导入失败');
          setIsLoading(false);
          setIsModalOpen(false);
          return;
        }
        // 2s后重新查询
        setTimeout(() => {
          handleGetImportTask(taskId);
        }, 3000);
      }
    } catch (error) {
      message.error('文件导入失败');
      setIsLoading(false);
      setIsModalOpen(false);
    }
  };

  // 文件上传
  const handleMessage = async (url: string) => {
    const params = {
      fileUrl: url,
      classId: Number(initialState?.currentUser?.currentClassId),
    };
    const res = await importChildFromUrl(params);
    if (res.status === 0 && res.data) {
      console.log(res);
      handleGetImportTask(res.data);
      return;
    }
    message.error('导入出错啦，请联系管理员。');
  };

  const [SexEnum, YesOrNoEnum, EducationalEnum, ChildStateEnum] = useGetEnumsByKey([
    'SexEnum',
    'YesOrNoEnum',
    'EducationalEnum',
    'ChildStateEnum',
  ]);
  // 新增配置项

  const columns: ProColumns<API.RuleListItem>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '儿童姓名',
      dataIndex: 'title',
      valueType: 'text',

      width: 120,
    },
    {
      title: '出生年月',
      dataIndex: 'birthday',
      valueType: 'text',
      width: 120,
      search: false,
      renderText(text) {
        return dayjs(text).format('YYYY-MM-DD');
      },
    },
    {
      title: '入园时间',
      dataIndex: 'entryTime',
      valueType: 'text',
      width: 120,
      search: false,
      renderText(text) {
        return dayjs(text).format('YYYY-MM-DD');
      },
    },
    {
      title: '是否有发展症状',
      dataIndex: 'symptoms',
      valueType: 'text',
      width: 180,
      search: false,
      valueEnum: {
        1: '正常儿童',
        2: '疑似特殊',
        3: '诊断特殊',
      },
    },
    {
      title: '性别',
      dataIndex: 'sex',
      valueType: 'text',
      width: 80,
      valueEnum: SexEnum,
    },
    {
      title: '是否独生子女',
      dataIndex: 'isSingle',
      valueType: 'text',
      width: 120,
      search: false,
      valueEnum: YesOrNoEnum,
    },

    // {
    //   title: '父亲姓名',
    //   dataIndex: 'fatherName',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    // },
    // {
    //   title: '父亲手机号',
    //   dataIndex: 'fatherMobile',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    // },
    // {
    //   title: '父亲学历',
    //   dataIndex: 'fatherEducational',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    //   valueEnum: EducationalEnum,
    // },
    // {
    //   title: '父亲职业',
    //   dataIndex: 'fatherProfession',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    // },
    // {
    //   title: '父亲工作单位',
    //   dataIndex: 'fatherWorkspace',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    // },

    // {
    //   title: '母亲姓名',
    //   dataIndex: 'motherName',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    // },
    // {
    //   title: '母亲手机号',
    //   dataIndex: 'motherMobile',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    // },
    // {
    //   title: '母亲学历',
    //   dataIndex: 'motherEducational',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    //   valueEnum: EducationalEnum,
    // },
    // {
    //   title: '母亲职业',
    //   dataIndex: 'motherProfession',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    // },
    // {
    //   title: '母亲工作单位',
    //   dataIndex: 'motherWorkspace',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    // },

    {
      title: '备注',
      dataIndex: 'note',
      valueType: 'text',
      width: 120,
      search: false,
    },

    {
      title: '照片',
      dataIndex: 'header',
      valueType: 'text',
      width: 120,
      search: false,
      render: (result: any) => {
        return result?.uri ? <Image src={result?.uri} height={50} /> : null;
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      width: 80,
      initialValue: '1',
      valueEnum: ChildStateEnum,
    },
    timeColumn,
    {
      title: '操作',
      fixed: 'right',
      align: 'center',
      width: 300,
      search: false,
      valueType: 'operation',
      render(_, entity: any) {
        return (
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Button
              type="primary"
              key="delete"
              size="small"
              danger
              onClick={() => {
                Modal.confirm({
                  title: '是否确认删除',
                  onOk() {
                    delChild({ id: entity.id }).then((r) => {
                      if (r.status === 0) {
                        actionRef.current?.reload();
                      }
                    });
                  },
                });
              }}
            >
              删除
            </Button>

            {entity.state === 1 ? (
              <Button
                size="small"
                type="primary"
                key="edit"
                href={`/child/edit/${entity.id}`}
                target="_blank"
              >
                编辑
              </Button>
            ) : null}

            {entity.state === 1 ? (
              <Button
                onClick={() => onLevelSchool(entity)}
                size="small"
                type="primary"
                key="level"
                danger
              >
                离园
              </Button>
            ) : null}
            <Button
              size="small"
              type="link"
              key="detail"
              href={`/child/parent/list/${entity.id}`}
              target="_blank"
            >
              家长列表
            </Button>

            <Button
              size="small"
              type="link"
              key="detail"
              href={`/child/detail/${entity.id}`}
              target="_blank"
            >
              详情
            </Button>
          </div>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <Modal
        title={modalTitle}
        maskClosable={false}
        keyboard={false}
        closable={false}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        loading={isLoading}
        cancelButtonProps={{ style: { display: 'none' } }}
      >
        <p style={{ whiteSpace: 'pre-wrap', height: '300px', overflowY: 'scroll' }}>{modalText}</p>
      </Modal>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        columnsState={{
          defaultValue: {
            note: {
              show: false,
            },
          },
        }}
        toolBarRender={() => [
          <Button
            size="small"
            type="link"
            key="Download"
            target="_blank"
            onClick={handleDownladTemplate}
          >
            <DownloadOutlined /> 下载模板
          </Button>,
          <UploadTextComponent
            key="upload"
            max={10}
            client={client}
            setClient={setClient}
            refresh={getChildList}
            handleMessage={handleMessage}
          />,
          <Button
            size="small"
            type="link"
            key="detail"
            href={`/child/edit/addChild`}
            target="_blank"
          >
            <PlusOutlined /> 新建
          </Button>,
        ]}
        request={getChildList}
        columns={columns}
        scroll={{ x: 1000 }}
      />
    </PageContainer>
  );
};

export default School;
