import AntForms, { FormFields, FormTypes } from '@/components/UseForms';
import {
  addChild,
  fetchChildDetail,
  updateChild,
  getFaceList,
  addFace,
  deleteFace,
} from '@/services/child';
import { useGetEnumOptionsByKey } from '@/services/enums';
import { filterEmptyObject, phoneValidator } from '@/services/utils';
import { PageContainer } from '@ant-design/pro-components';
import { history, useParams } from '@umijs/max';
import { Button, message, Row } from 'antd';
import { useForm } from 'antd/es/form/Form';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import { initData } from './data';

const ChildDetail: React.FC = () => {
  const params = useParams();
  const [subjectId, setSubjectId] = useState(params.id === 'addChild' ? '' : params.id);
  const [faceLength, setFaceLength] = useState(0);
  const [childFormData] = useState(initData);
  const [form] = useForm();
  const [loading, setLoading] = useState(false);
  const [classId, setClassId] = useState(null);

  const [EducationalOptions] = useGetEnumOptionsByKey(['EducationalEnum']);

  // 根据图片地址截取文件名
  const getFileName = (url: string) => {
    return url.substring(url.lastIndexOf('/') + 1);
  };

  useEffect(() => {
    if (subjectId) {
      // 获取详情
      fetchChildDetail(subjectId!).then((res) => {
        form.setFieldsValue({
          ...res.data,
          birthday: dayjs(res.data.birthday),
          entryTime: dayjs(res.data.entryTime),
          headerIds: (res.data.headers || []).map((h: any) => ({ ...h, uid: h.hash })),
          headers: (res.data.headers || []).map((h: any) => ({ ...h, uid: h.hash })),
        });
      });
      // 获取人脸列表
      getFaceList(Number(subjectId)).then((res) => {
        let list = res.data || [];

        list.forEach((item) => (item.isOne = true));
        setFaceLength(list.length);
        form.setFieldsValue({
          faceIds: list.map((h: any, i: number) => ({
            ...h,
            uid: h.faceToken,
            hash: h.faceToken,
            filename: getFileName(h.faceUrl),
            uri: h.faceUrl,
          })),
        });
      });
    }
  }, []);

  const [SexEnum, YesOrNoEnum, EducationalEnum] = useGetEnumOptionsByKey([
    'SexEnum',
    'YesOrNoEnum',
    'EducationalEnum',
  ]);

  const childForms: FormFields = [
    [
      {
        label: '儿童姓名',
        name: 'title',
        formType: FormTypes.input,
        rules: [
          {
            message: '请输入儿童姓名',
            required: true,
          },
        ],
      },
      {
        label: '性别',
        name: 'sex',
        formType: FormTypes.radio,
        rules: [
          {
            message: '请选择性别',
            required: true,
          },
        ],
        options: SexEnum,
      },
    ],
    [
      {
        label: '出生年月',
        name: 'birthday',
        rules: [
          {
            message: '请选择出生年月',
            required: true,
          },
        ],
        formType: FormTypes.datepicker,
        format: 'YYYY-MM-DD',
      },
      {
        label: '入园时间',
        name: 'entryTime',
        rules: [
          {
            message: '请选择入园时间',
            required: true,
          },
        ],
        formType: FormTypes.datepicker,
        format: 'YYYY-MM-DD',
      },
    ],
    [
      {
        label: '是否独生子女',
        name: 'isSingle',

        rules: [
          {
            message: '请选择是否独生子女',
            required: true,
          },
        ],
        formType: FormTypes.select,
        options: YesOrNoEnum,
      },
      {
        label: '是否有发展症状',
        name: 'symptoms',
        formType: FormTypes.select,

        rules: [
          {
            message: '请选择是否有发展症状',
            required: true,
          },
        ],
        options: [
          {
            label: '正常儿童',
            value: 1,
          },
          {
            label: '疑似特殊',
            value: 2,
          },
          {
            label: '诊断特殊',
            value: 3,
          },
        ],
      },
    ],
    // [
    //   {
    //     label: '父亲姓名',
    //     name: 'fatherName',
    //     formType: FormTypes.input,
    //   },
    //   {
    //     label: '父亲手机号',
    //     name: 'fatherMobile',
    //     rules: [
    //       {
    //         validator: phoneValidator,
    //       },
    //     ],
    //     formType: FormTypes.input,
    //   },
    // ],
    // [
    //   {
    //     label: '父亲学历',
    //     name: 'fatherEducational',
    //     formType: FormTypes.select,
    //     options: EducationalEnum,
    //   },
    //   {
    //     label: '父亲职业',
    //     name: 'fatherProfession',
    //     formType: FormTypes.input,
    //   },
    // ],
    // [
    //   {
    //     label: '母亲姓名',
    //     name: 'motherName',
    //     formType: FormTypes.input,
    //   },

    //   {
    //     label: '母亲手机号',
    //     name: 'motherMobile',
    //     formType: FormTypes.input,
    //     rules: [
    //       {
    //         validator: phoneValidator,
    //       },
    //     ],
    //   },
    // ],
    // [
    //   {
    //     label: '母亲学历',
    //     name: 'motherEducational',
    //     formType: FormTypes.select,
    //     options: EducationalOptions,
    //   },
    //   {
    //     label: '母亲职业',
    //     name: 'motherProfession',
    //     formType: FormTypes.input,
    //   },
    // ],

    // [
    //   {
    //     label: '父亲工作单位',
    //     name: 'fatherWorkspace',
    //     formType: FormTypes.textarea,
    //   },
    //   {
    //     label: '母亲工作单位',
    //     name: 'motherWorkspace',
    //     formType: FormTypes.textarea,
    //   },
    // ],
    {
      label: '上传头像',
      name: 'headerIds',
      fileKey: 'headers',
      formType: FormTypes.file,
    },
    {
      label: '上传人脸',
      name: 'faceIds',
      fileKey: 'faces',
      formType: FormTypes.file,
      onRemove: async (file) => {
        console.log(file);
        console.log(form.getFieldValue('faceIds'));
        let obj = (form.getFieldValue('faceIds') || []).find((item) => item.uid === file.uid);
        let res = await deleteFace(obj.id);
        if (res.status === 0) {
          return message.success('人脸删除成功');
        }
        message.error('人脸删除失败: ', res?.message);
      },
    },
    {
      label: '备注',
      name: 'note',
      formType: FormTypes.textarea,
    },
  ];

  // 添加人脸
  const addFaceList = async (data) => {
    const res = await addFace(data);
    return res;
  };

  // 循环人脸列表发起请求
  const fetchData = async (list) => {
    let params = list.map((item) => {
      if (!item.isOne) {
        return {
          childId: Number(subjectId),
          classId: Number(form.getFieldValue('classId')),
          resourceId: item.id,
        };
      }
    });
    // 清楚数据位undefined 的一项
    console.log(params, '参数列表');
    const requests = params.map((item: any) => {
      if (item) {
        return addFaceList(item);
      }
    }); // 将所有请求生成数组

    try {
      const responses = await Promise.all(requests); // 并行请求
      console.log(responses, 'responses');
      responses.forEach((item, index) => {
        if (item && item.status === 0) {
          return message.success(`第${index + 1}张成功`);
        }
        if(item !== undefined) message.error(`第${index + 1}张失败: ${item?.message}`);
        
      });
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const onUpdateChild = useCallback(
    (values: any) => {
      const fn = subjectId ? updateChild : addChild;
      setLoading(true);
      // if (values.faceIds.length !== faceLength)
      fn(
        filterEmptyObject({
          ...values,
          withElders: 1,
          id: subjectId,
          headerIds: (values.headerIds || []).map((v: any) => v.id),
          faceIds: (values.faceIds || []).map((v: any) => v.id),
        }),
      )
        .then( async (r) => {
          if (r.status === 0) {
            message.success(childFormData.id ? '更新成功' : '添加成功');
            await fetchData(values.faceIds); // 上传人脸
            if (params.id === 'addChild') {
              setSubjectId(r.data.id);
              history.replace('/child/detail/' + r.data.id);
            }
          }
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [childFormData, subjectId],
  );

  return (
    <PageContainer style={{ background: '#fff', height: '100%' }}>
      <AntForms
        form={form}
        formProps={{
          onFinish: onUpdateChild,
          initialValues: childFormData,
        }}
        forms={childForms}
      >
        <Row justify="end">
          <Button loading={loading} style={{ marginRight: 16 }} htmlType="submit" type="primary">
            确定
          </Button>
          {subjectId === 'addChild' ? (
            <Button onClick={() => form.setFieldsValue({ ...initData })}>重置</Button>
          ) : null}
        </Row>
      </AntForms>
    </PageContainer>
  );
};

export default ChildDetail;
