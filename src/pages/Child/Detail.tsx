import AntForms, { FormFields, FormTypes } from '@/components/UseForms';
import { fetchChildDetail } from '@/services/child';
import { phoneValidator } from '@/services/utils';
import { PageContainer } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';

import { useGetEnumOptionsByKey } from '@/services/enums';
import { useForm } from 'antd/es/form/Form';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { initData } from './data';

const ChildDetail: React.FC = () => {
  const params = useParams();
  const [subjectId] = useState(params.id === 'addChild' ? '' : params.id);

  const [childFormData] = useState(initData);
  const [form] = useForm();

  const [yesOrNoOptions, EducationalOptions] = useGetEnumOptionsByKey([
    'YesOrNoEnum',
    'EducationalEnum',
  ]);

  useEffect(() => {
    if (subjectId) {
      // 获取详情
      fetchChildDetail(subjectId!).then((res) => {
        form.setFieldsValue({
          ...res.data,
          birthday: dayjs(res.data.birthday),
          entryTime: dayjs(res.data.entryTime),
          headerIds: res.data.headers,
        });
      });
    }
  }, []);

  const childForms: FormFields = [
    [
      {
        label: '儿童姓名',
        name: 'title',
        formType: FormTypes.input,
        rules: [
          {
            message: '请输入儿童姓名',
            required: true,
          },
        ],
      },
      {
        label: '性别',
        name: 'sex',
        formType: FormTypes.radio,
        rules: [
          {
            message: '请选择性别',
            required: true,
          },
        ],
        options: [
          { label: '男', value: 1 },
          { label: '女', value: 2 },
        ],
      },
    ],
    [
      {
        label: '出生年月',
        name: 'birthday',
        rules: [
          {
            message: '请选择出生年月',
            required: true,
          },
        ],
        formType: FormTypes.datepicker,
        format: 'YYYY-MM-DD',
      },
      {
        label: '入园时间',
        name: 'entryTime',
        rules: [
          {
            message: '请选择入园时间',
            required: true,
          },
        ],
        formType: FormTypes.datepicker,
        format: 'YYYY-MM-DD',
      },
    ],
    [
      {
        label: '是否独生子女',
        name: 'isSingle',

        rules: [
          {
            message: '请选择是否独生子女',
            required: true,
          },
        ],
        formType: FormTypes.select,
        options: yesOrNoOptions,
      },
      {
        label: '是否有发展症状',
        name: 'symptoms',
        formType: FormTypes.select,

        rules: [
          {
            message: '请选择是否有发展症状',
            required: true,
          },
        ],
        options: [
          {
            label: '正常儿童',
            value: 1,
          },
          {
            label: '疑似特殊',
            value: 2,
          },
          {
            label: '诊断特殊',
            value: 3,
          },
        ],
      },
    ],
    // [
    //   {
    //     label: '父亲姓名',
    //     name: 'fatherName',
    //     formType: FormTypes.input,
    //   },
    //   {
    //     label: '父亲手机号',
    //     name: 'fatherMobile',
    //     rules: [
    //       {
    //         validator: phoneValidator,
    //       },
    //     ],
    //     formType: FormTypes.input,
    //   },
    // ],
    // [
    //   {
    //     label: '父亲学历',
    //     name: 'fatherEducational',
    //     formType: FormTypes.select,
    //     options: EducationalOptions,
    //   },
    //   {
    //     label: '父亲职业',
    //     name: 'fatherProfession',
    //     formType: FormTypes.input,
    //   },
    // ],
    // [
    //   {
    //     label: '母亲姓名',
    //     name: 'motherName',
    //     formType: FormTypes.input,
    //   },

    //   {
    //     label: '母亲手机号',
    //     name: 'motherMobile',
    //     formType: FormTypes.input,
    //     rules: [
    //       {
    //         validator: phoneValidator,
    //       },
    //     ],
    //   },
    // ],
    // [
    //   {
    //     label: '母亲学历',
    //     name: 'motherEducational',
    //     formType: FormTypes.select,
    //     options: EducationalOptions,
    //   },
    //   {
    //     label: '母亲职业',
    //     name: 'motherProfession',
    //     formType: FormTypes.input,
    //   },
    // ],

    // [
    //   {
    //     label: '父亲工作单位',
    //     name: 'fatherWorkspace',
    //     formType: FormTypes.textarea,
    //   },
    //   {
    //     label: '母亲工作单位',
    //     name: 'motherWorkspace',
    //     formType: FormTypes.textarea,
    //   },
    // ],
    {
      label: '照片',
      name: 'headerIds',
      fileKey: 'headers',
      formType: FormTypes.file,
    },
    {
      label: '备注',
      name: 'note',
      formType: FormTypes.textarea,
    },
  ];

  return (
    <PageContainer style={{ background: '#fff', height: '100%' }}>
      <AntForms
        form={form}
        formProps={{
          disabled: true,
          initialValues: childFormData,
        }}
        forms={childForms}
      ></AntForms>
    </PageContainer>
  );
};

export default ChildDetail;
