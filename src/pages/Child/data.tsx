import dayjs from 'dayjs';

export interface ChildEntity {
  title: string;
  sex: number;
  birthday: dayjs.Dayjs | null;
  entryTime: dayjs.Dayjs | null;
  symptoms: number;
  isSingle: number;
  fatherName: string;
  fatherMobile: string;
  fatherEducational: number | null;
  fatherProfession: string;
  fatherWorkspace: string;
  motherName: string;
  motherMobile: string;
  motherEducational: number | null;
  motherProfession: string;
  motherWorkspace: string;
  note: string;
  id?: number;
  score?: number;
}

export const initData: ChildEntity = {
  title: '',
  sex: 1,
  symptoms: 1,
  birthday: null,
  entryTime: null,
  isSingle: 1,
  fatherName: '',
  fatherMobile: '',
  fatherEducational: 7,
  motherName: '',
  motherMobile: '',
  motherEducational: 7,
  fatherProfession: '',
  motherProfession: '',
  fatherWorkspace: '',
  motherWorkspace: '',
  note: '',
};
