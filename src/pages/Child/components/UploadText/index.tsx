/*
 * @Description:文件上传
 * @Author: SaraSL
 * @Date: 2024-02-26 13:25:01
 */

/**
 * 文件上传逻辑
 *
 * 1、前端上传文件组件，拿到文件对象后，使用hash算法，算得文件的 Hash 值，然后2
 * 2、前端请求后端 /admin/resource/hash 接口，查询该 Hash 值是否已有对应文件实体；有则6，无则3
 * 3、前端请求后端 /admin/resource/auth 接口，获取上传oss使用的auth信息；此步骤可在页面加载时直接调用，然后4
 * 4、前端从后端获取 auth 数据后，调用 ali-oss SDK中 简单上传 方法，上传文件到 阿里云OSS，然后5
 * 5、前端获取 oss 数据实体，请求后端 /admin/resource/sync 接口，将 oss 实体数据存储到 resource 实体，然后6
 * 6、前端获取 resource 数据实体，请求后端 /admin/resource/bind 接口，将 resource 实体数据绑定到对应业务实体，END
 */

import React, { useEffect } from 'react';

import { fetchClient, handleUpload } from '@/services/fileUpload';
// import { isImageFile } from '@/services/utils';
import { FileOutlined } from '@ant-design/icons';
// import { ProFormUploadButton } from '@ant-design/pro-form';
import { Upload, Button, message, Tooltip } from 'antd';
// import Player from '../Player';

/**
 * ===== 上传图片组件
 * @see [阿里云OSS简单上传] (https://help.aliyun.com/document_detail/383950.html)
 * @see [antd上传图片到阿里云的oss服务器] (https://www.jianshu.com/p/7ca4b4fad6eb)
 */
type UploadImageProps = {
  label?: string;
  max?: number;
  client: any;
  setClient: (client: any) => void;
  edit?: boolean;
  accept?: string;
  refresh?: Function;
  showUploadList?: boolean;
  handleMessage?: Function;
};
const UploadImageComponent: React.FC<UploadImageProps> = (props) => {
  const {
    max = 1,
    client,
    setClient,
    accept = 'file',
    showUploadList = false,
    refresh,
    handleMessage,
  } = props;

  const title = '请先下载模板，须严格按照模板内容填写';

  useEffect(() => {
    fetchClient(setClient);
  }, []);

  const handleSave = async (file: any) => {
    if (!client) {
      await fetchClient(setClient);
    }
    const resource = await handleUpload(client, file);
    // pushFileList(resource);
    return resource;
  };

  const customRequest = async (option: any) => {
    const { onSuccess, onError } = option;
    try {
      const res = await handleSave(option.file);
      // message.success('上传成功，请稍等1分钟后刷新查看!');
      onSuccess(res.uri);
      if (handleMessage) handleMessage(res.uri);
      if (refresh) refresh();
    } catch (e) {
      message.error('上传失败!');
      onError(e || '上传失败');
    }
  };

  return (
    <>
      <Tooltip title={title}>
        <Upload
          customRequest={customRequest}
          maxCount={max}
          accept={accept}
          showUploadList={showUploadList}
        >
          <Button size="small" type="link" icon={<FileOutlined />}>
            批量上传
          </Button>
        </Upload>
      </Tooltip>
    </>
  );
};

export default UploadImageComponent;
