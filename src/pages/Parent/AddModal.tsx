import { PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProForm,
  ProFormSelect,
  ProFormText,
  ProFormDigit,
} from '@ant-design/pro-components';
import { Button, Form, message } from 'antd';
import { useModel } from '@umijs/max';
import { addParent, getParentDetail, updateParentDetail } from '@/services/api';
export default (props: {
  id?: string;
  isDetail?: boolean;
  setOpen: (open: boolean) => void;
  data?: Record<string, any>;
  title?: string;
  isOpen?: boolean;
  actionRef?: any;
}) => {
  const { isDetail, id, data, isOpen, setOpen, actionRef } = props;
  const [form] = Form.useForm();
  const { initialState } = useModel('@@initialState');

  let EducationalEnum = initialState?.enums?.EducationalEnum || {};

  const getButtonText = () => {
    if (isDetail) {
      return `编辑家长`;
    }
    return `新增家长`;
  };
  const isSex = (i: number) => {
    if (i === 11) return 2;
    if (i === 12) return 1;
    return i % 2 === 0 ? 2 : 1;
  };
  return (
    <ModalForm
      title={getButtonText()}
      trigger={
        <Button type="primary">
          <PlusOutlined />
          新增家长
        </Button>
      }
      form={form}
      open={isOpen}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: () => setOpen(false),
        afterOpenChange: async (open) => {
          if (open) {
            console.log('%c Line:73 🥑 data', 'color:#f5ce50', data);
            const params = {
              parentId: data?.id,
              childId: Number(id),
            };
            if (!isDetail) {
              form.setFieldsValue({});
              return;
            }
            let res = await getParentDetail(params);
            if (res.status === 0) {
              let data = res.data;
              console.log(data.education, '111111111111111111111');
              data.relationship = Number(data.relationship);
              data.education = Number(data.education);
              form.setFieldsValue({
                ...(data || {}),
              });
            }
          } else {
            form.setFieldsValue({});
          }
        },
      }}
      submitTimeout={2000}
      onFinish={async (values) => {
        let params = {
          childId: id,
          ...values,
        };
        if (isDetail) params.id = data?.id;
        const fn = isDetail ? updateParentDetail : addParent;
        let res = await fn(params);
        console.log('res:', res);

        if (res.status === 0) {
          let text = isDetail ? '修改成功' : '新增成功';
          message.success(text);
          setOpen(false);
          actionRef.current?.reload();
          return true;
        }
        message.error('提交失败');
      }}
    >
      <ProForm.Group>
        <ProFormText
          width="md"
          name="name"
          label="家长姓名"
          rules={[{ required: true }]}
          placeholder="请输入名称"
        />
        <ProFormSelect
          width="md"
          name="relationship"
          label="关系"
          rules={[{ required: true }]}
          placeholder="请输入关系"
          request={async () => [
            { label: '父亲', value: 1 },
            { label: '母亲', value: 2 },
            { label: '爷爷', value: 3 },
            { label: '奶奶', value: 4 },
            { label: '外公', value: 5 },
            { label: '外婆', value: 6 },
            { label: '伯父', value: 7 },
            { label: '伯母', value: 8 },
            { label: '叔叔', value: 9 },
            { label: '婶婶', value: 10 },
            { label: '姑姑', value: 11 },
            { label: '姑父', value: 12 },
            { label: '舅舅', value: 13 },
            { label: '舅妈', value: 14 },
          ]}
          onChange={(values) => {
            console.log(values);
            form.setFieldsValue({ gender: isSex(Number(values)) });
          }}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormSelect
          request={async () => [
            {
              value: 1,
              label: '男',
            },
            {
              value: 2,
              label: '女',
            },
          ]}
          width="md"
          name="gender"
          label="性别"
          rules={[{ required: true }]}
          placeholder="请选择性别"
        />
        <ProFormDigit
          width="md"
          name="phone"
          label="手机号"
          rules={[{ required: true }, { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }]}
        />
      </ProForm.Group>
      <ProFormSelect
        name="education"
        label="教育程度"
        request={async () => {
          return Object.keys(EducationalEnum).map((item: any) => {
            console.log(item);
            return {
              value: Number(item - 1),
              label: EducationalEnum[item],
            };
          });
        }}
      />
      <ProFormText name="occupation" label="职业" />
      <ProFormText name="company" label="公司" />
    </ModalForm>
  );
};
