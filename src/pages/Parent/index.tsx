// 家长列表
/* eslint-disable guard-for-in */

import { getParentList, deleteParentDetail } from '@/services/api';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, message, Popconfirm } from 'antd';
import React, { useRef, useState } from 'react';
import { useParams } from '@umijs/max';
import AddModal from './AddModal';

const parent: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isD, setIsD] = useState(false);
  const [data, setData] = useState({});
  const params = useParams();

  const deleteConfirm = async (record: any) => {
    console.log('删除：', record.id);
    const res = await deleteParentDetail(record.id);
    if (res.status === 0) {
      actionRef.current?.reload();
      message.success('删除成功');
      return;
    }
    message.error('删除失败: ' + res?.message);
  };

  const columns: ProColumns<API.RuleListItem>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '家长姓名',
      dataIndex: 'name',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '与儿童关系',
      dataIndex: 'childRelationshipText',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '学历',
      dataIndex: 'educationText',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '职业',
      dataIndex: 'company',
      valueType: 'text',
      width: 400,
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: '16em',
      search: false,
      disable: true,
      fixed: 'right',
      render: (_, record) => {
        return [
          <Button
            size="small"
            type="link"
            key="edit"
            target="_blank"
            onClick={() => {
              setData(record);
              setIsD(true);
              setIsModalOpen(true);
            }}
          >
            编辑
          </Button>,
          <Popconfirm
            key="delete"
            title="您确定删除吗？"
            onConfirm={() => deleteConfirm(record)}
            onCancel={() => {}}
            okText="确定"
            cancelText="取消"
          >
            <Button size="small" type="link" danger target="_blank">
              删除
            </Button>
          </Popconfirm>,
        ];
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={false}
        columnsState={{
          defaultValue: {
            note: {
              show: false,
            },
          },
        }}
        toolBarRender={() => [
          <AddModal
            aref={actionRef}
            actionRef={actionRef}
            setOpen={setIsModalOpen}
            key={'add'}
            id={params?.id}
            isDetail={isD}
            isOpen={isModalOpen}
            data={data}
          />,
        ]}
        request={async () => {
          const res = await getParentList(Number(params?.id));
          return {
            data: res.data,
            success: true,
          };
        }}
        columns={columns}
      />
    </PageContainer>
  );
};

export default parent;
