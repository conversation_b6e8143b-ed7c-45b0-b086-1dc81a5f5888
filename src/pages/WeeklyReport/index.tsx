/* eslint-disable guard-for-in */

import { getWookly<PERSON><PERSON><PERSON>List, getListWeek } from '@/services/api';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, Popover, message } from 'antd';
import React, { useRef, useState, useEffect } from 'react';
import { useModel } from '@umijs/max';
import dayjs from 'dayjs';
const WeeklyReport = () => {
  //   const actionRef = useRef<ActionType>();
  const { initialState } = useModel('@@initialState');
  // 通过dayjs获取本周和上周的日期 格式为 2020-01-01 - 2020-01-07
  let dayJs = dayjs();
  const getWeekDate = (b: boolean = false) => {
    // b 为true时获取本周日期，为false时获取上周日期
    const startOfWeek = dayJs.startOf('week').format('YYYY-MM-DD');
    const endOfWeek = dayJs.endOf('week').format('YYYY-MM-DD');
    const startOfLastWeek = dayJs.subtract(1, 'week').startOf('week').format('YYYY-MM-DD');
    const endOfLastWeek = dayJs.subtract(1, 'week').endOf('week').format('YYYY-MM-DD');
    return b ? `${startOfWeek} - ${endOfWeek}` : `${startOfLastWeek} - ${endOfLastWeek}`;
  };

  console.log('日期：', getWeekDate(true));
  // 新增配置项
  const columns: ProColumns<API.RuleListItem>[] = [
    {
      title: '班级',
      dataIndex: 'className',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '班级圈',
      dataIndex: 'postCount',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '观察记录',
      dataIndex: 'orCount',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '活动教案',
      dataIndex: 'seCount',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '活动评价',
      dataIndex: 'saCount',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '幼儿进区',
      dataIndex: 'caeCount',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '一对一倾听',
      dataIndex: 'o2oCount',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '周期',
      search: true,
      hidden: true,
      dataIndex: 'week',
      valueType: 'select',
      fieldProps: { placeholder: '请选择周期' },
      initialValue: getWeekDate(), // 默认选中上周
      filterResetToDefaultFilteredValue: true,
      request: async () => {
        let res = await getListWeek();
        if (res.status === 0) {
          const result = res?.data?.map((item: any) => {
            return { label: item, value: item };
          });
          return result || [];
        }
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        // 工具栏 默认谁关闭
        // columnsState={{
        //   defaultValue: {
        //     note: {
        //       show: false,
        //     },
        //   },
        // }}
        request={async (values) => {
          let { currentSchoolId } = initialState?.currentUser || {};
          console.log('表格的日期：', getWeekDate(false));

          let params = {
            schoolId: currentSchoolId,
            week: values?.week || getWeekDate(false),
          };
          let res = await getWooklyReportList(params);
          if (res.status === 0) {
            return {
              data: res.data,
              success: true,
            };
          }
        }}
        columns={columns}
        // scroll={{ x: 1000 }}
      />
    </PageContainer>
  );
};

export default WeeklyReport;
