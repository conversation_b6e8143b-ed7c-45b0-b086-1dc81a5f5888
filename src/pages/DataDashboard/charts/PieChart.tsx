import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

interface PieChartProps {
  data: {
    name: string;
    value: number;
  }[];
  title?: string;
  roseType?: 'radius' | 'area' | false;
  height?: string | number;
  width?: string | number;
  isDarkMode?: boolean;
}

const PieChart: React.FC<PieChartProps> = ({
  data,
  title,
  roseType = false,
  height = '100%',
  width = '100%',
  isDarkMode = false,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current, null, { renderer: 'svg' });

    const colors = ['#ff85c0', '#ffadd2', '#f7c6d7', '#fde0e9', '#fff0f6'];

    const option: echarts.EChartsOption = {
      color: colors,
      title: title
        ? {
            text: title,
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: isDarkMode ? '#E9E9EB' : '#333',
            },
            left: 'center',
            top: 10,
          }
        : undefined,
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
        position: function (point: any, _params: any, _dom: any, _rect: any, size: any) {
          // 获取容器大小
          const containerWidth = size.viewSize[0];
          const containerHeight = size.viewSize[1];
          const tooltipWidth = size.contentSize[0];
          const tooltipHeight = size.contentSize[1];

          // 计算位置
          let x = point[0];
          let y = point[1];

          // 防止右侧超出
          if (x + tooltipWidth > containerWidth) {
            x = containerWidth - tooltipWidth - 10;
          }

          // 防止底部超出
          if (y + tooltipHeight > containerHeight) {
            y = y - tooltipHeight - 10;
          }

          // 防止左侧超出
          if (x < 0) {
            x = 10;
          }

          // 防止顶部超出
          if (y < 0) {
            y = 10;
          }

          return [x, y];
        },
        backgroundColor: isDarkMode ? '#3A3B42' : '#fff',
        borderColor: isDarkMode ? '#3A3B42' : '#e4e7ed',
        borderWidth: 1,
        textStyle: {
          color: isDarkMode ? '#E9E9EB' : '#333',
          fontSize: 12,
        },
        extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px;',
      },
      legend: {
        show: false,
      },
      series: [
        {
          name: title || '数据',
          type: 'pie',
          radius: roseType ? ['20%', '70%'] : ['40%', '70%'],
          center: ['50%', '50%'],
          roseType: roseType || undefined,
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          itemStyle: {
            borderRadius: 2,
            borderColor: '#fff',
            borderWidth: 1,
            shadowColor: 'rgba(255, 133, 192, 0.5)',
            shadowBlur: 5,
          },
          label: {
            show: true,
            fontSize: 10,
            color: isDarkMode ? '#BBBDC1' : '#666',
            formatter: '{b}: {c}\n({d}%)',
            fontWeight: 'normal',
          },
          labelLine: {
            show: true,
            length: 8,
            length2: 8,
            lineStyle: {
              color: isDarkMode ? '#BBBDC1' : '#666',
            },
          },
        },
      ],
      animation: true,
      animationEasing: 'elasticOut',
      animationDelayUpdate: (idx: number) => idx * 5,
    };

    chartInstance.current.setOption(option);

    // 自适应大小
    const handleResize = () => {
      chartInstance.current?.resize({
        animation: {
          duration: 300,
          easing: 'cubicInOut',
        },
      });
    };

    const debouncedResize = () => {
      handleResize();
    };

    window.addEventListener('resize', debouncedResize);

    return () => {
      window.removeEventListener('resize', debouncedResize);
      chartInstance.current?.dispose();
    };
  }, [data, title, roseType, isDarkMode]);

  // 当数据变化时更新图表
  useEffect(() => {
    if (chartInstance.current) {
      const option: echarts.EChartsOption = {
        series: [
          {
            data: data,
          },
        ],
      };
      chartInstance.current.setOption(option, { notMerge: false, lazyUpdate: true });
    }
  }, [data]);

  return (
    <div
      ref={chartRef}
      style={{
        width: typeof width === 'number' ? `${width}px` : width,
        height: typeof height === 'number' ? `${height}px` : height,
      }}
    />
  );
};

export default PieChart;
