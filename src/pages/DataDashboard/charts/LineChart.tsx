import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

interface LineChartProps {
  data: {
    categories: string[];
    series: {
      name: string;
      data: number[];
      color?: string;
    }[];
  };
  title?: string;
  height?: string | number;
  width?: string | number;
  smooth?: boolean;
  area?: boolean;
  isDarkMode?: boolean;
}

const LineChart: React.FC<LineChartProps> = ({
  data,
  title,
  height = '100%',
  width = '100%',
  smooth = true,
  area = true,
  isDarkMode = false,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current, null, { renderer: 'svg' });

    const option: echarts.EChartsOption = {
      title: title
        ? {
            text: title,
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#333',
            },
            left: 'center',
            top: 10,
          }
        : undefined,
      grid: {
        left: '3%',
        right: '3%',
        top: title ? '25%' : '15%',
        bottom: '5%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: data.categories,
        axisLabel: {
          fontSize: 10,
          color: '#666',
          rotate: 45,
        },
        axisLine: {
          lineStyle: {
            color: isDarkMode ? '#BBBDC1' : '#767779',
          },
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 10,
          color: isDarkMode ? '#E9E9EB' : '#666',
        },
        axisLine: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: isDarkMode ? '#767779' : '#f0f0f0',
          },
        },
      },
      series: data.series.map((item) => ({
        name: item.name,
        type: 'line',
        data: item.data,
        smooth: smooth,
        areaStyle: area
          ? {
              opacity: 0.3,
            }
          : undefined,
        lineStyle: {
          color: item.color || '#3b82f6',
          width: 2,
        },
        itemStyle: {
          color: item.color || '#3b82f6',
        },
        symbol: 'circle',
        symbolSize: 4,
        label: {
          show: true,
          position: 'top',
          fontSize: 10,
          color: isDarkMode ? '#E9E9EB' : '#333',
          fontWeight: 'normal',
        },
      })),
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
        },
        position: function (point: any, _params: any, _dom: any, _rect: any, size: any) {
          // 获取容器大小
          const containerWidth = size.viewSize[0];
          const containerHeight = size.viewSize[1];
          const tooltipWidth = size.contentSize[0];
          const tooltipHeight = size.contentSize[1];

          // 计算位置
          let x = point[0];
          let y = point[1];

          // 防止右侧超出
          if (x + tooltipWidth > containerWidth) {
            x = containerWidth - tooltipWidth - 10;
          }

          // 防止底部超出
          if (y + tooltipHeight > containerHeight) {
            y = y - tooltipHeight - 10;
          }

          // 防止左侧超出
          if (x < 0) {
            x = 10;
          }

          // 防止顶部超出
          if (y < 0) {
            y = 10;
          }

          return [x, y];
        },
        backgroundColor: isDarkMode ? '#3A3B42' : '#fff',
        borderColor: isDarkMode ? '#3A3B42' : '#e4e7ed',
        borderWidth: 1,
        textStyle: {
          color: isDarkMode ? '#E9E9EB' : '#333',
          fontSize: 12,
        },
        extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px;',
      },
      animation: true,
      animationEasing: 'elasticOut',
      animationDelayUpdate: (idx: number) => idx * 5,
    };

    chartInstance.current.setOption(option);

    // 自适应大小
    const handleResize = () => {
      chartInstance.current?.resize({
        animation: {
          duration: 300,
          easing: 'cubicInOut',
        },
      });
    };

    const debouncedResize = () => {
      handleResize();
    };

    window.addEventListener('resize', debouncedResize);

    return () => {
      window.removeEventListener('resize', debouncedResize);
      chartInstance.current?.dispose();
    };
  }, [data, title, smooth, area, isDarkMode]);

  // 当数据变化时更新图表
  useEffect(() => {
    if (chartInstance.current) {
      const option: echarts.EChartsOption = {
        xAxis: {
          data: data.categories,
        },
        series: data.series.map((item) => ({
          data: item.data,
        })),
      };
      chartInstance.current.setOption(option, { notMerge: false, lazyUpdate: true });
    }
  }, [data]);

  return (
    <div
      ref={chartRef}
      style={{
        width: typeof width === 'number' ? `${width}px` : width,
        height: typeof height === 'number' ? `${height}px` : height,
      }}
    />
  );
};

export default LineChart;
