.ant-pro-layout .ant-pro-layout-content {
  padding: 10px;
}
// 数据大屏样式文件
.data-dashboard {
  // width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  // 背景现在由 Tailwind CSS 控制
  &.normal-mode {
    color: #333333;
  }
  &.fullscreen-mode {
    width: 100vw;
    color: #ffffff;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
}

// 顶部工具栏（非全屏模式）
.dashboard-header {
  position: absolute;
  top: 10px;
  left: 0;
  right: 5px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  z-index: 100;
  .header-actions {
    display: flex;
    gap: 12px;
  }
}

// 全屏模式工具栏
.fullscreen-toolbar {
  position: fixed;
  top: 10px;
  right: 10px;
  z-index: 10000;
  display: flex;
  align-items: center;
  gap: 12px;
}

// 主要内容区域
.dashboard-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
  &.normal-mode {
    padding: 15px;
  }
  &.fullscreen-mode {
    padding: 2vh 2vw;
  }
}

// 标题区域已删除

// 内容展示区域 - 三列布局
.dashboard-chart-area {
  width: 100%;
  height: 100%;
  display: flex;
  gap: 15px;
  transition: all 0.3s ease;
  flex: 1 1 auto;
  overflow: hidden;
  box-sizing: border-box;
}
// 重置 ant design 的一些默认样式
.ant-btn {
  border-radius: 6px;
}
.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;

  &:hover,
  &:focus {
    background: #40a9ff;
    border-color: #40a9ff;
  }
}
.ant-statistic {
  .ant-statistic-content {
    font-size: 16px;
    font-weight: 600;
  }
}
// 无缝滚动动画
@keyframes seamlessScroll {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}
// 全局无缝滚动样式
.seamless-scroll-content {
  animation: seamlessScroll 20s linear infinite;

  &:hover {
    animation-play-state: paused;
  }
}
