import React, { useRef, useEffect, useState } from 'react';

interface SickLeaveMarqueeProps {
  sickLeaveDetails: string[];
  isDarkMode?: boolean;
}

const SickLeaveMarquee: React.FC<SickLeaveMarqueeProps> = ({
  sickLeaveDetails,
  isDarkMode = false,
}) => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [isScrolling, setIsScrolling] = useState(false);

  useEffect(() => {
    if (sickLeaveDetails.length < 2) return;

    setIsScrolling(true);
    const scrollElement = scrollRef.current;
    if (!scrollElement) return;

    let scrollTop = 0;
    const itemHeight = 20; // 每项的高度
    const totalHeight = sickLeaveDetails.length * itemHeight;
    const speed = 0.5; // 滚动速度

    const scroll = () => {
      scrollTop += speed;

      // 当滚动到一半时重置，实现无缝循环
      if (scrollTop >= totalHeight) {
        scrollTop = 0;
      }

      scrollElement.scrollTop = scrollTop;
    };

    const interval = setInterval(scroll, 50);

    return () => {
      clearInterval(interval);
      setIsScrolling(false);
    };
  }, [sickLeaveDetails]);

  if (sickLeaveDetails.length === 0) {
    return <span className={`${isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'}`}>无</span>;
  }

  if (sickLeaveDetails.length === 1) {
    return (
      <span className={`${isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'}`}>
        {sickLeaveDetails[0]}
      </span>
    );
  }

  return (
    <div className="flex-1 overflow-hidden">
      <div
        ref={scrollRef}
        className="h-5 overflow-hidden scrollbar-hide"
        style={{
          scrollBehavior: 'auto',
        }}
      >
        {/* 原始数据 */}
        {sickLeaveDetails.map((detail, index) => (
          <div
            key={index}
            className={`${isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'} leading-5`}
            style={{ height: '20px', lineHeight: '20px' }}
          >
            {detail}
          </div>
        ))}

        {/* 复制数据用于无缝滚动 */}
        {sickLeaveDetails.map((detail, index) => (
          <div
            key={`copy-${index}`}
            className={`${isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'} leading-5`}
            style={{ height: '20px', lineHeight: '20px' }}
          >
            {detail}
          </div>
        ))}
      </div>
    </div>
  );
};

export default SickLeaveMarquee;
