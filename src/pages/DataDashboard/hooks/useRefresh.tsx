import React, { createContext, useContext, useCallback, useRef } from 'react';

type RefreshFunction = () => Promise<void> | void;

interface RefreshContextType {
  registerRefresh: (id: string, refreshFn: RefreshFunction) => void;
  unregisterRefresh: (id: string) => void;
  refreshAll: () => Promise<void>;
}

const RefreshContext = createContext<RefreshContextType | null>(null);

export const RefreshProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const refreshFunctions = useRef<Map<string, RefreshFunction>>(new Map());

  // 使用 useCallback 确保函数引用稳定，避免无限循环
  const registerRefresh = useCallback((id: string, refreshFn: RefreshFunction) => {
    refreshFunctions.current.set(id, refreshFn);
  }, []);

  const unregisterRefresh = useCallback((id: string) => {
    refreshFunctions.current.delete(id);
  }, []);

  const refreshAll = useCallback(async () => {
    console.log('开始刷新所有数据，注册的刷新函数数量:', refreshFunctions.current.size);
    
    const promises: Promise<void>[] = [];
    
    refreshFunctions.current.forEach((refreshFn, id) => {
      console.log('执行刷新函数:', id);
      try {
        const result = refreshFn();
        if (result instanceof Promise) {
          promises.push(result);
        }
      } catch (error) {
        console.error(`执行刷新函数 ${id} 失败:`, error);
      }
    });

    if (promises.length > 0) {
      await Promise.all(promises);
    }
    
    console.log('所有数据刷新完成');
  }, []);

  const contextValue = useRef<RefreshContextType>({
    registerRefresh,
    unregisterRefresh,
    refreshAll,
  });

  // 更新 ref 中的函数，但保持对象引用稳定
  contextValue.current.registerRefresh = registerRefresh;
  contextValue.current.unregisterRefresh = unregisterRefresh;
  contextValue.current.refreshAll = refreshAll;

  return (
    <RefreshContext.Provider value={contextValue.current}>
      {children}
    </RefreshContext.Provider>
  );
};

export const useRefresh = () => {
  const context = useContext(RefreshContext);
  if (!context) {
    throw new Error('useRefresh must be used within RefreshProvider');
  }
  return context;
};
