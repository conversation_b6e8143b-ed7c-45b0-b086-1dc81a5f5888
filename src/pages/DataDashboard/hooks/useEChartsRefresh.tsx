import { useEffect, useRef, useCallback } from 'react';
import * as echarts from 'echarts';

/**
 * ECharts 图表刷新优化 Hook
 * 解决数据刷新时图表渲染问题
 */
export const useEChartsRefresh = () => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const isInitialized = useRef<boolean>(false);

  // 初始化图表
  const initChart = useCallback((theme?: string | object) => {
    if (!chartRef.current || isInitialized.current) return;

    try {
      // 如果已存在实例，先销毁
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }

      chartInstance.current = echarts.init(chartRef.current, theme, { 
        renderer: 'svg',
        useDirtyRect: true // 启用脏矩形优化
      });
      
      isInitialized.current = true;
      console.log('ECharts 图表初始化成功');
    } catch (error) {
      console.error('ECharts 图表初始化失败:', error);
    }
  }, []);

  // 更新图表配置
  const updateChart = useCallback((option: echarts.EChartsOption, notMerge = false) => {
    if (!chartInstance.current) {
      console.warn('图表实例未初始化，无法更新配置');
      return;
    }

    try {
      chartInstance.current.setOption(option, {
        notMerge,
        lazyUpdate: true, // 启用懒更新
        silent: false
      });
    } catch (error) {
      console.error('更新图表配置失败:', error);
    }
  }, []);

  // 调整图表大小
  const resizeChart = useCallback(() => {
    if (chartInstance.current) {
      try {
        chartInstance.current.resize({
          animation: {
            duration: 300,
            easing: 'cubicInOut',
          },
        });
      } catch (error) {
        console.error('调整图表大小失败:', error);
      }
    }
  }, []);

  // 销毁图表
  const disposeChart = useCallback(() => {
    if (chartInstance.current) {
      try {
        chartInstance.current.dispose();
        chartInstance.current = null;
        isInitialized.current = false;
        console.log('ECharts 图表已销毁');
      } catch (error) {
        console.error('销毁图表失败:', error);
      }
    }
  }, []);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      resizeChart();
    };

    // 防抖处理
    let resizeTimer: NodeJS.Timeout;
    const debouncedResize = () => {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(handleResize, 100);
    };

    window.addEventListener('resize', debouncedResize);

    return () => {
      window.removeEventListener('resize', debouncedResize);
      clearTimeout(resizeTimer);
    };
  }, [resizeChart]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      disposeChart();
    };
  }, [disposeChart]);

  return {
    chartRef,
    chartInstance: chartInstance.current,
    initChart,
    updateChart,
    resizeChart,
    disposeChart,
    isInitialized: isInitialized.current,
  };
};
