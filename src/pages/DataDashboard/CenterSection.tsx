import React, { useState, useCallback } from 'react';
import { Statistic } from 'antd';
import CountUp from 'react-countup';
import <PERSON><PERSON><PERSON> from './charts/BarChart';
import PieChart from './charts/PieChart';
import LineChart from './charts/LineChart';
import ScrollTable from './charts/ScrollTable';
import { getGameActivityStats, getGameActivityStatsByWeek } from '@/services/dataDashboard';
import { useDataFetch } from './hooks/useDataFetch';

// CenterSection 组件属性类型
interface CenterSectionProps {
  isDarkMode: boolean;
}

const CenterSection: React.FC<CenterSectionProps> = ({ isDarkMode }) => {
  // 游戏活动数据状态
  const [gameActivityData, setGameActivityData] = useState({
    observationCount: 0,
    areaEntryDay: 0,
    oneToOneListeningCount: 0,
    areaEntryStats: [],
    schoolAreaEntryStats: [],
    classAreaEntryStats: [],
    childAreaEntryStats: [],
  });

  // 家园共育数据状态
  const [homeSchoolData, setHomeSchoolData] = useState({
    classCircleCount: 0,
    classCircleDays: 0,
    childArchiveCount: 0,
  });

  // 游戏活动周数据状态（观察记录和一对一倾听）
  const [weeklyGameData, setWeeklyGameData] = useState({
    weeklyObservationStats: [],
    weeklyListeningStats: [],
  });

  // 获取游戏活动数据的函数
  const fetchGameActivityData = useCallback(async () => {
    try {
      const response = await getGameActivityStats({});
      console.log('游戏活动数据:', response);
      if (response.status === 0 && response.data) {
        setGameActivityData({
          observationCount: response.data.observationCount || 0,
          areaEntryDay: response.data.areaEntryDay || 0,
          oneToOneListeningCount: response.data.oneToOneListeningCount || 0,
          areaEntryStats: response.data.areaEntryStats || [],
          schoolAreaEntryStats: response.data.schoolAreaEntryStats || [],
          classAreaEntryStats: response.data.classAreaEntryStats || [],
          childAreaEntryStats: response.data.childAreaEntryStats || [],
        });

        // 如果接口返回家园共育数据，也更新这部分
        if (response.data.homeSchoolData) {
          setHomeSchoolData({
            classCircleCount: response.data.homeSchoolData.classCircleCount || 0,
            classCircleDays: response.data.homeSchoolData.classCircleDays || 0,
            childArchiveCount: response.data.homeSchoolData.childArchiveCount || 0,
          });
        }
      }
    } catch (error) {
      console.error('获取游戏活动数据失败:', error);
    }
  }, []);

  // 获取游戏活动周数据的函数
  const fetchWeeklyGameData = useCallback(async () => {
    try {
      const response = await getGameActivityStatsByWeek({});
      console.log('游戏活动周数据:', response);
      if (response.status === 0 && response.data) {
        setWeeklyGameData({
          weeklyObservationStats: response.data.weeklyObservationStats || [],
          weeklyListeningStats: response.data.weeklyListeningStats || [],
        });
      }
    } catch (error) {
      console.error('获取游戏活动周数据失败:', error);
    }
  }, []);

  // 注册数据刷新函数
  useDataFetch('centerSection-gameActivity', fetchGameActivityData);
  useDataFetch('centerSection-weeklyGame', fetchWeeklyGameData);

  // CountUp formatter
  const formatter = (value: any) => <CountUp end={value as number} separator="," />;

  // 游戏活动汇总数据
  const gameActivitySummary = [
    { key: 'areaEntry', label: '幼儿选区总天数', value: gameActivityData.areaEntryDay },
    { key: 'observation', label: '观察记录总篇数', value: gameActivityData.observationCount },
    { key: 'oneOnOne', label: '一对一倾听数量', value: gameActivityData.oneToOneListeningCount },
  ];

  // 处理单个幼儿选区数据
  const processChildAreaData = (childAreaEntryStats: any[]) => {
    if (!childAreaEntryStats || childAreaEntryStats.length === 0) {
      return [];
    }

    // 获取所有区域名称
    const allAreas = new Set<string>();
    childAreaEntryStats.forEach((child) => {
      child.areaEntries?.forEach((area: any) => {
        if (area.areaName) {
          allAreas.add(area.areaName);
        }
      });
    });
    const areaNames = Array.from(allAreas).sort();

    // 转换数据格式
    return childAreaEntryStats.map((child) => {
      const rowData: any = {
        childName: child.childName || '未知',
      };

      // 为每个区域添加数据
      areaNames.forEach((areaName) => {
        const areaEntry = child.areaEntries?.find((area: any) => area.areaName === areaName);
        rowData[areaName] = areaEntry ? areaEntry.count : 0;
      });

      // 计算总次数
      rowData.total =
        child.areaEntries?.reduce((sum: number, area: any) => sum + (area.count || 0), 0) || 0;

      return rowData;
    });
  };

  // 生成表格列配置
  const generateTableColumns = (childAreaEntryStats: any[]) => {
    if (!childAreaEntryStats || childAreaEntryStats.length === 0) {
      return [
        { key: 'childName', title: '儿童姓名' },
        { key: 'total', title: '总次数' },
      ];
    }

    // 获取所有区域名称
    const allAreas = new Set<string>();
    childAreaEntryStats.forEach((child) => {
      child.areaEntries?.forEach((area: any) => {
        if (area.areaName) {
          allAreas.add(area.areaName);
        }
      });
    });
    const areaNames = Array.from(allAreas).sort();

    // 生成列配置
    const columns = [
      { key: 'childName', title: '儿童姓名' },
      ...areaNames.map((areaName) => ({ key: areaName, title: areaName })),
      { key: 'total', title: '总次数' },
    ];

    return columns;
  };

  // 处理后的数据
  const childAreaData = processChildAreaData(gameActivityData.childAreaEntryStats);
  const tableColumns = generateTableColumns(gameActivityData.childAreaEntryStats);

  return (
    <div className="w-[30%] flex flex-col gap-[15px]">
      <div
        className={`h-[85%] p-3 flex flex-col rounded-xl shadow-md overflow-hidden ${
          isDarkMode ? 'bg-[#26272E]' : 'bg-white'
        }`}
      >
        <div
          className={`pb-2 text-xl font-semibold text-left ${
            isDarkMode ? 'text-white' : 'text-[#202020]'
          }`}
        >
          游戏活动
        </div>
        {/* 游戏活动汇总数据 */}
        <div className="pb-2">
          <div className="grid grid-cols-3 gap-5">
            {gameActivitySummary.map((item) => (
              <div key={item.key} className="flex flex-col items-center text-center">
                <Statistic
                  value={item.value}
                  formatter={formatter}
                  valueStyle={{
                    color: isDarkMode ? '#E9E9EB' : '#333333',
                  }}
                />
                <span
                  className={`text-[14px] pt-2   ${
                    isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'
                  }`}
                >
                  {item.label}
                </span>
              </div>
            ))}
          </div>
        </div>
        {/* 单个幼儿选区数据表格 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <ScrollTable
            data={childAreaData}
            columns={tableColumns}
            isDarkMode={isDarkMode}
            title="单个幼儿选区数据"
            height="100%"
            gridTemplateColumns={`1fr ${tableColumns
              .slice(1, -1)
              .map(() => '1fr')
              .join(' ')} 1fr`}
            headerFontSize="text-[11px]"
            cellFontSize="text-[11px]"
            headerPadding="py-1.5 px-1"
            cellPadding="py-1.5 px-1"
          />
        </div>
        {/* 四个图表区域 */}
        <div className="pt-2 grid grid-cols-2 grid-rows-2 gap-3 h-80">
          {/* 各班幼儿选区数据 - 柱状图 */}
          <div className="flex flex-col overflow-hidden min-h-0">
            <div
              className={`text-xs font-semibold pb-1.5 flex-shrink-0 ${
                isDarkMode ? 'text-white' : 'text-[#202020]'
              }`}
            >
              各班幼儿选区数据
            </div>
            <div className="flex-1 min-h-0 w-full h-full">
              <BarChart
                data={{
                  categories: gameActivityData.classAreaEntryStats.map(
                    (item: any) => item.className,
                  ),
                  values: gameActivityData.classAreaEntryStats.map((item: any) => item.totalDays),
                }}
                color="#3b82f6"
                height="100%"
                isDarkMode={isDarkMode}
              />
            </div>
          </div>

          {/* 幼儿选区分组数据 - 玫瑰圆环图 */}
          <div className="flex flex-col overflow-hidden min-h-0">
            <div
              className={`text-xs font-semibold pb-1.5 flex-shrink-0 ${
                isDarkMode ? 'text-white' : 'text-[#202020]'
              }`}
            >
              幼儿选区分组数据
            </div>
            <div className="flex-1 min-h-0 w-full h-full">
              <PieChart
                data={gameActivityData.schoolAreaEntryStats
                  .filter((item: any) => item.areaName) // 过滤掉 areaName 为 null 的数据
                  .map((item: any) => ({
                    name: item.areaName,
                    value: item.count,
                  }))}
                roseType="radius"
                height="100%"
                isDarkMode={isDarkMode}
              />
            </div>
          </div>

          {/* 观察记录变化曲线 - 折线图 */}
          <div className="flex flex-col overflow-hidden min-h-0">
            <div
              className={`text-xs font-semibold pb-1.5 flex-shrink-0 ${
                isDarkMode ? 'text-white' : 'text-[#202020]'
              }`}
            >
              观察记录变化曲线
            </div>
            <div className="flex-1 min-h-0 w-full h-full">
              <LineChart
                data={{
                  categories: weeklyGameData.weeklyObservationStats.map((item: any) => item.week),
                  series: [
                    {
                      name: '观察记录',
                      data: weeklyGameData.weeklyObservationStats.map((item: any) => item.count),
                      color: '#3b82f6',
                    },
                  ],
                }}
                height="100%"
                smooth={true}
                area={true}
                isDarkMode={isDarkMode}
              />
            </div>
          </div>

          {/* 一对一倾听变化曲线 - 折线图 */}
          <div className="flex flex-col overflow-hidden min-h-0">
            <div
              className={`text-xs font-semibold pb-1.5 flex-shrink-0 ${
                isDarkMode ? 'text-white' : 'text-[#202020]'
              }`}
            >
              一对一倾听变化曲线
            </div>
            <div className="flex-1 min-h-0 w-full h-full">
              <LineChart
                data={{
                  categories: weeklyGameData.weeklyListeningStats.map((item: any) => item.week),
                  series: [
                    {
                      name: '一对一倾听',
                      data: weeklyGameData.weeklyListeningStats.map((item: any) => item.count),
                      color: '#10b981',
                    },
                  ],
                }}
                height="100%"
                smooth={true}
                area={true}
                isDarkMode={isDarkMode}
              />
            </div>
          </div>
        </div>
      </div>

      <div
        className={`h-[15%] rounded-[17.6px] shadow-[0px_5.51px_10.55px_rgba(62,73,84,0.04)] flex items-center justify-center ${
          isDarkMode ? 'bg-[#26272E]' : 'bg-white'
        }`}
      >
        <div className="w-full h-full p-3 items-start text-left justify-start flex flex-col">
          <div
            className={`text-xl font-semibold text-left ${
              isDarkMode ? 'text-white' : 'text-[#202020]'
            }`}
          >
            家园共育
          </div>

          <div className="flex-1 w-full grid grid-cols-3 gap-4 items-center">
            <div className="flex flex-col justify-center items-center">
              <Statistic
                value={homeSchoolData.classCircleCount}
                formatter={formatter}
                valueStyle={{
                  color: isDarkMode ? '#E9E9EB' : '#333333',
                }}
              />
              <span
                className={`text-[14px] pt-2   ${isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'}`}
              >
                班级圈总数
              </span>
            </div>
            <div className="flex flex-col justify-center items-center">
              <Statistic
                value={homeSchoolData.classCircleDays}
                formatter={formatter}
                valueStyle={{
                  color: isDarkMode ? '#E9E9EB' : '#333333',
                  fontWeight: 600,
                }}
              />
              <span
                className={`text-[14px] pt-2 ${isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'}`}
              >
                班级圈天数
              </span>
            </div>
            <div className="flex flex-col justify-center items-center">
              <Statistic
                value={homeSchoolData.childArchiveCount}
                formatter={formatter}
                valueStyle={{
                  color: isDarkMode ? '#E9E9EB' : '#333333',
                }}
              />
              <span
                className={`text-[14px] pt-2 ${isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'}`}
              >
                儿童档案总数
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CenterSection;
