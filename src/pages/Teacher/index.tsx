/* eslint-disable guard-for-in */

import { getTeacher<PERSON>ist, getTeacherPhone } from '@/services/api';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, Popover, message } from 'antd';
import React, { useRef, useState } from 'react';
import { useModel } from '@umijs/max';
const School: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [content, setContent] = useState('');
  const { initialState } = useModel('@@initialState');

  // 新增配置项
  const columns: ProColumns<API.RuleListItem>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '教师姓名',
      dataIndex: 'name',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '操作',
      align: 'left',
      width: 120,
      search: false,
      render(_, entity: any) {
        return (
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Popover content={<div>{content}</div>} trigger="click">
              <Button
                type="link"
                onClick={async () => {
                  setContent('loading......');
                  let res = await getTeacherPhone({ id: entity.id });
                  if (res.status === 0) {
                    setContent(res.data.mobile);
                  }
                }}
                key="delete"
                size="small"
              >
                查看老师手机号
              </Button>
            </Popover>
          </div>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={false}
        columnsState={{
          defaultValue: {
            note: {
              show: false,
            },
          },
        }}
        request={async () => {
          console.log(initialState);
          let { currentClassId, currentSchoolId } = initialState?.currentUser || {};
          let params = {
            classId: currentClassId,
            schoolId: currentSchoolId,
          };
          let res = await getTeacherList(params);
          if (res.status === 0) {
            return {
              data: res.data,
              success: true,
            };
          }
        }}
        columns={columns}
        // scroll={{ x: 1000 }}
      />
    </PageContainer>
  );
};

export default School;
