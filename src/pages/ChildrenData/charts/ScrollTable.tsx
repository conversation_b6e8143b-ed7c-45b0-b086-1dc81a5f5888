import React from 'react';

interface ScrollTableColumn {
  key: string;
  title: string;
  width?: string;
}

interface ScrollTableProps {
  data: any[];
  columns: ScrollTableColumn[];
  isDarkMode?: boolean;
  height?: string;
  title?: string;
  gridTemplateColumns?: string;
  headerFontSize?: string;
  cellFontSize?: string;
  headerPadding?: string;
  cellPadding?: string;
}

const ScrollTable: React.FC<ScrollTableProps> = ({
  data,
  columns,
  isDarkMode = false,
  height = 'auto',
  title,
  gridTemplateColumns,
  headerFontSize = 'text-xs',
  cellFontSize = 'text-xs',
  headerPadding = 'py-2 px-1',
  cellPadding = 'py-1.5 px-1',
}) => {
  // 直接计算是否需要滚动，不使用状态
  const shouldScroll = data.length > 5;

  return (
    <div
      className="w-full flex flex-col overflow-hidden min-h-0"
      style={{ height: height !== 'auto' ? height : undefined }}
    >
      {title && (
        <div
          className={`font-semibold pb-2 text-base flex-shrink-0 ${
            isDarkMode ? 'text-white' : 'text-[#202020]'
          }`}
        >
          {title}
        </div>
      )}
      <div
        className={`w-full border rounded-md overflow-hidden flex-1 flex flex-col min-h-0 ${
          isDarkMode ? 'border-[#3A3B42] bg-black/10' : 'border-gray-200 bg-[#EDF0FF]'
        }`}
      >
        {/* 表头 */}
        <div
          className={`grid border-b flex-shrink-0 min-h-[48px] rounded-bl-xl rounded-br-xl ${
            isDarkMode ? 'bg-[#2A2B32] border-[#3A3B42]' : 'bg-white border-gray-200'
          }`}
          style={{ gridTemplateColumns }}
        >
          {columns.map((column, index) => (
            <div
              key={column.key}
              className={`${headerPadding} ${headerFontSize} font-semibold   text-center border-r flex items-center justify-center ${
                isDarkMode ? 'text-[#E9E9EB] border-[#3A3B42]' : 'text-[#333333] border-gray-200'
              } ${index === columns.length - 1 ? 'border-r-0' : ''}`}
            >
              {column.title}
            </div>
          ))}
        </div>

        {/* 表体 */}
        <div
          className={`flex-1 relative min-h-0 ${
            shouldScroll && data.length > 5 ? 'overflow-hidden' : 'overflow-auto'
          }`}
          style={{
            overflowY: shouldScroll && data.length > 5 ? 'hidden' : 'auto',
            maxHeight: shouldScroll && data.length > 5 ? undefined : 'none',
          }}
        >
          <div className={shouldScroll && data.length > 5 ? 'seamless-scroll-content' : ''}>
            {data.map((row, index) => (
              <div
                key={index}
                className={`grid border-b hover:bg-opacity-50 transition-colors duration-200 min-h-[48px] ${
                  isDarkMode
                    ? 'border-[#3A3B42] hover:bg-[#3A3B42]'
                    : 'border-gray-200 hover:bg-gray-100'
                } last:border-b-0`}
                style={{ gridTemplateColumns }}
              >
                {columns.map((column, colIndex) => (
                  <div
                    key={column.key}
                    className={`${cellPadding} ${cellFontSize} text-center border-r flex items-center justify-center ${
                      isDarkMode
                        ? 'text-[#E9E9EB] border-[#3A3B42]'
                        : 'text-[#333333] border-gray-200'
                    } ${colIndex === columns.length - 1 ? 'border-r-0' : ''}`}
                  >
                    {row[column.key]}
                  </div>
                ))}
              </div>
            ))}
            {/* 只有在需要滚动且数据量大于5条时才复制数据 */}
            {shouldScroll &&
              data.length > 5 &&
              data.map((row, index) => (
                <div
                  key={`copy-${index}`}
                  className={`grid border-b hover:bg-opacity-50 transition-colors duration-200 min-h-[48px] ${
                    isDarkMode
                      ? 'border-[#3A3B42] hover:bg-[#3A3B42]'
                      : 'border-gray-200 hover:bg-gray-100'
                  } last:border-b-0`}
                  style={{ gridTemplateColumns }}
                >
                  {columns.map((column, colIndex) => (
                    <div
                      key={column.key}
                      className={`${cellPadding} ${cellFontSize} text-center border-r flex items-center justify-center ${
                        isDarkMode
                          ? 'text-[#E9E9EB] border-[#3A3B42]'
                          : 'text-[#333333] border-gray-200'
                      } ${colIndex === columns.length - 1 ? 'border-r-0' : ''}`}
                    >
                      {row[column.key]}
                    </div>
                  ))}
                </div>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScrollTable;
