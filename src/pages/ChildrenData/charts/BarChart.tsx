import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

interface BarChartProps {
  data: {
    categories: string[];
    values: number[];
  };
  title?: string;
  color?: string;
  height?: string | number;
  width?: string | number;
  isDarkMode?: boolean;
}

const BarChart: React.FC<BarChartProps> = ({
  data,
  title,
  color = '#ff85c0',
  height = '100%',
  width = '100%',
  isDarkMode = false,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current, null, { renderer: 'svg' });

    const option: echarts.EChartsOption = {
      title: title
        ? {
            text: title,
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#333',
            },
            left: 'center',
            top: 10,
          }
        : undefined,
      grid: {
        left: '3%',
        right: '3%',
        top: title ? '20%' : '15%',
        bottom: '5%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: data.categories,
        axisLabel: {
          fontSize: 10,
          color: '#666',
          rotate: 45,
        },
        axisLine: {
          lineStyle: {
            color: isDarkMode ? '#BBBDC1' : '#767779',
          },
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 10,
          color: isDarkMode ? '#E9E9EB' : '#666',
        },
        axisLine: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: isDarkMode ? '#767779' : '#f0f0f0',
          },
        },
      },
      series: [
        {
          type: 'bar',
          data: data.values,
          itemStyle: {
            borderRadius: [2, 2, 0, 0],
            shadowColor: 'rgba(255, 133, 192, 0.5)',
            shadowBlur: 5,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(255, 133, 192, 0.6)',
              },
              {
                offset: 1,
                color: 'rgba(255, 173, 210, 0.6)',
              },
            ]),
          },
          barWidth: '30%',
          label: {
            show: true,
            position: 'top',
            fontSize: 10,
            color: isDarkMode ? '#E9E9EB' : '#333',
            fontWeight: 'normal',
          },
        },
      ],
      animation: true,
      animationEasing: 'elasticOut',
      animationDelayUpdate: (idx: number) => idx * 5,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        position: function (point: any, _params: any, _dom: any, _rect: any, size: any) {
          // 获取容器大小
          const containerWidth = size.viewSize[0];
          const containerHeight = size.viewSize[1];
          const tooltipWidth = size.contentSize[0];
          const tooltipHeight = size.contentSize[1];

          // 计算位置
          let x = point[0];
          let y = point[1];

          // 防止右侧超出
          if (x + tooltipWidth > containerWidth) {
            x = containerWidth - tooltipWidth - 10;
          }

          // 防止底部超出
          if (y + tooltipHeight > containerHeight) {
            y = y - tooltipHeight - 10;
          }

          // 防止左侧超出
          if (x < 0) {
            x = 10;
          }

          // 防止顶部超出
          if (y < 0) {
            y = 10;
          }

          return [x, y];
        },
        backgroundColor: isDarkMode ? '#3A3B42' : '#fff',
        borderColor: isDarkMode ? '#3A3B42' : '#e4e7ed',
        borderWidth: 1,
        textStyle: {
          color: isDarkMode ? '#E9E9EB' : '#333',
          fontSize: 12,
        },
        formatter: '{b}: {c}',
        extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px;',
      },
    };

    chartInstance.current.setOption(option);

    // 自适应大小
    const handleResize = () => {
      chartInstance.current?.resize({
        animation: {
          duration: 300,
          easing: 'cubicInOut',
        },
      });
    };

    const debouncedResize = () => {
      handleResize();
    };

    window.addEventListener('resize', debouncedResize);

    return () => {
      window.removeEventListener('resize', debouncedResize);
      chartInstance.current?.dispose();
    };
  }, [data, title, color, isDarkMode]);

  // 当数据变化时更新图表
  useEffect(() => {
    if (chartInstance.current) {
      const option: echarts.EChartsOption = {
        xAxis: {
          data: data.categories,
        },
        series: [
          {
            data: data.values,
          },
        ],
      };
      chartInstance.current.setOption(option, { notMerge: false, lazyUpdate: true });
    }
  }, [data]);

  return (
    <div
      ref={chartRef}
      style={{
        width: typeof width === 'number' ? `${width}px` : width,
        height: typeof height === 'number' ? `${height}px` : height,
      }}
    />
  );
};

export default BarChart;
