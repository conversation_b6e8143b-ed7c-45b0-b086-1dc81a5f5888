import React, { useState, useCallback } from 'react';
import { Statistic, Avatar, Button, Dropdown, Menu } from 'antd';
import { SwapOutlined } from '@ant-design/icons';
import CountUp from 'react-countup';
import BarChart from './charts/BarChart';
import ScrollTable from './charts/ScrollTable';
import SickLeaveMarquee from './components/SickLeaveMarquee';
import { getTodayData } from '@/services/dataDashboard';
import { getChildList } from '@/services/child';
import { fetchUserClasses } from '@/services/apis';
import { useDataFetch } from './hooks/useDataFetch';
import { useModel } from '@umijs/max';
import dayjs from 'dayjs';
import maleIcon from './img/male.png';
import femaleIcon from './img/female.png';

// 图片压缩参数
const IMAGE_COMPRESS_PARAMS = '?x-oss-process=image/resize,m_fill,w_150';

// 添加图片压缩参数的函数
const addImageCompress = (url: string) => {
  if (!url) return '';
  return url.includes('?')
    ? `${url}&x-oss-process=image/resize,m_fill,w_150`
    : `${url}${IMAGE_COMPRESS_PARAMS}`;
};

// 定义默认头像URL常量
const DEFAULT_BOY_AVATAR =
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png' +
  IMAGE_COMPRESS_PARAMS;
const DEFAULT_GIRL_AVATAR =
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png' +
  IMAGE_COMPRESS_PARAMS;

// 儿童数据接口
interface ChildData {
  id: number;
  title: string;
  sex: number; // 1为男，2为女
  birthday: string;
  entryTime: string;
  headerIds?: any[];
  [key: string]: any;
}

// 班级数据接口
interface ClassData {
  id: number;
  title: string;
  [key: string]: any;
}

// LeftSection 组件属性类型
interface LeftSectionProps {
  isDarkMode: boolean;
}

const LeftSection: React.FC<LeftSectionProps> = ({ isDarkMode }) => {
  // 获取全局状态
  const { initialState } = useModel('@@initialState');
  const currentClassId = initialState?.currentUser?.currentClassId;

  // 儿童数据状态
  const [childrenList, setChildrenList] = useState<ChildData[]>([]);
  const [currentChildIndex, setCurrentChildIndex] = useState<number>(0);
  const [classData, setClassData] = useState<ClassData | null>(null);

  // 当前显示的儿童
  const currentChild = childrenList.length > 0 ? childrenList[currentChildIndex] : null;



  // 当日数据状态
  const [todayData, setTodayData] = useState({
    requiredCount: 0,
    presentCount: 0,
    personalLeaveCount: 0,
    sickLeaveCount: 0,
    sickLeaveDetails: [],
    classAttendances: [],
  });

  // 获取儿童列表数据的函数
  const fetchChildData = useCallback(async () => {
    if (!currentClassId) return;

    try {
      const response = await getChildList({ classId: currentClassId });
      console.log('儿童列表数据:', response);
      if (response.status === 0 && response.data && response.data.length > 0) {
        setChildrenList(response.data);
        setCurrentChildIndex(0); // 默认显示第一个儿童
      }
    } catch (error) {
      console.error('获取儿童列表失败:', error);
    }
  }, [currentClassId]);

  // 获取班级数据的函数
  const fetchClassData = useCallback(async () => {
    try {
      const response = await fetchUserClasses();
      console.log('班级数据:', response);
      if (response.data && response.data.length > 0) {
        const currentClass = response.data.find((cls: any) => cls.id === currentClassId);
        if (currentClass) {
          setClassData(currentClass);
        }
      }
    } catch (error) {
      console.error('获取班级数据失败:', error);
    }
  }, [currentClassId]);



  // 获取当日数据的函数
  const fetchTodayData = useCallback(async () => {
    try {
      const response = await getTodayData({});
      console.log('当日数据:', response);
      if (response.status === 0 && response.data) {
        setTodayData({
          requiredCount: response.data.requiredCount || 0,
          presentCount: response.data.presentCount || 0,
          personalLeaveCount: response.data.personalLeaveCount || 0,
          sickLeaveCount: response.data.sickLeaveCount || 0,
          sickLeaveDetails: response.data.sickLeaveDetails || [],
          classAttendances: response.data.classAttendances || [],
        });
      }
    } catch (error) {
      console.error('获取当日数据失败:', error);
    }
  }, []);

  // 选择儿童
  const handleSelectChild = (index: number) => {
    setCurrentChildIndex(index);
  };

  // 注册数据刷新函数
  useDataFetch('leftSection-child', fetchChildData);
  useDataFetch('leftSection-class', fetchClassData);
  useDataFetch('leftSection-today', fetchTodayData);

  // 当日数据
  const dailyData = [
    { key: 'requiredCount', label: '应出勤人数', value: todayData.requiredCount },
    { key: 'presentCount', label: '实际出勤人数', value: todayData.presentCount },
    { key: 'personalLeaveCount', label: '事假人数', value: todayData.personalLeaveCount },
    { key: 'sickLeaveCount', label: '病假人数', value: todayData.sickLeaveCount },
  ];

  // 后勤物资数据
  const logisticsData = {
    summary: [
      { key: 'warehouse', label: '后勤仓库数量', value: 15 },
      { key: 'fixed', label: '固定资产数量', value: 20 },
      { key: 'activity', label: '活动物资数量', value: 5 },
    ],
    chart: {
      categories: ['分类', '纸巾', '消毒物', '物资1', '物资2', '物资3', '物资4', '物资5', '物资6'],
      values: [1, 3, 2, 3, 1.5, 2.5, 4, 2.5, 4],
    },
    remainingDays: 5,
  };

  // 计算年龄的函数
  const calculateAge = (birthday: string) => {
    if (!birthday) return 0;
    const birthDate = dayjs(birthday);
    const today = dayjs();
    return today.diff(birthDate, 'year');
  };

  // 获取默认头像
  const getDefaultAvatar = (sex: number) => {
    return sex === 1 ? DEFAULT_BOY_AVATAR : DEFAULT_GIRL_AVATAR;
  };

  // 获取儿童头像
  const getChildAvatar = (child: ChildData | null) => {
    if (!child) return getDefaultAvatar(1);

    // 如果有头像数据
    if (child.headerIds && child.headerIds.length > 0) {
      const avatar = child.headerIds[0];
      if (avatar && avatar.uri) {
        return addImageCompress(avatar.uri);
      }
    }

    return getDefaultAvatar(child.sex);
  };

  // 获取性别图标
  const getGenderIcon = (sex: number) => {
    return sex === 1 ? maleIcon : femaleIcon;
  };

  const formatter = (value: any) => <CountUp end={value as number} separator="," />;

  return (
    <div className="w-[30%] flex flex-col gap-[15px]">
      {/* 儿童信息模块 */}
      <div
        className={`h-[15%] rounded-xl shadow-md p-4 flex items-center gap-4 box-border
          ${isDarkMode ? 'bg-[#26272E]' : 'bg-[#F8F9FE]'}`}
      >
        {/* 儿童头像 */}
        <div className="flex-shrink-0">
          <Avatar
            src={getChildAvatar(currentChild)}
            size={80}
            className="border-4 border-white shadow-lg"
          />
        </div>

        {/* 儿童信息 */}
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <span className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-[#202020]'}`}>
              {currentChild?.title || '暂无儿童'}
            </span>
            {currentChild && (
              <img
                src={getGenderIcon(currentChild.sex)}
                alt={currentChild.sex === 1 ? '男' : '女'}
                className="w-6 h-6"
              />
            )}
          </div>
          <div className={`text-lg ${isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'}`}>
            {currentChild ? `${calculateAge(currentChild.birthday)}岁` : '0岁'} |{' '}
            {classData?.title || '未知班级'}
            {childrenList.length > 1 && (
              <span className="ml-2 text-sm">
                ({currentChildIndex + 1}/{childrenList.length})
              </span>
            )}
          </div>
        </div>

        {/* 切换儿童下拉选择 */}
        {childrenList.length > 1 && (
          <div className="flex-shrink-0">
            <Dropdown
              menu={{
                items: childrenList.map((child, index) => ({
                  key: index,
                  label: (
                    <div className="flex items-center gap-2 py-1">
                      <Avatar
                        src={getChildAvatar(child)}
                        size={24}
                      />
                      <span>{child.title}</span>
                      <img
                        src={getGenderIcon(child.sex)}
                        alt={child.sex === 1 ? '男' : '女'}
                        className="w-4 h-4"
                      />
                    </div>
                  ),
                  onClick: () => handleSelectChild(index),
                })),
              }}
              trigger={['click']}
              placement="bottomRight"
            >
              <Button
                type="text"
                icon={<SwapOutlined />}
                className={`w-8 h-8 flex items-center justify-center rounded-full
                  ${isDarkMode ? 'text-white hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'}`}
              />
            </Dropdown>
          </div>
        )}
      </div>
      <div
        className={`h-[55%] rounded-xl shadow-md  justify-start p-3 gap-4 flex flex-col box-border ${
          isDarkMode ? 'bg-[#26272E]' : 'bg-white'
        }`}
      >
        <div className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-[#202020]'}`}>
          当日数据
        </div>
        {/* 当日汇总数据 */}
        <div className="w-full">
          <div className="grid grid-cols-4 gap-2.5 mb-2.5">
            {dailyData.map((item) => (
              <div key={item.key} className="flex flex-col items-center text-center">
                <Statistic
                  value={item.value}
                  formatter={formatter}
                  valueStyle={{
                    color: isDarkMode ? '#E9E9EB' : '#333333',
                  }}
                />
                <span
                  className={`text-[14px] pt-1.5 ${
                    isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'
                  }`}
                >
                  {item.label}
                </span>
              </div>
            ))}
          </div>

          {/* 病假明细 */}
          <div className="flex items-center gap-2 text-base py-2">
            <span
              className={`font-semibold flex-shrink-0 ${
                isDarkMode ? 'text-[#E9E9EB]' : 'text-[#333333]'
              }`}
            >
              病假明细
            </span>
            <SickLeaveMarquee
              sickLeaveDetails={todayData.sickLeaveDetails}
              isDarkMode={isDarkMode}
            />
          </div>
        </div>
        {/* 各班出勤数据表格 */}
        <div className="w-full flex-1 flex flex-col overflow-hidden">
          <ScrollTable
            data={todayData.classAttendances}
            columns={[
              { key: 'className', title: '班级' },
              { key: 'requiredCount', title: '应出勤人数' },
              { key: 'presentCount', title: '实际出勤人数' },
              { key: 'personalLeaveCount', title: '事假人数' },
              { key: 'sickLeaveCount', title: '病假人数' },
            ]}
            isDarkMode={isDarkMode}
            title="各班出勤数据"
            gridTemplateColumns="1fr 1.2fr 1.2fr 1fr 1fr"
            headerFontSize="text-[12px]"
            cellFontSize="text-[12px]"
            headerPadding="py-5 px-3"
            cellPadding="py-5 px-3"
          />
        </div>
      </div>
      <div
        className={`h-[30%] rounded-xl shadow-md p-3 flex flex-col box-border ${
          isDarkMode ? 'bg-[#26272E]' : 'bg-white'
        }`}
      >
        <div
          className={`text-xl pb-1 font-semibold ${isDarkMode ? 'text-white' : 'text-[#202020]'}`}
        >
          后勤物资
        </div>
        {/* 后勤物资汇总数据 */}
        <div className="flex-shrink-0">
          <div className="grid grid-cols-3 gap-2">
            {logisticsData.summary.map((item) => (
              <div key={item.key} className="flex flex-col items-center text-center">
                <Statistic
                  value={item.value}
                  formatter={formatter}
                  valueStyle={{
                    color: isDarkMode ? '#E9E9EB' : '#333333',
                  }}
                />
                <span
                  className={`text-[14px] pt-1 ${isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'}`}
                >
                  {item.label}
                </span>
              </div>
            ))}
          </div>
        </div>
        {/* 各类后勤物资库存图表和预计剩余时间 */}
        <div className="w-full flex-1 flex flex-col overflow-hidden min-h-0">
          <div
            className={`text-base font-semibold pb-2 flex-shrink-0 ${
              isDarkMode ? 'text-white' : 'text-[#202020]'
            }`}
          >
            各类后勤物资库存
          </div>
          <div className="flex-1 flex gap-2  items-stretch min-h-0">
            {/* 左侧柱状图 */}
            <div className="flex-[2] min-h-0 h-full">
              <BarChart
                data={logisticsData.chart}
                color="#3b82f6"
                height="100%"
                isDarkMode={isDarkMode}
              />
            </div>

            {/* 右侧预计剩余可用时间 */}
            <div
              className={`flex-1 flex flex-col items-center justify-between rounded-lg p-2 ${
                isDarkMode ? 'bg-[#2A2B32]' : 'bg-blue-50'
              }`}
            >
              <div
                className={`text-[14px] flex flex-col items-center  ${
                  isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'
                }`}
              >
                <span> 预计剩余</span>
                <span> 可用时间</span>
              </div>
              <div
                className={`text-xl font-semibold leading-none ${
                  isDarkMode ? 'text-[#E9E9EB]' : 'text-blue-600'
                }`}
              >
                {logisticsData.remainingDays}
              </div>
              <div className={`text-xs ${isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'}`}>
                天
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LeftSection;
