import React, { useState, useCallback } from 'react';
import { Statistic } from 'antd';
import CountUp from 'react-countup';
import Bar<PERSON><PERSON> from './charts/BarChart';
import ScrollTable from './charts/ScrollTable';
import SickLeaveMarquee from './components/SickLeaveMarquee';
import { getSchoolData, getTodayData } from '@/services/dataDashboard';
import { useDataFetch } from './hooks/useDataFetch';

// LeftSection 组件属性类型
interface LeftSectionProps {
  isDarkMode: boolean;
}

const LeftSection: React.FC<LeftSectionProps> = ({ isDarkMode }) => {
  // 学校数据状态
  const [schoolData, setSchoolData] = useState({
    classCount: 0,
    childCount: 0,
    parentCount: 0,
    teacherCount: 0,
  });

  // 当日数据状态
  const [todayData, setTodayData] = useState({
    requiredCount: 0,
    presentCount: 0,
    personalLeaveCount: 0,
    sickLeaveCount: 0,
    sickLeaveDetails: [],
    classAttendances: [],
  });

  // 获取学校数据的函数
  const fetchSchoolData = useCallback(async () => {
    try {
      const response = await getSchoolData({});
      console.log('学校数据:', response);
      if (response.status === 0 && response.data) {
        setSchoolData({
          classCount: response.data.classCount || 0,
          childCount: response.data.childCount || 0,
          parentCount: response.data.parentCount || 0,
          teacherCount: response.data.teacherCount || 0,
        });
      }
    } catch (error) {
      console.error('获取学校数据失败:', error);
    }
  }, []);

  // 获取当日数据的函数
  const fetchTodayData = useCallback(async () => {
    try {
      const response = await getTodayData({});
      console.log('当日数据:', response);
      if (response.status === 0 && response.data) {
        setTodayData({
          requiredCount: response.data.requiredCount || 0,
          presentCount: response.data.presentCount || 0,
          personalLeaveCount: response.data.personalLeaveCount || 0,
          sickLeaveCount: response.data.sickLeaveCount || 0,
          sickLeaveDetails: response.data.sickLeaveDetails || [],
          classAttendances: response.data.classAttendances || [],
        });
      }
    } catch (error) {
      console.error('获取当日数据失败:', error);
    }
  }, []);

  // 注册数据刷新函数
  useDataFetch('leftSection-school', fetchSchoolData);
  useDataFetch('leftSection-today', fetchTodayData);

  // 班级汇总数据
  const summaryData = [
    { key: 'classCount', label: '班级数量', value: schoolData.classCount },
    { key: 'childCount', label: '儿童数量', value: schoolData.childCount },
    { key: 'parentCount', label: '家长数量', value: schoolData.parentCount },
    { key: 'teacherCount', label: '教师数量', value: schoolData.teacherCount },
  ];

  // 当日数据
  const dailyData = [
    { key: 'requiredCount', label: '应出勤人数', value: todayData.requiredCount },
    { key: 'presentCount', label: '实际出勤人数', value: todayData.presentCount },
    { key: 'personalLeaveCount', label: '事假人数', value: todayData.personalLeaveCount },
    { key: 'sickLeaveCount', label: '病假人数', value: todayData.sickLeaveCount },
  ];

  // 后勤物资数据
  const logisticsData = {
    summary: [
      { key: 'warehouse', label: '后勤仓库数量', value: 15 },
      { key: 'fixed', label: '固定资产数量', value: 20 },
      { key: 'activity', label: '活动物资数量', value: 5 },
    ],
    chart: {
      categories: ['分类', '纸巾', '消毒物', '物资1', '物资2', '物资3', '物资4', '物资5', '物资6'],
      values: [1, 3, 2, 3, 1.5, 2.5, 4, 2.5, 4],
    },
    remainingDays: 5,
  };

  const formatter = (value: any) => <CountUp end={value as number} separator="," />;

  return (
    <div className="w-[30%] flex flex-col gap-[15px]">
      <div
        className={`h-[15%] rounded-xl shadow-md] 
          p-3 flex flex-col items-start text-left justify-start box-border
          ${isDarkMode ? 'bg-[#26272E]' : 'bg-white'}`}
      >
        <div
          className={`text-xl font-semibold pb-2.5 ${isDarkMode ? 'text-white' : 'text-[#202020]'}`}
        >
          班级汇总
        </div>
        <div className="flex-1 w-full grid grid-cols-4 gap-4">
          {summaryData.map((item) => (
            <div key={item.key} className="flex flex-col justify-center items-center">
              <Statistic
                value={item.value}
                formatter={formatter}
                valueStyle={{
                  color: isDarkMode ? '#E9E9EB' : '#333333',
                }}
              />
              <span
                className={`text-[14px]  pt-1.5 ${
                  isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'
                }`}
              >
                {item.label}
              </span>
            </div>
          ))}
        </div>
      </div>
      <div
        className={`h-[55%] rounded-xl shadow-md  justify-start p-3 gap-4 flex flex-col box-border ${
          isDarkMode ? 'bg-[#26272E]' : 'bg-white'
        }`}
      >
        <div className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-[#202020]'}`}>
          当日数据
        </div>
        {/* 当日汇总数据 */}
        <div className="w-full">
          <div className="grid grid-cols-4 gap-2.5 mb-2.5">
            {dailyData.map((item) => (
              <div key={item.key} className="flex flex-col items-center text-center">
                <Statistic
                  value={item.value}
                  formatter={formatter}
                  valueStyle={{
                    color: isDarkMode ? '#E9E9EB' : '#333333',
                  }}
                />
                <span
                  className={`text-[14px] pt-1.5 ${
                    isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'
                  }`}
                >
                  {item.label}
                </span>
              </div>
            ))}
          </div>

          {/* 病假明细 */}
          <div className="flex items-center gap-2 text-base py-2">
            <span
              className={`font-semibold flex-shrink-0 ${
                isDarkMode ? 'text-[#E9E9EB]' : 'text-[#333333]'
              }`}
            >
              病假明细
            </span>
            <SickLeaveMarquee
              sickLeaveDetails={todayData.sickLeaveDetails}
              isDarkMode={isDarkMode}
            />
          </div>
        </div>
        {/* 各班出勤数据表格 */}
        <div className="w-full flex-1 flex flex-col overflow-hidden">
          <ScrollTable
            data={todayData.classAttendances}
            columns={[
              { key: 'className', title: '班级' },
              { key: 'requiredCount', title: '应出勤人数' },
              { key: 'presentCount', title: '实际出勤人数' },
              { key: 'personalLeaveCount', title: '事假人数' },
              { key: 'sickLeaveCount', title: '病假人数' },
            ]}
            isDarkMode={isDarkMode}
            title="各班出勤数据"
            gridTemplateColumns="1fr 1.2fr 1.2fr 1fr 1fr"
            headerFontSize="text-[12px]"
            cellFontSize="text-[12px]"
            headerPadding="py-5 px-3"
            cellPadding="py-5 px-3"
          />
        </div>
      </div>
      <div
        className={`h-[30%] rounded-xl shadow-md p-3 flex flex-col box-border ${
          isDarkMode ? 'bg-[#26272E]' : 'bg-white'
        }`}
      >
        <div
          className={`text-xl pb-1 font-semibold ${isDarkMode ? 'text-white' : 'text-[#202020]'}`}
        >
          后勤物资
        </div>
        {/* 后勤物资汇总数据 */}
        <div className="flex-shrink-0">
          <div className="grid grid-cols-3 gap-2">
            {logisticsData.summary.map((item) => (
              <div key={item.key} className="flex flex-col items-center text-center">
                <Statistic
                  value={item.value}
                  formatter={formatter}
                  valueStyle={{
                    color: isDarkMode ? '#E9E9EB' : '#333333',
                  }}
                />
                <span
                  className={`text-[14px] pt-1 ${isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'}`}
                >
                  {item.label}
                </span>
              </div>
            ))}
          </div>
        </div>
        {/* 各类后勤物资库存图表和预计剩余时间 */}
        <div className="w-full flex-1 flex flex-col overflow-hidden min-h-0">
          <div
            className={`text-base font-semibold pb-2 flex-shrink-0 ${
              isDarkMode ? 'text-white' : 'text-[#202020]'
            }`}
          >
            各类后勤物资库存
          </div>
          <div className="flex-1 flex gap-2  items-stretch min-h-0">
            {/* 左侧柱状图 */}
            <div className="flex-[2] min-h-0 h-full">
              <BarChart
                data={logisticsData.chart}
                color="#3b82f6"
                height="100%"
                isDarkMode={isDarkMode}
              />
            </div>

            {/* 右侧预计剩余可用时间 */}
            <div
              className={`flex-1 flex flex-col items-center justify-between rounded-lg p-2 ${
                isDarkMode ? 'bg-[#2A2B32]' : 'bg-blue-50'
              }`}
            >
              <div
                className={`text-[14px] flex flex-col items-center  ${
                  isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'
                }`}
              >
                <span> 预计剩余</span>
                <span> 可用时间</span>
              </div>
              <div
                className={`text-xl font-semibold leading-none ${
                  isDarkMode ? 'text-[#E9E9EB]' : 'text-blue-600'
                }`}
              >
                {logisticsData.remainingDays}
              </div>
              <div className={`text-xs ${isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'}`}>
                天
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LeftSection;
