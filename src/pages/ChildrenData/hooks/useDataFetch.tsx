import { useEffect, useCallback, useRef } from 'react';
import { useRefresh } from './useRefresh';

export const useDataFetch = (
  id: string,
  fetchFunction: () => Promise<void> | void,
  deps: any[] = []
) => {
  const { registerRefresh, unregisterRefresh } = useRefresh();
  
  // 使用 ref 存储最新的 fetchFunction，避免依赖变化导致的无限循环
  const fetchFunctionRef = useRef(fetchFunction);
  fetchFunctionRef.current = fetchFunction;

  // 创建稳定的刷新函数引用
  const refresh = useCallback(async () => {
    try {
      console.log(`开始刷新 ${id} 数据`);
      await fetchFunctionRef.current();
      console.log(`${id} 数据刷新成功`);
    } catch (error) {
      console.error(`刷新 ${id} 数据失败:`, error);
    }
  }, [id]); // 只依赖 id，避免 fetchFunction 变化导致的重新注册

  // 注册和注销刷新函数
  useEffect(() => {
    registerRefresh(id, refresh);
    return () => {
      unregisterRefresh(id);
    };
  }, [id, refresh, registerRefresh, unregisterRefresh]);

  // 初始化时执行一次数据获取
  useEffect(() => {
    refresh();
  }, deps); // eslint-disable-line react-hooks/exhaustive-deps

  return refresh;
};
