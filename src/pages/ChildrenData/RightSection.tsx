import React, { useState, useCallback } from 'react';
import { Statistic } from 'antd';
import CountUp from 'react-countup';
import LineChart from './charts/LineChart';
import BarChart from './charts/BarChart';
import ScrollTable from './charts/ScrollTable';
import { getSchoolData, getLearnActivityStatsWeek } from '@/services/dataDashboard';
import { useDataFetch } from './hooks/useDataFetch';

// RightSection 组件属性类型
interface RightSectionProps {
  isDarkMode: boolean;
}

const RightSection: React.FC<RightSectionProps> = ({ isDarkMode }) => {
  // 学校数据状态
  const [schoolData, setSchoolData] = useState({
    subjectCount: 0,
    activityCount: 0,
    activityCommentCount: 0,
    activityWordCount: 0,
    activityEvaluationDetails: [],
  });

  // 图表数据状态
  const [chartData, setChartData] = useState({
    courseChanges: [],
    activityPlanAndEvaluationChanges: [],
    processRecordWordChanges: [],
  });

  // 获取学校数据的函数
  const fetchSchoolData = useCallback(async () => {
    try {
      const response = await getSchoolData({});
      console.log('学校数据:', response);
      if (response.status === 0 && response.data) {
        setSchoolData({
          subjectCount: response.data.subjectCount || 0,
          activityCount: response.data.activityCount || 0,
          activityCommentCount: response.data.activityCommentCount || 0,
          activityWordCount: response.data.activityWordCount || 0,
          activityEvaluationDetails: response.data.activityEvaluationDetails || [],
        });
      }
    } catch (error) {
      console.error('获取学校数据失败:', error);
    }
  }, []);

  // 获取图表数据的函数
  const fetchChartData = useCallback(async () => {
    try {
      const response = await getLearnActivityStatsWeek({});
      console.log('图表数据:', response);
      if (response.status === 0 && response.data) {
        setChartData({
          courseChanges: response.data.courseChanges || [],
          activityPlanAndEvaluationChanges: response.data.activityPlanAndEvaluationChanges || [],
          processRecordWordChanges: response.data.processRecordWordChanges || [],
        });
      }
    } catch (error) {
      console.error('获取图表数据失败:', error);
    }
  }, []);

  // 注册数据刷新函数
  useDataFetch('rightSection-school', fetchSchoolData);
  useDataFetch('rightSection-chart', fetchChartData);

  // CountUp formatter
  const formatter = (value: any) => <CountUp end={value as number} separator="," />;

  // 学习活动汇总数据
  const learningActivityData = [
    { key: 'courses', label: '课程', value: schoolData.subjectCount },
    { key: 'activities', label: '活动教案', value: schoolData.activityCount },
    { key: 'evaluations', label: '活动评价', value: schoolData.activityCommentCount },
    { key: 'records', label: '活动过程记录字数', value: schoolData.activityWordCount },
  ];

  // 材料库存统计数据
  // const materialStatsData = [
  //   { key: 'classArea', label: '班级区域材料总数', value: 5, unit: '天' },
  //   { key: 'outdoorCount', label: '户外区域数量', value: 5, unit: '天' },
  //   { key: 'outdoorMaterial', label: '户外区域材料总数', value: 5, unit: '天' },
  // ];

  // 各类区域材料库存柱状图数据
  const materialChartData = {
    categories: ['场地1', '场地2 场地3', '场地4', '场地5', '场地6', '场地7', '场地8', '场地9'],
    values: [1, 5, 4, 6, 2, 3, 8, 3, 4],
  };

  // AI使用量统计数据
  const aiUsageData = [
    { key: 'textGeneration', label: '文本生成', value: 5 },
    { key: 'textCorrection', label: '文本修正', value: 20 },
    { key: 'speechRecognition', label: '一句话识别', value: 5 },
    { key: 'audioRecognition', label: '录音识别', value: 5 },
    { key: 'imageAnalysis', label: '图像分析', value: 5 },
    { key: 'faceRecognition', label: '人脸识别', value: 5 },
  ];

  return (
    <div className="w-[55%] flex flex-col gap-[15px]">
      <div
        className={`p-3 h-[55%] rounded-xl shadow-md flex flex-col overflow-hidden ${
          isDarkMode ? 'bg-[#26272E]' : 'bg-white'
        }`}
      >
        <div className={`text-xl font-semibold  ${isDarkMode ? 'text-white' : 'text-[#202020]'}`}>
          学习活动
        </div>
        <div
          className={`flex-1 flex gap-2 min-h-0 ${
            isDarkMode ? 'text-[#E9E9EB]' : 'text-[#333333]'
          }`}
        >
          <div className="flex-1 flex flex-col min-h-0">
            {/* 学习活动汇总数据 */}
            <div className="flex-shrink-0">
              <div className="grid grid-cols-4 gap-3">
                {learningActivityData.map((item) => (
                  <div key={item.key} className="flex flex-col items-center text-center">
                    <Statistic
                      value={item.value}
                      formatter={formatter}
                      valueStyle={{
                        color: isDarkMode ? '#E9E9EB' : '#333333',
                        fontWeight: 600,
                      }}
                    />
                    <span
                      className={`text-[14px] pt-1.5 ${
                        isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'
                      }`}
                    >
                      {item.label}
                    </span>
                  </div>
                ))}
              </div>
            </div>
            {/* 左侧表格区域 */}
            <div className="flex-1 flex flex-col overflow-hidden pt-2 min-h-0">
              <ScrollTable
                data={schoolData.activityEvaluationDetails}
                columns={[
                  { key: 'className', title: '班级' },
                  { key: 'subjectName', title: '课程' },
                  { key: 'activityCount', title: '活动教案' },
                  { key: 'activityCommentCount', title: '活动评价' },
                  { key: 'activityWordCount', title: '活动过程记录字数' },
                ]}
                isDarkMode={isDarkMode}
                title="各班活动教案和评价分布图"
                height="100%"
                gridTemplateColumns="0.8fr 0.8fr 1fr 1fr 1.4fr"
                headerFontSize="text-[10px]"
                cellFontSize="text-[10px]"
                headerPadding="py-1.5 px-1"
                cellPadding="py-1 px-0.5"
              />
            </div>
          </div>
          {/* 右侧图表区域 */}
          <div className="flex-1 flex flex-col gap-2">
            {/* 课程变化曲线 */}
            <div className="flex-1 flex flex-col overflow-hidden min-h-0">
              <div
                className={`text-[11px] font-semibold mb-1 flex-shrink-0 ${
                  isDarkMode ? 'text-white' : 'text-[#202020]'
                }`}
              >
                课程变化曲线
              </div>
              <div className="flex-1 min-h-0 w-full h-full">
                <LineChart
                  data={{
                    categories: chartData.courseChanges.map((item: any) => item.week),
                    series: [
                      {
                        name: '课程',
                        data: chartData.courseChanges.map((item: any) => item.count),
                        color: '#3b82f6',
                      },
                    ],
                  }}
                  height="100%"
                  smooth={true}
                  area={true}
                  isDarkMode={isDarkMode}
                />
              </div>
            </div>

            {/* 活动教案&评价变化曲线 */}
            <div className="flex-1 flex flex-col overflow-hidden min-h-0">
              <div
                className={`text-[11px] font-semibold mb-1 flex-shrink-0 ${
                  isDarkMode ? 'text-white' : 'text-[#202020]'
                }`}
              >
                活动教案&评价变化曲线
              </div>
              <div className="flex-1 min-h-0 w-full h-full">
                <LineChart
                  data={{
                    categories: chartData.activityPlanAndEvaluationChanges.map(
                      (item: any) => item.week,
                    ),
                    series: [
                      {
                        name: '活动教案&评价',
                        data: chartData.activityPlanAndEvaluationChanges.map(
                          (item: any) => item.count,
                        ),
                        color: '#10b981',
                      },
                    ],
                  }}
                  height="100%"
                  smooth={true}
                  area={true}
                  isDarkMode={isDarkMode}
                />
              </div>
            </div>

            {/* 过程记录字数变化曲线 */}
            <div className="flex-1 flex flex-col overflow-hidden min-h-0">
              <div
                className={`text-[11px] font-semibold mb-1 flex-shrink-0 ${
                  isDarkMode ? 'text-white' : 'text-[#202020]'
                }`}
              >
                过程记录字数变化曲线
              </div>
              <div className="flex-1 min-h-0 w-full h-full">
                <LineChart
                  data={{
                    categories: chartData.processRecordWordChanges.map((item: any) => item.week),
                    series: [
                      {
                        name: '过程记录字数',
                        data: chartData.processRecordWordChanges.map((item: any) => item.count),
                        color: '#f59e0b',
                      },
                    ],
                  }}
                  height="100%"
                  smooth={true}
                  area={true}
                  isDarkMode={isDarkMode}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        className={`p-3 h-[30%] flex flex-col overflow-hidden rounded-xl shadow-md ${
          isDarkMode ? 'bg-[#26272E]' : 'bg-white'
        }`}
      >
        <div
          className={`text-xl font-semibold pb-2  ${isDarkMode ? 'text-white' : 'text-[#202020]'}`}
        >
          材料
        </div>
        {/* 材料内容区域 */}
        <div className="flex-1 flex gap-4 items-stretch min-h-0">
          {/* 左侧统计数据 */}
          <div className="flex-[2] flex gap-1 flex-col min-h-0 overflow-hidden">
            <div
              className={`text-sm font-semibold flex-shrink-0 ${
                isDarkMode ? 'text-white' : 'text-[#202020]'
              }`}
            >
              各类区域材料库存
            </div>
            <div className="flex-1 flex flex-col gap-1 min-h-0">
              <div
                className={`p-2 py-4 rounded-lg  flex items-center ${
                  isDarkMode ? 'bg-[#2A2B32]' : 'bg-blue-50'
                }`}
              >
                <div className={`text-sm mr-4 ${isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'}`}>
                  班级区域材料总数
                </div>
                <div className="flex items-center">
                  <Statistic
                    value={5}
                    formatter={formatter}
                    valueStyle={{
                      color: isDarkMode ? '#E9E9EB' : '#333333',
                    }}
                  />
                  <span
                    className={`text-sm ml-1 ${isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'}`}
                  >
                    个
                  </span>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-1  min-h-0">
                <div
                  className={`p-2 py-6 rounded-lg flex flex-col justify-center ${
                    isDarkMode ? 'bg-[#2A2B32]' : 'bg-blue-50'
                  }`}
                >
                  <div className={`text-sm   ${isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'}`}>
                    户外区域数量
                  </div>
                  <div className="flex items-center">
                    <Statistic
                      value={5}
                      formatter={formatter}
                      valueStyle={{
                        color: isDarkMode ? '#E9E9EB' : '#333333',
                      }}
                    />
                    <span className={`text-sm ${isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'}`}>
                      个
                    </span>
                  </div>
                </div>
                <div
                  className={`p-1 rounded-lg flex flex-col justify-center ${
                    isDarkMode ? 'bg-[#2A2B32]' : 'bg-blue-50'
                  }`}
                >
                  <div className={`text-sm   ${isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'}`}>
                    户外区域材料总数
                  </div>
                  <div className="flex items-center">
                    <Statistic
                      value={5}
                      formatter={formatter}
                      valueStyle={{
                        color: isDarkMode ? '#E9E9EB' : '#333333',
                      }}
                    />
                    <span className={`text-sm ${isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'}`}>
                      个
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧柱状图 */}
          <div className="flex-1 flex flex-col min-h-0">
            <div className="flex-1 w-full h-full min-h-0">
              <BarChart
                data={materialChartData}
                color="#3b82f6"
                height="100%"
                isDarkMode={isDarkMode}
              />
            </div>
          </div>
        </div>
      </div>
      <div
        className={`h-[15%] rounded-xl shadow-md p-3 flex flex-col ${
          isDarkMode ? 'bg-[#26272E]' : 'bg-white'
        }`}
      >
        <div className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-[#202020]'}`}>
          AI使用量
        </div>
        {/* AI使用量统计 */}
        <div className="flex-1 w-full grid grid-cols-6  items-center">
          {aiUsageData.map((item) => (
            <div key={item.key} className="flex flex-col justify-center items-center">
              <div className="mb-1">
                <Statistic
                  value={item.value}
                  formatter={formatter}
                  valueStyle={{
                    color: isDarkMode ? '#E9E9EB' : '#333333',
                  }}
                />
              </div>
              <div
                className={`text-[14px] text-center ${
                  isDarkMode ? 'text-[#BBBDC1]' : 'text-[#767779]'
                }`}
              >
                {item.label}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RightSection;
