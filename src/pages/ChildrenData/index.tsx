import React, { useState, useEffect } from 'react';
import { FloatButton, message, Popover } from 'antd';
import {
  FullscreenOutlined,
  FullscreenExitOutlined,
  QuestionOutlined,
  ReloadOutlined,
  SunOutlined,
  MoonOutlined,
} from '@ant-design/icons';
import LeftSection from './LeftSection';
import CenterSection from './CenterSection';
import RightSection from './RightSection';
import { RefreshProvider, useRefresh } from './hooks/useRefresh';
import './index.less';

// 数据大屏内容组件
const DataDashboardContent: React.FC = () => {
  // 全屏状态
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  // 主题状态
  const [isDarkMode, setIsDarkMode] = useState<boolean>(false);

  // 获取刷新上下文
  const { refreshAll } = useRefresh();

  // 监听全屏变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isFullscreenNow = !!document.fullscreenElement;
      setIsFullscreen(isFullscreenNow);
    };

    // 设置 body 样式防止滚动
    document.body.style.margin = '0';
    document.body.style.padding = '0';
    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';

    document.addEventListener('fullscreenchange', handleFullscreenChange);

    return () => {
      // 组件卸载时恢复默认样式
      document.body.style.overflow = 'auto';
      document.documentElement.style.overflow = 'auto';
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // 切换全屏
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement
        .requestFullscreen()
        .then(() => {
          setIsFullscreen(true);
        })
        .catch((err) => {
          message.error(`无法进入全屏: ${err.message}`);
        });
    } else {
      if (document.exitFullscreen) {
        document
          .exitFullscreen()
          .then(() => {
            setIsFullscreen(false);
          })
          .catch((err) => {
            message.error(`无法退出全屏: ${err.message}`);
          });
      }
    }
  };

  // 刷新数据而不是刷新页面
  const handleRefresh = async () => {
    try {
      message.info('正在刷新数据...');
      await refreshAll();
      message.success('数据刷新成功');
    } catch (error) {
      console.error('数据刷新失败:', error);
      message.error('数据刷新失败');
    }
  };

  // 切换主题
  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  // 说明信息内容
  const helpContent = (
    <div style={{ maxWidth: 300 }}>
      <p>• 7点刷新整个页面</p>
      <p>• 全量统计在每日6点前统计历史数据</p>
      <p>• 单日增量数据在每日6点前统计上一日数据</p>
      <p>• 单周增量数据在每周一6点前统计上一周数据</p>
      <p>• 环比数据在增量数据统计时，计算环比变化值</p>
    </div>
  );

  return (
    <div className={`data-dashboard ${isFullscreen ? 'fullscreen-mode' : 'normal-mode'} ${
      isDarkMode
        ? 'bg-[#1A1A1A]'
        : 'bg-gradient-to-b from-blue-400 to-blue-50'
    }`}>
      {/* 主要内容区域 */}
      <div className={`dashboard-content ${isFullscreen ? 'fullscreen-mode' : 'normal-mode'}`}>
        {/* 图表展示区域 - 左中右三列布局 */}
        <div className={`dashboard-chart-area ${isFullscreen ? 'fullscreen-mode' : 'normal-mode'}`}>
          <LeftSection isDarkMode={isDarkMode} />
          <CenterSection isDarkMode={isDarkMode} />
          <RightSection isDarkMode={isDarkMode} />
        </div>
      </div>

      {/* 右上角悬浮按钮组 */}
      <FloatButton.Group style={{ right: 24, top: 24 }} type="primary">
        {/* 说明信息按钮 */}
        <Popover content={helpContent} title="数据说明" placement="leftBottom">
          <FloatButton icon={<QuestionOutlined />} />
        </Popover>

        {/* 主题切换按钮 */}
        <FloatButton
          icon={isDarkMode ? <SunOutlined /> : <MoonOutlined />}
          onClick={toggleTheme}
          tooltip={isDarkMode ? '切换到浅色模式' : '切换到深色模式'}
        />

        {/* 刷新按钮 */}
        <FloatButton icon={<ReloadOutlined />} onClick={handleRefresh} />

        {/* 全屏按钮 */}
        <FloatButton
          icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
          onClick={toggleFullscreen}
        />
      </FloatButton.Group>
    </div>
  );
};

// 主组件，提供刷新上下文
const DataDashboard: React.FC = () => {
  return (
    <RefreshProvider>
      <DataDashboardContent />
    </RefreshProvider>
  );
};

export default DataDashboard;
