import React, { useEffect, useRef, useState } from 'react';
import { useParams, useLocation, useModel } from '@umijs/max';
import {
  PageContainer,
  ProForm,
  ProFormDatePicker,
  ProFormGroup,
  ProFormList,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Form, Row, Col, Card, message } from 'antd';
import UploadImageComponent from '@/components/UploadImage';
// import { CloseCircleOutlined } from '@ant-design/icons';
import {
  getObsItemDetails,
  getObsClassList,
  getObsChildList,
  getObsMatrixList,
  getMatrix,
  getTarget,
  updateObsItem,
  updateCoreExpList,
} from '@/services/api';

export const getLists = async (params: { pid: number; current: number; pageSize: number }) => {
  try {
    const res = await getMatrix(params);
    return res.data?.map((item) => ({ label: item.title, value: item.id }));
  } catch (e) {
    return [];
  }
};
const getTargetList = async (params) => {
  try {
    const res = await getTarget(params);

    return res.data?.map((item) => ({ label: item.title, value: item.id }));
  } catch (e) {
    return [];
  }
};
const UserForm = () => {
  const [form] = Form.useForm();
  const { id } = useParams(); // 获取路由参数，有id为编辑，无id为新建
  const isEdit = !!id;
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const isDetail = !!searchParams.get('isDetail');
  const { initialState } = useModel('@@initialState');
  const { enums, currentUser } = initialState || {};
  const { ObservationRecordSceneEnumDesc } = enums || {};
  const [client, setClient] = useState<any>();
  const [fileList, setFileList] = useState([]);
  const [classList, setClassList] = useState([]); // 班级列表
  const [childList, setChildList] = useState([]); // 孩子列表
  const [matrix, setMatrix] = useState([]); // 领域
  // 观察类型映射
  const observationTypeMap = Object.keys(ObservationRecordSceneEnumDesc).map((key) => {
    return {
      label: ObservationRecordSceneEnumDesc[key],
      value: key,
    };
  });
  // 获取领域
  const fetchList = async () => {
    const list = await getLists({ pid: 0, current: 1, pageSize: 1000 });
    setMatrix(list);
  };
  // 获取核心经验
  const getMatrixListData = async () => {
    let params = { relId: Number(id), matchType: 'observation' };
    const res = await getObsMatrixList(params);
    if (res.status === 0) {
      console.log('核心经验, ', res.data);
      form.setFieldsValue({ matrices: res.data });
      return;
    }
    message.error('获取失败!');
  };
  // // 保存核心经验
  const onSaveCore = async () => {
    let data = {
      relId: Number(id),
      matchType: 'observation',
      list: [],
    };
    data.list = form.getFieldValue('matrices').map((item: any) => {
      item.schoolId = currentUser?.currentSchoolId;
      return {
        relId: Number(id),
        matchType: 'observation',
        matrix1Id: item.matrix1Id,
        matrix1Name: item.matrix1Name,
        matrix2Id: item.matrix2Id,
        matrix2Name: item.matrix2Name,
        matrix3Id: item.matrix3Id,
        matrix3Name: item.matrix3Name,
        targetId: item.targetId,
        targetName: item.targetName,
      };
    });

    try {
      let res = await updateCoreExpList(data);
      if (res.status !== 0) {
        message.error(res?.message || '核心经验保存失败');
        return false;
      }
      return true;
    } catch (err) {
      console.log(err);
      return false;
    }
    // return true;
  };

  const getChildListData = async (classId: number) => {
    let params = { classId: classId || form.getFieldValue('classId'), current: 1, pageSize: 999 };
    const res = await getObsChildList(params);
    if (res.status === 0) {
      console.log('儿童列表, ', res.data);

      const classList = res.data?.map((item: any) => {
        return {
          ...item,
          label: item.title,
          value: item.id,
        };
      });
      setChildList(classList);
    }
  };
  const getClassListData = async () => {
    const res = await getObsClassList();
    if (res.status === 0) {
      const classList = res.data?.map((item: any) => {
        return {
          label: item.title,
          value: item.id,
        };
      });
      setClassList(classList);
      form.setFieldsValue({ classId: currentUser?.currentClassId });
      getChildListData(Number(currentUser?.currentClassId));
    }
  };
  // 获取详情
  const getDetails = async () => {
    if (!id) return;
    const res = await getObsItemDetails(Number(id));
    if (res.status === 0) {
      console.log(res);
      form.setFieldsValue(res.data);
      let fileLists = res.data?.picList?.map((item: any) => {
        return {
          uid: '',
          name: '',
          uri: item,
        };
      });
      fetchList();
      getMatrixListData();
      let childLists = res.data?.children?.map((item: any) => item.childId);
      form.setFieldsValue({ Observable: childLists });
      setFileList(fileLists);
    }
  };
  useEffect(() => {
    getDetails();
    getClassListData();
  }, []);
  return (
    <PageContainer title={isEdit ? '编辑用户' : '新建用户'}>
      <Card bordered={false}>
        <ProForm
          style={{
            margin: 'auto',
            marginTop: 8,
          }}
          {...((isDetail && { submitter: false, readonly: true }) || {
            submitter: {
              resetButtonProps: {
                style: {
                  display: 'none', // 隐藏提交按钮
                },
              },
            },
          })}
          className={`activityDetail ${(isDetail && 'detailActivityDetail') || ''}`}
          clearOnDestroy
          form={form}
          name="basic"
          layout="vertical"
          onFinish={(value) => {
            console.log(value, '图片');

            let Children = value.Observable.map((i: any) => {
              let o = childList.find((item: any) => item?.id === i);
              let o1 = classList.find((item: any) => item?.value === o?.classId);
              return {
                classId: o?.classId,
                childId: i,
                childName: o?.title,
                className: o1?.label,
              };
            });
            let params = {
              ...value,
              id: Number(id),
              picList: fileList.map((item) => item?.url || item?.uri),
              children: Children,
            };
            console.log(params);
            updateObsItem(params).then((res) => {
              if (res.status === 0) {
                message.success('修改成功');
                // history.push('/observation/list');
              }
            });
            if (value.matrices) onSaveCore();
          }}
          onFinishFailed={(e) => {
            // 提交表单且数据验证失败后回调事件
            console.log(e, '111111');
          }}
        >
          <Card bordered={false} title={'基本信息'}>
            <Row gutter={8}>
              <Col span={12}>
                <ProFormSelect
                  // rules={[
                  //   {
                  //     required: true,
                  //     message: '请填写活动名称',
                  //   },
                  // ]}
                  label="记录班级"
                  name="classId"
                  options={classList}
                />
              </Col>
              <Col span={12}>
                <ProFormSelect
                  mode="multiple"
                  // rules={[
                  //   {
                  //     required: true,
                  //     message: '请选择预设生成',
                  //   },
                  // ]}
                  name="Observable"
                  label="观察对象"
                  options={childList}
                />
              </Col>
              <Col span={12}>
                <ProFormText
                  rules={[
                    {
                      required: true,
                      message: '请输入活动名称',
                    },
                  ]}
                  // disabled
                  name="activityName"
                  label="活动名称"
                />
              </Col>
              <Col span={12}>
                <ProFormText
                  rules={[
                    {
                      required: true,
                      message: '请输入观察地点',
                    },
                  ]}
                  label="观察地点"
                  name="observationLocation"
                />
              </Col>
              <Col span={12}>
                <ProFormDatePicker
                  format="YYYY-MM-DD HH:mm:ss"
                  showTime
                  // fieldProps={{
                  //   addonAfter: '分钟',
                  // }}
                  rules={[
                    {
                      required: true,
                      message: '请输入观察时间',
                    },
                  ]}
                  width="100%"
                  name="observationTime"
                  label={'观察时间'}
                  placeholder="请输入"
                />
              </Col>
              <Col span={12}>
                <ProFormSelect
                  rules={[
                    {
                      required: true,
                      message: '请选择实施时间',
                    },
                  ]}
                  name="observationType"
                  label="记录场景"
                  placeholder="请选择实施时间"
                  options={observationTypeMap}
                />
              </Col>
              <Col span={24}>
                <ProFormTextArea name="productDesc" label="材料" placeholder="请输入材料" />
              </Col>
              {/* <Col span={24}>
                <ProFormText placeholder="请输入本活动的关键问题，通过关键问题可以迅速了解本活动探究的主要内容，例如，“普通的黄泥土可以制作陶瓷碗吗？如果用黄泥土捏模型，你发现了什么问题？为什么不能用黄泥土做陶瓷碗？”" label="关键问题" name="keyQuestion" />
              </Col> */}
              <Col span={24}>
                <ProFormTextArea
                  name="observationBackground"
                  label="背景"
                  placeholder="请输入背景"
                />
              </Col>
            </Row>

            <Row>
              <Col span={24}>
                <ProFormTextArea
                  // rules={[
                  //   {
                  //     required: true,
                  //     message: '请填写教学目标',
                  //   },
                  // ]}
                  label="目的"
                  name="observationPurpose"
                  placeholder="请输入目的"
                  // fieldProps={{ maxLength: 200 }}
                />
              </Col>
              <Col span={24}>
                <ProFormTextArea
                  // rules={[
                  //   {
                  //     required: true,
                  //     message: '请填写活动重点',
                  //   },
                  // ]}
                  label="观察内容"
                  name="observationContent"
                  placeholder="请输入观察内容"
                  // fieldProps={{ maxLength: 200 }}
                />
              </Col>
              <Col span={24}>
                <ProFormTextArea
                  // rules={[
                  //   {
                  //     required: true,
                  //     message: '请填写资源和环境支持',
                  //   },
                  // ]}
                  label="分析"
                  placeholder="请输入使用到的教具、资源或环境，包括但不限于：教具、玩具、实物、ppt、图画书、图片、音视频、家长资源、园所资源、社区资源、班级环境、园所环境等"
                  name="analysis"
                />
              </Col>
              <Col span={24}>
                <ProFormTextArea
                  // rules={[
                  //   {
                  //     required: true,
                  //     message: '请填写资源和环境支持',
                  //   },
                  // ]}
                  label="支持"
                  placeholder="请输入支持策略"
                  name="support"
                />
              </Col>
              <Col span={24}>
                <ProFormTextArea
                  // rules={[
                  //   {
                  //     required: true,
                  //     message: '请填写资源和环境支持',
                  //   },
                  // ]}
                  label="反思"
                  placeholder="请输入反思总结"
                  name="reflection"
                />
              </Col>
            </Row>
            <ProFormList
              name="matrices"
              label="核心经验"
              copyIconProps={false}
              // rules={[
              //   {
              //     required: true,
              //     message: '请添加核心经验',
              //   },
              // ]}
              alwaysShowItemLabel
              creatorButtonProps={{
                // position: 'top',
                creatorButtonText: '新增核心经验',
              }}
              {...(isDetail && { alwaysShowItemLabel: true })}
              // deleteIconProps={false}
            >
              {(meta, index, action) => {
                return (
                  <ProFormGroup key="matrices">
                    <ProFormSelect
                      rules={[
                        {
                          required: true,
                          message: '请选择领域',
                        },
                      ]}
                      width={'sm'}
                      name="matrix1Id"
                      label={isDetail ? '领域' : '请选择领域'}
                      placeholder="请选择领域"
                      onChange={(value, o) => action.setCurrentRowData({ matrix1Name: o?.title })}
                      showSearch
                      // fieldProps={{
                      //   labelInValue: true,
                      // }}
                      options={matrix}
                    />
                    <ProFormSelect
                      dependencies={['matrix1Id']}
                      width={'sm'}
                      rules={[
                        {
                          required: true,
                          message: '请选择维度',
                        },
                      ]}
                      placeholder={'请选择维度'}
                      showSearch
                      label={isDetail ? '维度' : '请选择维度'}
                      name="matrix2Id"
                      onChange={(value, o) => action.setCurrentRowData({ matrix2Name: o?.title })}
                      request={async (params) => {
                        if (!params?.matrix1Id) {
                          return [];
                        }
                        return await getLists({
                          pid: params.matrix1Id,
                          current: 1,
                          pageSize: 1000,
                        });
                      }}
                    />
                    <ProFormSelect
                      dependencies={['matrix2Id']}
                      showSearch
                      rules={[
                        {
                          required: true,
                          message: '请选择子维度',
                        },
                      ]}
                      label={isDetail ? '子维度' : '请选择子维度'}
                      width={'lg'}
                      placeholder="请选择子维度"
                      name="matrix3Id"
                      onChange={(value, o) => action.setCurrentRowData({ matrix3Name: o?.title })}
                      request={async (params) => {
                        if (!params?.matrix2Id) {
                          return [];
                        }
                        return await getLists({
                          pid: params.matrix2Id,
                          current: 1,
                          pageSize: 1000,
                        });
                      }}
                    />
                    <ProFormSelect
                      showSearch
                      width={'xl'}
                      dependencies={['matrix3Id']}
                      rules={[
                        {
                          required: true,
                          message: '请选择指标',
                        },
                      ]}
                      placeholder="请选择指标"
                      label={isDetail ? '指标' : '请选择指标'}
                      name="targetId"
                      onChange={(value, o) => action.setCurrentRowData({ targetName: o?.title })}
                      request={async (params) => {
                        console.log(params);

                        if (!params?.matrix3Id) {
                          return [];
                        }
                        const res = await getTargetList({
                          matrix3Id: params.matrix3Id,
                          current: 1,
                          pageSize: 1000,
                        });

                        return res;
                      }}
                    />
                  </ProFormGroup>
                );
              }}
            </ProFormList>

            <Row>
              <UploadImageComponent
                key="upload"
                fileName="headerId"
                label="照片"
                max={10}
                client={client}
                fileList={fileList}
                setClient={setClient}
                setFileList={setFileList}
                edit={!isDetail}
                fileCategory={230}
                // accept=".png,.jpg,.jpeg,.gif"
              />
            </Row>
          </Card>
        </ProForm>
      </Card>
    </PageContainer>
  );
};

export default UserForm;
