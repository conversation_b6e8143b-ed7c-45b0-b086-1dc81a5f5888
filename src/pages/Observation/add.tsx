import React from 'react';
import { useState, useEffect } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProForm,
  ProFormDateTimePicker,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Button, Form, message } from 'antd';
import UploadImageComponent from '@/components/UploadImage';
import { useModel } from '@umijs/max';
import { getChildList } from '@/services/child';
import { addAICoreExpList, addObservation } from '@/services/observation';
const AddForm = (props: { title: string; reload: () => void }) => {
  const [form] = Form.useForm<{ name: string; company: string }>();
  const [client, setClient] = useState<any>();
  const [headerList, setHeaderList] = useState<API.FileItem[]>([]);
  const { initialState } = useModel('@@initialState');
  const { ObservationRecordSceneEnumDesc } = initialState?.enums;
  //   const [curClassId, setCurClassId] = useState<number>(0);
  const [optionsList, setOptionsList] = useState<any[]>([]);
  const [isAI, setIsAI] = useState<boolean>(false);

  useEffect(() => {
    if (props.title === 'AI生成') setIsAI(true);
  }, [props.title]);
  // 求当前班级下的所有学生
  const requestClassStu = async () => {
    let list: any = [];
    let res = await getChildList({
      current: 1,
      pageSize: 999,
      classId: form.getFieldValue('schoolClassId') || initialState?.currentUser?.currentClassId,
    });
    if (res.status === 0) {
      list = res.data.map((item: any) => {
        return {
          value: item.id,
          label: item.title,
          classId: item.classId,
        };
      });
      setOptionsList((prevList) => [...prevList, ...list]);
    }
  };

  return (
    <ModalForm<{
      name: string;
      company: string;
    }>
      title={props.title}
      trigger={
        <Button type="link">
          <PlusOutlined />
          {props.title}
        </Button>
      }
      form={form}
      autoFocusFirstInput
      modalProps={{
        destroyOnHidden: true,
        onCancel: () => {
          // 清空表单
          form.resetFields();
          setOptionsList([]);
        },
        style: {
          top: 20, // 设置 Modal 距离顶部的距离
        },
      }}
      onOpenChange={(visible) => {
        // open 改变时触发
        if (visible) requestClassStu();
      }}
      submitTimeout={2000}
      onFinish={async (values) => {
        let fn = isAI ? addAICoreExpList : addObservation;
        let params = JSON.parse(JSON.stringify(values));
        params.picList = headerList.map((item) => item?.url || item?.uri);
        // 调整学生信息参数
        params.children = params.children.map((item: any) => {
          let cid = optionsList.find((stu) => stu.value === item)?.classId;
          return {
            childId: item,
            childName: optionsList.find((stu) => stu.value === item)?.label,
            classId: cid,
            className: initialState?.userClass.find((key) => key.id === cid)?.title,
          };
        });
        console.log(params);

        let res = await fn(params);
        if (res.status === 0) {
          message.success('添加成功');
          props?.reload();
          form.resetFields();
          setOptionsList([]);
          return true;
        }
        message.error(res?.message || '添加失败');
        return false;
      }}
    >
      <ProForm.Group>
        <ProFormText width="md" name="activityName" label="活动名称" rules={[{ required: true }]} />
        <ProFormSelect
          width="md"
          name="schoolClassId"
          label="记录班级"
          onChange={requestClassStu}
          placeholder="请选择观察班级"
          initialValue={initialState?.currentUser?.currentClassId}
          request={async () => {
            return initialState?.userClass.map((key) => {
              return {
                value: key.id,
                label: key.title,
              };
            });
          }}
        />
        <ProFormSelect
          request={async () => {
            return Object.keys(ObservationRecordSceneEnumDesc).map((key) => {
              return {
                value: key,
                label: ObservationRecordSceneEnumDesc[key],
              };
            });
          }}
          width="md"
          name="observationType"
          label="记录场景"
          placeholder="请选择记录场景"
          rules={[{ required: true }]}
        />

        <ProFormSelect
          width="md"
          name="children"
          label="观察对象"
          mode="multiple"
          placeholder="请选择观察对象"
          rules={[{ required: true }]}
          options={optionsList}
        />
      </ProForm.Group>

      <ProForm.Group>
        <ProFormDateTimePicker
          width="md"
          name="observationTime"
          label="观察时间"
          rules={[{ required: true }]}
          placeholder="请输入观察时间"
        />
        <ProFormText
          width="md"
          name="observationLocation"
          label="观察地点"
          placeholder="请输入观察地点"
        />
      </ProForm.Group>

      <ProForm.Group>
        {isAI ? (
          <ProFormTextArea
            width="md"
            name="activityOutline"
            fieldProps={{
              style: {
                height: '150px', // 设置文本框高度为 200px
              },
            }}
            label="活动梗概"
            rules={[{ required: true }]}
            placeholder="幼儿在什么地方，用什么材料，做了什么，怎么做的，做了多长时间；幼儿遇到了什么问题，如何解决的；幼儿对谁说了什么；幼儿表情如何，有什么变化等。"
          />
        ) : (
          <>
            <ProFormTextArea width="md" name="productDesc" label="材料" placeholder="请输入材料" />
            <ProFormTextArea
              width="md"
              name="observationBackground"
              label="背景"
              placeholder="请输入背景"
            />
            <ProFormTextArea
              width="md"
              name="observationPurpose"
              label="目的"
              placeholder="请输入目的"
            />
            <ProFormTextArea
              width="md"
              name="observationContent"
              label="观察内容"
              placeholder="请输入观察内容"
            />
            <ProFormTextArea width="md" name="analysis" label="分析" placeholder="请输入分析" />
            <ProFormTextArea width="md" name="support" label="支持" placeholder="请输入支持" />
            <ProFormTextArea width="md" name="reflection" label="反思" placeholder="请输入反思" />
          </>
        )}
        <UploadImageComponent
          key="upload"
          fileName="picList"
          label="照片"
          rules={[{ required: true }]}
          client={client}
          fileList={headerList}
          setClient={setClient}
          setFileList={setHeaderList}
          accept=".png,.jpg,.jpeg,.gif"
          fileCategory={230}
        />
        {isAI ? (
          <span style={{ color: 'rgb(192, 196, 204)' }}>
            照片将决定AI识别的准确度，建议上传如下几张照片：场景和材料、多张幼儿游戏过程、幼儿遇到的问题、幼儿表情、幼儿作品等。
          </span>
        ) : (
          ''
        )}
      </ProForm.Group>
    </ModalForm>
  );
};
export default AddForm;
