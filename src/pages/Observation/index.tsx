// 观察记录
import { getObsList, delObsItem } from '@/services/api';
import { exportTemplate } from '@/services/apis';
import { downloadWithUrl } from '@/services/utils';
import type { ActionType } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, message, Modal } from 'antd';
import React, { useRef, useState, useCallback } from 'react';
import { useModel } from '@umijs/max';
import TemplateList from '@/pages/Class/TemplateList';
import { useModal } from '@/components/UseModal';
import AddModal from './add';
const Observation: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const { initialState } = useModel('@@initialState');
  const [AntModal, showModal, hideModal] = useModal();
  const [activeRows, setActiveRows] = useState<any[]>();
  const templateRef = useRef<any>(null);

  // 新增配置项

  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '活动名称',
      dataIndex: 'activityName',
      valueType: 'text',
      width: 120,
    },
    {
      title: '参与学生',
      dataIndex: 'children',
      valueType: 'text',
      width: 120,
      search: false,
      render: (result: any) =>
        result?.length ? result.map((item: any) => item.childName).join('，') : '-',
    },
    {
      title: '观察班级',
      dataIndex: 'children',
      valueType: 'text',
      width: 120,
      search: false,
      render: (result: any) => {
        let list = result?.map((item: any) => item.className).join('，');
        // 去重
        list = Array.from(new Set(list.split('，'))).join('，');
        return list?.length ? list : '-';
      },
    },
    {
      title: '观察地点',
      dataIndex: 'observationLocation',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '观察老师',
      dataIndex: 'createdByName',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '观察日期',
      dataIndex: 'observationTime',
      valueType: 'text',
      width: 100,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'state',
      valueType: 'select',
      width: 100,
      valueEnum: {
        1: {
          text: '激活',
        },
        2: {
          text: '生成中',
        },
        3: {
          text: '生成失败',
        },
      },
      renderText: (text: any) => {
        console.log(text, 'text');
        let map = {
          0: '关闭',
          1: '激活',
          2: '生成中',
          3: '生成失败',
        };
        return map[text];
      },
    },
    {
      title: '操作',
      fixed: 'right',
      align: 'center',
      width: 200,
      search: false,
      render(_, entity: any) {
        return (
          <div style={{ display: 'flex', gap: '10px' }}>
            <Button
              type="link"
              key="deatails"
              size="small"
              href={`/GameManager/observation/Detail/${entity.id}`}
              disabled={entity.state !== 1}
            >
              详情
            </Button>
            <Button
              type="link"
              key="export"
              size="small"
              disabled={entity.state !== 1}
              onClick={(e) => {
                e.stopPropagation();
                setActiveRows([entity]);
                showModal();
              }}
            >
              导出wrod
            </Button>
            <Button
              type="link"
              key="delete"
              size="small"
              danger
              disabled={entity.state !== 1}
              onClick={() => {
                Modal.confirm({
                  title: '是否确认删除',
                  onOk() {
                    delObsItem(entity.id).then((res) => {
                      if (res.status === 0) {
                        message.success('删除成功');
                        actionRef?.current?.reload();
                      }
                    });
                  },
                });
              }}
            >
              删除
            </Button>
          </div>
        );
      },
    },
  ];
  const onConfirmDownload = useCallback(() => {
    const templateId = templateRef.current.templateId;
    if (!templateId) {
      message.warning('未找到模板');
      return;
    }
    exportTemplate({
      id: templateId,
      observationRecordId: activeRows?.map((v) => v.id)[0],
    }).then((response) => {
      if (response.status === 0) {
        const data = response.data;
        downloadWithUrl(data.uri, data.filename);
        hideModal();
      }
    });
  }, [activeRows]);

  return (
    <PageContainer>
      <ProTable<API.PageParams>
        actionRef={actionRef}
        rowKey="ids"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        toolBarRender={() => {
          return [
            <AddModal key='AI' title="AI生成" reload={actionRef?.current?.reload} />,
            <AddModal key='add' title="手动生成" reload={actionRef?.current?.reload} />,
          ];
        }}
        expandable={{
          childrenColumnName: 'abs', // 数据与该配置项冲突，暂不使用
        }}
        // columnsState={{
        //   defaultValue: {
        //     note: {
        //       show: false,
        //     },
        //   },
        // }}
        request={async (params) => {
          const { currentClassId, currentSchoolId } = initialState?.currentUser || {};
          let obj = {
            pageNo: params.current,
            pageSize: params.pageSize,
            pageModel: {
              schoolClassId: currentSchoolId,
              classId: currentClassId,
              activityName: params.activityName,
              state: Number(params.state),
            },
          };

          const response = await getObsList(obj);
          return {
            data: response?.data,
            success: true,
          };
        }}
        columns={columns}
        scroll={{ x: 1000 }}
      />
      <AntModal onOk={() => onConfirmDownload()} title="选择模版" width={'80%'}>
        <TemplateList ref={templateRef} index={1} />
      </AntModal>
      
    </PageContainer>
  );
};

export default Observation;
