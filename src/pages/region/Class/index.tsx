// 班级区域
import {
  getRegionClassList,
  getRegionClassNameList,
  addRegionClassNameItem,
  editRegionClassNameItem,
  deleteRegionClassNameItem,
} from '@/services/api';
import {
  PageContainer,
  ProTable,
  ModalForm,
  ProFormText,
  ProFormSelect,
  ProFormDigit,
} from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';
import { Button, message, Form, Modal, Image } from 'antd';
import { useModel } from '@umijs/max';
import UploadImageComponent from '@/components/UploadImage';
const ClassRegion = () => {
  const formRef = useRef<ProFormInstance>();
  const { initialState } = useModel('@@initialState');
  const [form] = Form.useForm();
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<any>({});
  const [client, setClient] = useState<any>(null);
  const [imageList, setImageList] = useState<any[]>([]);
  let { currentClassId, currentSchoolId } = initialState?.currentUser || {};
  console.log('currentClassId', initialState);
  // 处理保存
  const handleSave = async (values: any) => {
    try {
      const params = {
        ...values,
        classId: currentClassId,
        schoolId: currentSchoolId,
        teacherId: initialState?.currentUser?.id || '',
        areaImg: imageList[0]?.url || '',
      };
      if (currentRow.id) params.id = currentRow.id;

      let fn = currentRow.id ? editRegionClassNameItem : addRegionClassNameItem;
      const response = await fn(params);
      if (response?.status === 0) {
        message.success(currentRow.id ? '修改成功' : '新增成功');
        setModalOpen(false);
        // 刷新页面
        formRef.current?.reload();
        return true;
      } else {
        message.error(response?.message || '操作失败');
        return false;
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('操作失败');
      return false;
    }
  };
  // 处理编辑
  const handleEdit = (record: any) => {
    console.log(record);

    setCurrentRow(record);
    form.setFieldsValue(record);
    setModalOpen(true);
    // // 如果有图片ID，构造图片列表数据
    if (record.areaImg) {
      // 构造图片数据，兼容 imageUrl 字段
      const imageData = {
        id: record.id,
        uri: record.areaImg || '', // 优先使用 imageUrl
        filename: `mood_image_${record.id}`,
        uid: record.id,
        hash: record.id,
      };
      setImageList([imageData]);
    } else {
      setImageList([]);
    }
    // form.setFieldsValue(record);
    // setModalOpen(true);
  };
  // 处理删除
  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除该"${record.area}"吗？此操作不可恢复。`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          const response = await deleteRegionClassNameItem({ id: record.id });
          if (response?.status === 0) {
            message.success('删除成功');
            formRef.current?.reload();
          } else {
            message.error(response?.message || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败');
        }
      },
    });
  };

  // 新增配置项
  const columns = [
    {
      title: '区域图片',
      dataIndex: 'areaImg',
      valueType: 'text',
      width: 100,
      search: false,
      render: (_, entity: any) => [
        <>{entity?.areaImg ? <Image src={entity.areaImg} alt="" height={80} width={80} /> : ''}</>,
      ],
    },
    {
      title: '区域',
      dataIndex: 'area',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '昵称',
      dataIndex: 'areaAlias',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '区域材料',
      dataIndex: 'materialNum',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '限定人数',
      dataIndex: 'limitNum',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'action',
      valueType: 'text',
      width: 120,
      search: false,
      render: (_, record: any) => [
        <Button
          onClick={() => handleEdit(record)}
          size="small"
          type="primary"
          key="edit"
          target="_blank"
          style={{ marginRight: 20 }}
        >
          编辑
        </Button>,
        <Button
          onClick={() => handleDelete(record)}
          size="small"
          color="danger"
          key="delete"
          target="_blank"
        >
          删除
        </Button>,
      ],
    },
  ];
  return (
    <PageContainer>
      <ProTable
        actionRef={formRef}
        rowKey="id"
        search={false}
        // 工具栏 默认谁关闭
        // columnsState={{
        //   defaultValue: {
        //     note: {
        //       show: false,
        //     },
        //   },
        // }}
        toolBarRender={() => [
          <Button key="add" type="primary" onClick={() => setModalOpen(true)}>
            添加区域
          </Button>,
        ]}
        // 表格导航栏
        request={async (values: any) => {
          let params = {
            classId: Number(currentClassId),
            pageNo: 1,
            pageSize: 999,
          };
          let res = await getRegionClassList(params);
          if (res.status === 0) {
            let data = res?.data?.records || [];
            return {
              data: data || [],
              success: true,
            };
          }
        }}
        columns={columns}
        // scroll={{ x: 1000 }}
      />
      <ModalForm
        title={currentRow.id ? '编辑区域' : '新建区域'}
        width="600px"
        form={form}
        open={modalOpen}
        modalProps={{
          maskClosable: false,
          destroyOnClose: true,
        }}
        onOpenChange={(open) => {
          setModalOpen(open);
          if (!open) {
            setCurrentRow({});
            setImageList([]);
            form.resetFields();
          }
        }}
        onFinish={handleSave}
      >
        <ProFormSelect
          name="area"
          label="区域名称(如无对应区域请联系客服添加)"
          rules={[{ required: true }]}
          //   options={[{ label: '自定义心情', value: 5 }]}
          //   options={[]}
          request={async () => {
            let res = await getRegionClassNameList({ classId: Number(currentClassId) });
            if (res.status === 0) {
              let data = res?.data || [];
              return data.map((item: any) => {
                return {
                  label: item,
                  value: item,
                };
              });
            }
          }}
          //   initialValue={5}
        />

        <ProFormText name="areaAlias" label="区域昵称(在幼儿选区页面展示)" />

        <UploadImageComponent
          key="upload"
          fileName="imageId"
          label="区域图标(在幼儿选区页面展示)"
          max={0}
          client={client}
          fileList={imageList}
          setClient={setClient}
          setFileList={setImageList}
          accept=".png,.jpg,.jpeg,.gif"
        />

        <ProFormDigit
          name="limitNum"
          label="区域限定人数(最大20人)"
          placeholder="请输入区域限定人数"
          min={1}
          max={20}
          rules={[{ required: true }]}
        />
      </ModalForm>
    </PageContainer>
  );
};
export default ClassRegion;
