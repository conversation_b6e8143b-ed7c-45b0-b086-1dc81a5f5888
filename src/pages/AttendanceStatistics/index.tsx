/* eslint-disable guard-for-in */

import { getStatisticsList, exportStatisticsExcel } from '@/services/api';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, Popover, message } from 'antd';
import React, { useRef, useState, useEffect } from 'react';
import { useModel } from '@umijs/max';
import dayjs from 'dayjs';
const WeeklyReport = () => {
  const formRef = useRef<any>();
  const { initialState } = useModel('@@initialState');
  let { currentSchoolId } = initialState?.currentUser || {};

  // 通过dayjs获取本周和上周的日期 格式为 2020-01-01 - 2020-01-07
  let dayJs = dayjs();

  // 新增配置项
  const columns = [
    {
      title: '班级',
      dataIndex: 'className',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '应出勤',
      dataIndex: 'expectedCount',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '今日出勤',
      dataIndex: 'actualCount',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '事假',
      dataIndex: 'personalLeaveCount',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '病假',
      dataIndex: 'sickLeaveCount',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '病假事由',
      dataIndex: 'sickLeaveReason',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '日期',
      search: true,
      hidden: true,
      dataIndex: 'attendanceDate',
      valueType: 'date',
      fieldProps: { placeholder: '请选择日期' },
      initialValue: dayJs.format('YYYY-MM-DD'), // 默认选中上周
      //   filterResetToDefaultFilteredValue: true,
      //   request: async () => {
      //     let res = await getListWeek();
      //     if (res.status === 0) {
      //       const result = res?.data?.map((item: any) => {
      //         return { label: item, value: item };
      //       });
      //       return result || [];
      //     }
      //   },
    },
  ];

  return (
    <PageContainer>
      <ProTable
        formRef={formRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        // 工具栏 默认谁关闭
        // columnsState={{
        //   defaultValue: {
        //     note: {
        //       show: false,
        //     },
        //   },
        // }}
        // 表格导航栏
        toolBarRender={() => {
          return [
            <Button
              onClick={async () => {
                let { attendanceDate } = formRef?.current?.getFieldsValue();
                let time = dayjs(attendanceDate).format('YYYY-MM-DD');
                let data = {
                  attendanceDate,
                  schoolId: currentSchoolId,
                };
                // 导出Excel
                let response = await exportStatisticsExcel(data);
                console.log(response, 'response');
                const url = URL.createObjectURL(response.data);
                const a = document.createElement('a');
                a.href = url;
                a.download = `班级出勤汇总_${time}.xlsx`
                a.click();
                URL.revokeObjectURL(url); // 释放资源

                // showModal();
                // setActiveRows(data?.subjectActivities);
              }}
              type="primary"
              key={'exportAll'}
            >
              导出Excel
            </Button>,
          ];
        }}
        request={async (values: any) => {
          let params = {
            schoolId: currentSchoolId,
            attendanceDate: values.attendanceDate || dayJs.format('YYYY-MM-DD'),
          };
          let res = await getStatisticsList(params);
          if (res.status === 0) {
            return {
              data: res?.data || [],
              success: true,
            };
          }
        }}
        columns={columns}
        // scroll={{ x: 1000 }}
      />
    </PageContainer>
  );
};

export default WeeklyReport;
