import {
  fetchUserClasses,
  fetchUserSchools,
  refreshState,
  selectUserSchoolsAndClasses,
} from '@/services/apis';
import { PageContainer } from '@ant-design/pro-layout';
import { useModel } from '@umijs/max';
import { Avatar, Button, Card, Col, Divider, message, Modal, Row, Select, Typography } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';

/**
 * steps
 * - 获取用户信息
 * - 获取已绑定的学校
 * - 获取已选择的学校 如果有则获取已绑定的班级
 *
 * 操作
 * - 选择学校 绑定学校同时清空已选择的班级  绑定成功之后获取班级列表
 * - 选择班级 绑定班级
 */
interface WelcomeState {
  schools: [];
  classes: [];
  terms: [];
}
const Welcome: React.FC = () => {
  const [config, setConfig] = useState<WelcomeState>({
    schools: [],
    classes: [],
    terms: [],
  });

  const { initialState, setInitialState } = useModel('@@initialState');
  /**
   * 更新班级信息
   */
  const updateClass = useCallback(() => {
    fetchUserClasses().then((res) => {
      setConfig((c) => ({
        ...c,
        classes: (res.data || []).map((v: any) => ({
          label: `${v.title}${v.nickname ? '-' + v.nickname : ''}`,
          value: v.id,
          key: v.id,
        })),
      }));
    });
  }, []);


  useEffect(() => {
    try {
      /** 获取已绑定的学校 */
      fetchUserSchools().then((res) => {
        const schools = res.data || [];
        if (!schools.length) {
          Modal.warning({
            title: '提示',
            content: '请先绑定学校',
          });
          return;
        }
        setConfig((c) => ({
          ...c,
          schools: (res.data || []).map((v: any) => ({ label: v.title, value: v.id, key: v.id })),
        }));
      });
      // 如果有学校，获取班级列表
      if (initialState?.currentUser?.currentSchoolId) {
        updateClass();
      }
    } catch (error) {
      console.log(error);
    }
  }, []);

  /** 设置当前选择学校 */
  const onSetCurrentSchool = useCallback(
    (value: number) => {
      selectUserSchoolsAndClasses({
        currentSchoolId: value,
        currentClassId: null,
        currentTerm: null,
      }).then((response) => {
       
        if (response.status === 0) {
          setInitialState(
            (i) =>
              ({
                ...i,
                currentUser: {
                  ...i?.currentUser,
                  currentSchoolId: value,
                  currentClassId: null,
                  currentTerm: null,
                },
              } as any),
          );
          updateClass();
        }
      });
    },
    [config],
  );
  /** 设置当前选择班级 */
  const onSetCurrentClass = useCallback(
    (value: number) => {
      selectUserSchoolsAndClasses({
        currentSchoolId: initialState?.currentUser?.currentSchoolId,
        currentClassId: value,
        currentTerm: null,
      }).then((response) => {
        const current = initialState?.userClass?.find(u => u.id === value)
        if (response.status === 0) {
          setInitialState(
            (i) =>
              ({
                ...i,
                currentClass: [value, current.term],
                currentUser: { ...i?.currentUser, currentClassId: value, currentTerm: null },
              } as any),
          );
        }
      });
    },
    [config],
  );



  const onRefreshState = () => {
    refreshState().then((r) => {
      if (r.status === 0) {
        message.success('更新成功');
      }
    });
  };
  return (
    <PageContainer>
      <Row gutter={24}>
        <Col span={12}>
          <Card style={{ height: '500px' }}>
            <Divider orientation="left">用户信息</Divider>
            <Row align={'middle'} gutter={24}>
              <Col
                span={8}
                style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              >
                <Avatar
                  size={60}
                  src="https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png"
                />
              </Col>
              <Col span={16}>
                <Row style={{ height: '60px', display: 'flex', alignItems: 'center' }}>
                  <Typography.Text strong>{initialState?.currentUser?.nickname}</Typography.Text>
                </Row>
                <Row style={{ height: '60px', display: 'flex', alignItems: 'center' }}>
                  登录名:&nbsp;&nbsp; {initialState?.currentUser?.mobile}
                </Row>
                <Row style={{ height: '60px', display: 'flex', alignItems: 'center' }}>
                  账号状态:&nbsp;&nbsp; {initialState?.currentUser?.state === 1 ? '激活' : '已停用'}
                </Row>
                <Row style={{ height: '60px', display: 'flex', alignItems: 'center' }}>
                  登录状态:
                  <Button onClick={() => onRefreshState()} type="link">
                    刷新登录状态
                  </Button>
                </Row>
              </Col>
            </Row>
          </Card>
        </Col>
        <Col span={12}>
          <Card style={{ height: '500px' }}>
            <Divider orientation="left">切换当前学校</Divider>
            <Row align={'middle'} style={{ width: '100%', height: '80px' }}>
              <Typography.Text>学校: </Typography.Text>
              &nbsp;&nbsp;
              <Select
                style={{ width: '200px' }}
                onChange={onSetCurrentSchool}
                value={initialState?.currentUser?.currentSchoolId}
                placeholder="请选择学校"
                options={config.schools}
              ></Select>
            </Row>
            {initialState?.currentUser?.currentSchoolId ? (
              <Row align={'middle'} style={{ width: '100%', height: '80px' }}>
                <Typography.Text>班级: </Typography.Text> &nbsp;&nbsp;
                <Select
                  style={{ width: '200px' }}
                  onChange={onSetCurrentClass}
                  value={initialState?.currentUser?.currentClassId}
                  placeholder="请选择班级"
                  options={config.classes}
                ></Select>
              </Row>
            ) : null}
          </Card>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default Welcome;
