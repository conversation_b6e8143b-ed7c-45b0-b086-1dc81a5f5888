import {
  fetchAreaProductList,
  fetchAreaList,
  queryAreaProductList,
  deleteAreaProduct,
} from '@/services/classAreaProduct';
import { DictionaryCategory } from '@/services/constants';
// import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Button, Image, message, Tag, Popconfirm } from 'antd';
import { useEffect, useRef, useState } from 'react';

import { fetchUserClasses } from '@/services/apis';

export const getInitAreaProductMatrixProps = () => ({
  matrix1Id: 0,
  matrix1Title: '',
  matrix2Id: 0,
  matrix2Title: '',
  matrix3Id: 0,
  matrix3Title: '',
});

const ClassAreaProduct = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const { initialState } = useModel('@@initialState');
  const classOptions = useRef<any[]>([]);
  const [areaOptions, setAreaOptions] = useState<any[]>([]);

  // 保留原有的字典区域选项作为备用
  const dictionaryAreaOptions = initialState?.dictionaryList?.filter(
    (f) => f.category === DictionaryCategory.Area,
  );

  // 获取区域列表的函数
  const loadAreaOptions = async (classId?: number, schoolId?: number) => {
    console.log('loadAreaOptions 调用参数:', { classId, schoolId });

    if (classId && schoolId) {
      try {
        console.log('调用 fetchAreaList 接口:', { classId, schoolId });
        const res = await fetchAreaList({ classId, schoolId });
        console.log('fetchAreaList 返回结果:', res);

        if (res.data) {
          const options = res.data.map((item: any) => ({
            label: item.areaAlias || item.area,
            value: item.id,
          }));
          // 手动添加"全部"选项，id 为 -1
          const allOptions = [{ label: '全部', value: -1 }, ...options];
          console.log('设置区域选项:', allOptions);
          setAreaOptions(allOptions);
        }
      } catch (error) {
        console.error('获取区域列表失败:', error);
        // 如果接口失败，使用字典数据作为备用
        const backupOptions =
          dictionaryAreaOptions?.map((item) => ({
            label: item.label,
            value: item.id,
          })) || [];
        setAreaOptions([{ label: '全部', value: -1 }, ...backupOptions]);
      }
    } else {
      console.log('使用字典数据作为区域选项');
      // 如果没有classId或schoolId，使用字典数据
      const defaultOptions =
        dictionaryAreaOptions?.map((item) => ({
          label: item.label,
          value: item.id,
        })) || [];
      setAreaOptions([{ label: '全部', value: -1 }, ...defaultOptions]);
    }
  };

  useEffect(() => {
    console.log('组件初始化，当前用户信息:', initialState?.currentUser);

    fetchUserClasses().then((res) => {
      console.log('获取班级列表结果:', res);
      classOptions.current = [
        { label: '全部', value: 0 },
        ...res.data.map((d: any) => ({ label: d.title, value: d.id })),
      ];
    });

    // 初始化时加载区域选项
    console.log('初始化加载区域选项');
    loadAreaOptions();
  }, []);

  const handleDelete = async (record: any) => {
    try {
      const deleteParams = {
        id: record.id,
        classId: record.classId || initialState?.currentUser?.currentClassId,
        sourceType: record.sourceType,
      };
      console.log('删除参数:', deleteParams);
      console.log('记录数据:', record);

      await deleteAreaProduct(deleteParams);
      message.success('删除成功');
      actionRef.current?.reload();
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败，请重试');
    }
  };
  const baseColumn: ProColumns<any, 'text'>[] = [
    {
      title: '操作',
      ellipsis: true,
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: '6em',
      search: false,
      fixed: 'right',
      render: (_, record) => [
        // <Button
        //   size="small"
        //   type="primary"
        //   key="edit"
        //   onClick={() => {
        //     window.open(`/ClassAreaProduct/edit/${record?.id}`, '_blank');
        //   }}
        // >
        //   编辑
        // </Button>,
        <Popconfirm
          key="delete"
          title="确认删除"
          description={`确定要删除材料"${record.name}"吗？此操作不可撤销。`}
          okText="是"
          cancelText="否"
          onConfirm={async () => {
            await handleDelete(record);
          }}
        >
          <Button
            size="small"
            type="primary"
            danger
          >
            删除
          </Button>
        </Popconfirm>,
        // <Button
        //   size="small"
        //   type="link"
        //   key="detail"
        //   href={`/ClassAreaProduct/detail/${record?.id}`}
        //   target="_blank"
        // >
        //   查看详情
        // </Button>,
      ],
    },

    {
      title: '区域',
      width: 100,
      dataIndex: 'areaId',
      valueType: 'select',
      fieldProps: {
        options: areaOptions,
        placeholder: '请选择区域',
      },
      renderText(_, record) {
        // 优先显示 area 字段，如果没有则通过 areaId 匹配
        if (record.area) {
          return record.area;
        }
        const t = areaOptions?.find((f) => f.value === record.areaId);
        return t ? t.label : _;
      },
      // sorter: (a, b) => {
      //   return a.areaId > b.areaId ? 1 : -1;
      // },
    },
    {
      title: '材料名称',
      dataIndex: 'name',
      width: 160,
    },
    {
      title: '玩法数量',
      dataIndex: 'playCount',
      width: 80,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'playStatus',
      width: 100,
      valueType: 'select',
      valueEnum: {
        '-1': '未生成',
        '0': '生成中',
        '99': '生成异常',
      },
      search: false, // 隐藏筛选表单中的状态筛选
      render(_, record) {
        if (record.playStatus < 0) {
          return <Tag color="default">未生成</Tag>;
        } else if (record.playStatus === 0) {
          return <Tag color="processing">生成中</Tag>;
        } else if (record.playStatus === 99) {
          return <Tag color="error">生成异常</Tag>;
        }
        return <Tag color="success">生成完成</Tag>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
      search: false,
      sorter: (a, b) => {
        return new Date(a.createTime).getTime() - new Date(b.createTime).getTime();
      },
    },
    {
      title: '预览图',
      dataIndex: 'displayedImage',
      width: 120,
      search: false,
      render(_, record) {
        return record.displayedImage ? (
          <Image width={'auto'} height={40} src={record.displayedImage} />
        ) : null;
      },
    },
  ];

  return (
    <PageContainer style={{ background: '#fff' }}>
      <ProTable<any, API.PageParams>
        actionRef={actionRef}
        formRef={formRef}
        rowKey="id"
        scroll={{ y: 500, x: 'max-content' }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 10,
        }}
        expandable={{ showExpandColumn: false }}
        form={{
          initialValues: {
            classId: initialState?.currentUser?.currentClassId || undefined,
            areaId: -1,
          },
        }}
        toolBarRender={() => [
          // <Button
          //   size="small"
          //   type="link"
          //   key="detail"
          //   href={`/ClassAreaProduct/edit/add`}
          //   target="_blank"
          // >
          //   <PlusOutlined /> 新建
          // </Button>,
          // <Button
          //   size="small"
          //   type="primary"
          //   key="statictis"
          //   onClick={() => {
          //     const classId = formRef.current?.getFieldValue('classId') || '';
          //     window.open(`/ClassAreaProduct/statictics?classId=${classId}`);
          //   }}
          //   target="_blank"
          // >
          //   统计
          // </Button>,
        ]}
        request={(params) => {
          console.log('ProTable request 原始参数:', params);
          const p: any = { ...params };

          // 确保传递 schoolId 参数
          if (!p.schoolId) {
            p.schoolId = initialState?.currentUser?.currentSchoolId;
          }

          // areaId 默认传 -1（全部）
          if (!p.areaId) {
            p.areaId = -1;
          }

          // 处理 classId 参数
          if (!p.classId || p.classId === 0) {
            // 如果没有classId或者是0（全部），使用当前用户的班级ID
            p.classId = initialState?.currentUser?.currentClassId;
          }

          console.log('处理后的参数:', p);
          console.log('当前用户信息:', initialState?.currentUser);

          // 检查必需参数 - 使用更严格的验证
          if (
            !p.classId ||
            !p.areaId ||
            !p.schoolId ||
            p.classId === undefined ||
            p.areaId === undefined ||
            p.schoolId === undefined
          ) {
            console.log('参数验证失败，缺少必需参数:', {
              classId: p.classId,
              areaId: p.areaId,
              schoolId: p.schoolId,
            });
            // 如果缺少必需参数，返回空结果
            return Promise.resolve({
              data: [],
              total: 0,
              success: true,
            });
          }

          // 判断是否有筛选条件（区域或材料名称）
          const hasFilter = (p.areaId && p.areaId !== -1) || p.name;

          if (hasFilter) {
            // 有筛选条件时使用 queryAreaProductList
            const queryParams = {
              classId: p.classId,
              areaId: p.areaId,
              schoolId: p.schoolId,
              materialName: p.name, // 将name参数转换为materialName
            };
            console.log('调用 queryAreaProductList 接口，参数:', queryParams);
            return queryAreaProductList(queryParams)
              .then((res) => {
                console.log('queryAreaProductList 返回结果:', res);
                // 展平嵌套的数据结构
                const flatData: any[] = [];
                const seenIds = new Set(); // 用于去重

                if (res.data && Array.isArray(res.data)) {
                  res.data.forEach((areaItem: any) => {
                    if (
                      areaItem.teacherCombinedMaterialVOList &&
                      Array.isArray(areaItem.teacherCombinedMaterialVOList)
                    ) {
                      areaItem.teacherCombinedMaterialVOList.forEach((material: any) => {
                        // 根据材料ID去重
                        if (!seenIds.has(material.id)) {
                          seenIds.add(material.id);
                          flatData.push(material);
                        }
                      });
                    }
                  });
                }

                console.log('展平并去重后的数据:', flatData);
                return {
                  data: flatData,
                  total: flatData.length,
                  success: true,
                };
              })
              .catch((error) => {
                console.error('queryAreaProductList 调用失败:', error);
                throw error;
              });
          } else {
            // 无筛选条件时使用 fetchAreaProductList
            console.log('调用 fetchAreaProductList 接口，参数:', p);
            return fetchAreaProductList(p)
              .then((res) => {
                console.log('fetchAreaProductList 返回结果:', res);
                return res;
              })
              .catch((error) => {
                console.error('fetchAreaProductList 调用失败:', error);
                throw error;
              });
          }
        }}
        columns={baseColumn}
      />
    </PageContainer>
  );
};

export default ClassAreaProduct;
