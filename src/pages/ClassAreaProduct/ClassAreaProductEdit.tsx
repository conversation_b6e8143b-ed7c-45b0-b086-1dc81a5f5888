import MatrixSelect from '@/components/MatrixSelect';
import AntForms, { FormFields, FormTypes } from '@/components/UseForms';
import { useCategorySelect } from '@/components/UseForms/categorySelect';
import { useModal } from '@/components/UseModal';
import { addDictionary, fetchUserClasses } from '@/services/apis';
import {
  addAreaProduct,
  fetchAreaProductDetail,
  updateAreaProduct,
} from '@/services/classAreaProduct';
import { DictionaryCategory } from '@/services/constants';
import { useGetEnumOptionsByKey } from '@/services/enums';
import { DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { PageContainer, ProForm, ProFormField } from '@ant-design/pro-components';
import { history, useModel, useParams } from '@umijs/max';
import { Button, message, Row } from 'antd';
import { useForm } from 'antd/es/form/Form';
import TextArea from 'antd/es/input/TextArea';
import { useCallback, useEffect, useRef, useState } from 'react';

function sleep(time: number) {
  return new Promise((resolve) => {
    setTimeout(resolve, time);
  });
}

const dictNameMaps: { [key: string]: string } = {
  [DictionaryCategory.Brand]: '品牌',
  [DictionaryCategory.Tag]: '标签',
  [DictionaryCategory.Material]: '材质',
};

const ClassAreaProductDetail = () => {
  const [form] = useForm();
  const [matrixForm] = useForm();
  const [modalForm] = useForm();
  const params = useParams();
  const { initialState } = useModel('@@initialState');

  const [brandSetect, updateBrandSelect, unshiftBrandSelect] = useCategorySelect({
    label: '品牌',
    name: 'brandId',
    category: DictionaryCategory.Brand,
  });

  const [materialSelect, updateMaterialSelect, unshiftMaterialSelect] = useCategorySelect({
    label: '材质',
    name: 'material',
    category: DictionaryCategory.Material,
    flex: 1,
  });

  const [tagSelect, updateTagSelect, unshiftTagSelect] = useCategorySelect({
    label: '标签',
    name: 'tagIds',
    mode: 'multiple',
    flex: 4,
    category: DictionaryCategory.Tag,
  });

  const [games, setGames] = useState<{ game: string; teacher: string }[]>([]);

  const isAdd = params.id === 'add';

  const [originData, setOriginData] = useState<any>({});
  // const isAdd  = params.id === 'add'
  const [SubjectActivityInterestScoreEnumDesc] = useGetEnumOptionsByKey([
    'SubjectActivityInterestScoreEnumDesc',
  ]);

  const [modalName, setModalName] = useState<DictionaryCategory>(DictionaryCategory.Brand);
  const [AntModal, showModal, hideModal] = useModal();
  const [isLoading, setIsLoading] = useState(false);

  const classOptions = useRef<any[]>([]);

  const onSave = useCallback(async () => {
    setIsLoading(true);
    try {
      const r = await matrixForm.validateFields();
      const fn = isAdd ? addAreaProduct : updateAreaProduct;
      const formData = form.getFieldsValue() || {};

      await sleep(0);

      const rs = await fn({
        ...originData,
        ...formData,
        classId: initialState?.currentUser?.currentClassId,
        gameplay: games,
        headerIds: formData.headers?.map((item: any) => item?.id),
        resourceIds: formData.resources?.map((item: any) => item?.id),
        ...r,
        header: null,
        headerId: null,
      });
      message.success(rs.message || '保存成功');
      history.replace(`/ClassAreaProduct/detail/${params.id || rs.data.id}`);
    } catch (_) {}
    setIsLoading(false);
  }, [games, originData]);

  const onAddCategory = useCallback(() => {
    const fields = modalForm.getFieldsValue();
    if (fields.value) {
      addDictionary({
        value: fields.value,
        category: modalName,
        sort: 0,
      }).then((r) => {
        const objVal = { ...r, label: r.data.value, value: r.data.id };
        const fn: any =
          modalName === DictionaryCategory.Brand
            ? unshiftBrandSelect
            : modalName === DictionaryCategory.Tag
            ? unshiftTagSelect
            : unshiftMaterialSelect;
       
            fn([objVal]);

        switch (modalName) {
          case DictionaryCategory.Brand:
            form.setFieldValue('brandId', objVal.value);
            break;
          case DictionaryCategory.Material:
            form.setFieldValue('material', objVal.value);
          default:
          case DictionaryCategory.Tag:
            form.setFieldValue(
              'tagIds',
              Array.from(new Set([objVal.value, ...form.getFieldsValue().tagIds])),
            );
            break;
        }

        message.success(r.message || '保存成功');

        hideModal();
      });
    } else {
      message.warning('请输入名称');
    }
  }, [modalName]);

  const [childFormData] = useState({});

  const childForms: FormFields = [
    {
      label: '材料名称',
      name: 'title',
      formType: FormTypes.input,
      rules: [{ required: true }],
    },
    {
      label: '淘宝名称',
      name: 'taobaoName',
      formType: FormTypes.input,
    },
    {
      label: '淘宝链接',
      name: 'taobaoLink',
      formType: FormTypes.input,
    },

    [
      brandSetect,
      {
        label: '',
        name: 'brandPlus',
        formType: FormTypes.render,
        render() {
          return (
            <PlusCircleOutlined
              onClick={() => {
                showModal();
                setModalName(DictionaryCategory.Brand);
              }}
              style={{ marginLeft: 10 }}
            />
          );
        },
        style: { flex: 0.2 },
      },
      materialSelect,
      {
        label: '',
        name: 'materialPlus',
        formType: FormTypes.render,
        render() {
          return (
            <PlusCircleOutlined
              onClick={() => {
                showModal();
                setModalName(DictionaryCategory.Material);
              }}
              style={{ marginLeft: 10 }}
            />
          );
        },
        style: { flex: 0.2 },
      },
    ],
    [
      tagSelect,
      {
        label: '',
        name: 'materialPlus',
        formType: FormTypes.render,
        render() {
          return (
            <PlusCircleOutlined
              onClick={() => {
                showModal();
                setModalName(DictionaryCategory.Tag);
              }}
              style={{ marginLeft: 10 }}
            />
          );
        },
        style: { flex: 0.2 },
      },
    ],
    {
      formType: FormTypes.file,
      label: '预览图',
      multiple: true,
      name: 'headers',
      fileKey: 'headers',
    },
  ];

  const matForms: FormFields = [
    [
      {
        label: '区域',
        name: 'areaId',
        rules: [{ required: true }],
        formType: FormTypes.select,

        showSearch: true,
        filterOption(input, option) {
          return !!option?.value?.toString().includes(input);
        },
        value: 'id',
        options: initialState?.dictionaryList?.filter(
          (f) => f.category === DictionaryCategory.Area,
        ),
      },

      {
        label: '幼儿兴趣',
        name: 'interest',
        formType: FormTypes.select,
        options: SubjectActivityInterestScoreEnumDesc,
      },
    ],
    {
      label: '材料概述',
      name: 'info',
      placeholder: `请输入材料概述，可以参考如下内容：材料由什么部分构成（这份材料包含什么部分，比如“1. 彩色积木块20块；2. 名字卡片；3. 筛子；4. 笔；5. 记录单”）？幼儿会怎么操作这份材料？材料和操作单是否易于幼儿理解和操作？这份材料有什么难点？玩的时候可能会出现什么情况？这份材料是否可以达成其他区域或领域的目标？`,
      formType: FormTypes.textarea,
    },

    {
      label: '投放目的',
      name: 'purpose',
      placeholder:
        '请输入投放目的，可以参考如下内容：为什么要投放这份材料？材料的目标是什么？是否是幼儿的需求？幼儿是否有兴趣？是否符合幼儿生活经验？是否吸引幼儿？',
      formType: FormTypes.textarea,
    },
    [
      {
        label: '最小年龄',
        name: 'minAge',
        formType: FormTypes.input,
        type: 'number',
      },
      {
        label: '最大年龄',
        name: 'maxAge',
        formType: FormTypes.input,
        type: 'number',
      },
      {
        label: '适宜人数',
        name: 'suitable',
        formType: FormTypes.input,
        type: 'number',
      },
    ],
  ];

  const lastForms: FormFields = [
    [
      {
        label: '操作单及操作指引',
        name: 'resources',
        formType: FormTypes.file,
        fileKey: 'resources',
      },
    ],

    {
      label: '变化延伸',
      name: '变化延伸',
      placeholder: '请输入变化延伸（如有），这份材料是否有其他变体或延伸的玩法？',
      formType: FormTypes.textarea,
    },
    {
      label: '备注',
      name: 'note',
      formType: FormTypes.textarea,
    },
  ];

  useEffect(() => {
    fetchUserClasses().then((res) => {
      classOptions.current = res.data.map((item: any) => {
        return {
          label: item.title,
          value: item.id,
        };
      });
    });
    if (!isAdd) {
      fetchAreaProductDetail(params.id as any).then((r) => {
        Object.keys(r.data).forEach((key) => {
          form.setFieldValue(key, r.data[key]);
        });
        if (r.data.headers) {
          form.setFieldValue('headers', r.data.headers);
        }
        if (r.data.brand) {
          form.setFieldValue('brandId', r.data.brand.id);
          updateBrandSelect('', [{ label: r.data.brand.value, value: r.data.brand.id }]);
        }
        if (r.data.tags) {
          form.setFieldValue('tagIds', Array.from(new Set(r.data.tags.map((v: any) => v.tagId))));
          updateTagSelect(
            '',
            r.data.tags.map((v: any) => ({ value: v.tagId, label: v.title })),
          );
        }
        updateMaterialSelect('')

        setOriginData(r.data);
        if (r.data.gameplay) {
          setGames(r.data.gameplay);
        }
      });
    }
  }, []);

  return (
    <PageContainer style={{ background: '#fff' }}>
      <h3>材料基本信息</h3>
      <Row>
        <AntForms
          style={{ width: '100%' }}
          form={form}
          formProps={{
            disabled: true,
            initialValues: childFormData,
          }}
          forms={[
            {
              label: '班级',
              name: 'classId',
              formType: FormTypes.select,
              options: classOptions.current,
            },
          ]}
        ></AntForms>
      </Row>
      <Row>
        <AntForms
          style={{ width: '100%' }}
          form={form}
          formProps={{
            disabled: true,
            initialValues: childFormData,
          }}
          forms={childForms}
        ></AntForms>
      </Row>

      <h3>材料分析</h3>
      <Row>
        <AntForms
          form={form}
          formProps={{
            disabled: true,
            initialValues: childFormData,
          }}
          forms={matForms}
        ></AntForms>
      </Row>
      <h3
        style={{
          padding: '12px 0',
        }}
      >
        玩法介绍：
        <PlusCircleOutlined onClick={() => setGames([...games, { game: '', teacher: '' }])} />
      </h3>

      {games.map((g, index) => (
        <div style={{ padding: '12px 18px 12px 0', display: 'flex', marginLeft: 50 }} key={index}>
          <div style={{ width: 80, whiteSpace: 'nowrap' }}>玩法介绍：</div>
          <TextArea
            value={g.game}
            placeholder="请输入玩法介绍，一种玩法一行，玩法需要体现材料的层次性，玩法由易到难均需要考虑到位，可以参考核心经验进行玩法编写。可以思考：这份材料怎么玩？（可以列出玩法的步骤）这个玩法对应什么核心经验？玩法的重难点是什么？幼儿可能会遇到什么困难？"
            onChange={(e) => {
              setGames([
                ...games.slice(0, index),
                {
                  ...g,
                  game: e.target.value,
                },
                ...games.slice(index + 1),
              ]);
            }}
          />
          <div style={{ width: 80 }}></div>
          <div style={{ width: 80, whiteSpace: 'nowrap' }}>教师支持：</div>
          <TextArea
            value={g.teacher}
            placeholder="请输入本玩法的教师支持，教师在幼儿遇到困难时，可以如何支持和引导"
            onChange={(e) => {
              setGames([
                ...games.slice(0, index),
                {
                  ...g,
                  teacher: e.target.value,
                },
                ...games.slice(index + 1),
              ]);
            }}
          />
          <DeleteOutlined
            onClick={() => setGames((games) => games.filter((_, i) => i !== index))}
            style={{ marginLeft: 12, fontSize: 16, cursor: 'pointer' }}
          />
        </div>
      ))}

      <h3
        style={{
          padding: '12px 0',
        }}
      >
        核心经验：
      </h3>

      <div style={{ width: '100%' }}>
        <MatrixSelect form={matrixForm} activeRow={originData} />
      </div>
      <div style={{ width: '100%' }}>
        <AntForms
          form={form}
          formProps={{
            disabled: true,
            initialValues: childFormData,
          }}
          forms={lastForms}
        ></AntForms>
      </div>

      <Button
        loading={isLoading}
        style={{
          position: 'fixed',
          right: 60,
          top: 75,
        }}
        onClick={onSave}
        type="primary"
      >
        保存
      </Button>

      <AntModal onOk={onAddCategory} title={`新增${dictNameMaps[modalName]}`}>
        <ProForm submitter={false} form={modalForm}>
          <ProFormField
            required
            rules={[{ required: true }]}
            placeholder={`请输入${dictNameMaps[modalName]}`}
            name={'value'}
          ></ProFormField>
        </ProForm>
      </AntModal>
    </PageContainer>
  );
};

export default ClassAreaProductDetail;
