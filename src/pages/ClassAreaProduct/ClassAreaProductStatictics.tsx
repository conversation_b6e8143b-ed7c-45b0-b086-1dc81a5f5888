import { fetchAreaProductStatistics } from '@/services/classAreaProduct';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { useEffect, useRef } from 'react';

export const getInitAreaProductMatrixProps = () => ({
  matrix1Id: 0,
  matrix1Title: '',
  matrix2Id: 0,
  matrix2Title: '',
  matrix3Id: 0,
  matrix3Title: '',
});

function getDescString(records: any) {
  return (
    <div>
      {records.map((record: any, index: any) => (
        <div key={index}>
          {record.areaTitle ? <b>{record.areaTitle}</b> : null}
          {Array.isArray(record.products)
            ? record.products.map((d: any, i: number) => <div key={d.id}>{`${i+1}、${d.title}`}</div>)
            : record.products}
        </div>
      ))}
    </div>
  );
}

function getQueryParam(paramName: string) {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(paramName); // 如果不存在，返回 null
}

const ClassAreaProduct = () => {
  const actionRef = useRef<ActionType>();

  const classId = getQueryParam('classId')
  console.log(classId)
  useEffect(() => {}, []);

  const baseColumn: ProColumns<any, 'text'>[] = [
    {
      title: '领域',
      search: false,
      dataIndex: 'matrix1Title',
      width: 120,
    },
    {
      title: '维度',
      search: false,
      width: 160,
      dataIndex: 'matrix2Title',
    },
    {
      title: '子维度',
      search: false,
      width: 240,
      dataIndex: 'matrix3Title',
    },
    {
      title: '区域材料数量',
      search: false,
      dataIndex: 'count',
      width: 120,
      sorter: (a, b) => {
        return parseInt(a.count) > parseInt(b.count) ? 1 : -1;
      },
    },

    {
      title: '区域材料明细',
      search: false,
      dataIndex: 'detail',
      renderText(text, record) {
        if (record.records && record.records.length) {
          return getDescString(record.records);
        }
        return text;
      },
    },
  ];

  return (
    <PageContainer style={{ background: '#fff' }}>
      <ProTable<API.ClassAreaProductProps, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        scroll={{ y: 500, x: 'max-content' }}
        expandable={{ showExpandColumn: false }}
        toolBarRender={() => []}
        columns={baseColumn}
        search={false}
        pagination={false}
        request={(params) => {
          return fetchAreaProductStatistics({
            ...params,
            pageSize: 9999,
            classId,
          });
        }}
      />
    </PageContainer>
  );
};

export default ClassAreaProduct;
