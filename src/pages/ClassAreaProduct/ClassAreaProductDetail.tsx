import MatrixSelect from '@/components/MatrixSelect';
import AntForms, { FormFields, FormTypes } from '@/components/UseForms';

import { fetchDictionaryList, fetchUserClasses } from '@/services/apis';
import { fetchAreaProductDetail } from '@/services/classAreaProduct';
import { DictionaryCategory } from '@/services/constants';
import { useGetEnumOptionsByKey } from '@/services/enums';
import { PageContainer} from '@ant-design/pro-components';
import { history, useModel, useParams } from '@umijs/max';
import { Button, Row } from 'antd';
import { useForm } from 'antd/es/form/Form';
import TextArea from 'antd/es/input/TextArea';
import { useEffect, useRef, useState } from 'react';



const ClassAreaProductDetail = () => {
  const [form] = useForm();
  const [matrixForm] = useForm();
  const params = useParams();
  const { initialState } = useModel('@@initialState');
  const disabled = true;
  const [brandOptions, setBrandOptions] = useState([]);
  const [tagOptions, setTagOptions] = useState([]);
  const [materialOptions, setMaterialOptions] = useState([]);

  const [games, setGames] = useState<{ game: string; teacher: string }[]>([]);

  const isAdd = params.id === 'add';
  const classOptions = useRef<any[]>([]);
  const [originData, setOriginData] = useState<any>({});
  const [SubjectActivityInterestScoreEnumDesc] = useGetEnumOptionsByKey([
    'SubjectActivityInterestScoreEnumDesc',
  ]);


  const [childFormData] = useState({});

  const childForms: FormFields = [
    {
      label: '材料名称',
      name: 'title',
      disabled,
      formType: FormTypes.input,
      rules: [{ required: true }],
    },
    {
      label: '淘宝名称',
      disabled,
      name: 'taobaoName',
      formType: FormTypes.input,
    },
    {
      label: '淘宝链接',
      disabled,
      name: 'taobaoLink',
      formType: FormTypes.input,
    },

    [
      {
        label: '品牌',
        disabled,
        name: 'brandId',
        filterOption(input, option) {
          return !!option?.value?.toString().includes(input);
        },
        formType: FormTypes.select,
        options: brandOptions,
        style: { flex: 2 },
      },
      {
        label: '材质',
        name: 'material',
        disabled,
        filterOption(input, option) {
          return !!option?.value?.toString().includes(input);
        },
        formType: FormTypes.select,
        options: materialOptions,
        style: { flex: 1 },
      },
    ],
    [
      {
        label: '标签',
        disabled,
        name: 'tagIds',
        formType: FormTypes.select,
        mode: 'multiple',
        showSearch: true,
        filterOption(input, option) {
          return !!option?.value?.toString().includes(input);
        },
        options: tagOptions,
        style: { flex: 4 },
      },
    ],
    {
      formType: FormTypes.file,
      label: '预览图',
      multiple: false,
      disabled,
      name: 'headers',
      fileKey: 'headers',
    },
  ];

  const matForms: FormFields = [
    [
      {
        label: '区域',
        name: 'areaId',
        rules: [{ required: true }],
        formType: FormTypes.select,
        disabled,

        showSearch: true,
        filterOption(input, option) {
          return !!option?.value?.toString().includes(input);
        },
        value: 'id',
        options: initialState?.dictionaryList?.filter(
          (f) => f.category === DictionaryCategory.Area,
        ),
      },

      {
        label: '幼儿兴趣',
        disabled,
        name: 'interest',
        formType: FormTypes.select,
        options: SubjectActivityInterestScoreEnumDesc,
      },
    ],
    {
      label: '材料概述',
      disabled,
      name: 'info',
      formType: FormTypes.textarea,
    },

    {
      label: '投放目的',
      name: 'purpose',
      disabled,
      formType: FormTypes.textarea,
    },
    [
      {
        label: '最小年龄',
        name: 'minAge',
        disabled,
        formType: FormTypes.input,
        type: 'number',
      },
      {
        label: '最大年龄',
        disabled,
        name: 'maxAge',
        formType: FormTypes.input,
        type: 'number',
      },
      {
        label: '适宜人数',
        name: 'suitable',
        disabled,
        formType: FormTypes.input,
        type: 'number',
      },
    ],
  ];

  const lastForms: FormFields = [
    [
      {
        label: '操作单及操作指引',
        name: 'resources',
        formType: FormTypes.file,
        disabled,
        fileKey: 'resources',
      },
    ],

    {
      label: '变化延伸',
      disabled,
      name: '变化延伸',
      formType: FormTypes.textarea,
    },
    {
      label: '备注',
      disabled,
      name: 'note',
      formType: FormTypes.textarea,
    },
  ];

  useEffect(() => {
    fetchDictionaryList({
      current: 1,
      pageSize: 50,
      category: 5,
      state: 1,
    }).then((r) => {
      setBrandOptions(r.data.map((v: any) => ({ label: v.value, value: v.id })));
    });
    fetchDictionaryList({
      current: 1,
      pageSize: 50,
      category: 7,
      state: 1,
    }).then((r) => {
      setTagOptions(r.data.map((v: any) => ({ label: v.value, value: v.id })));
    });

    fetchDictionaryList({
      current: 1,
      pageSize: 50,
      category: 17,
      state: 1,
    }).then((r) => {
      setMaterialOptions(r.data.map((v: any) => ({ label: v.value, value: v.id })));
    });
    if (!isAdd) {
      fetchAreaProductDetail(params.id as any).then((r) => {
        Object.keys(r.data).forEach((key) => {
          form.setFieldValue(key, r.data[key]);
        });
        if (r.data.header) {
          form.setFieldValue('headers', [r.data.header]);
        }
        if (r.data.brand) {
          form.setFieldValue('brandId', r.data.brand.id);
        }
        if (r.data.tags) {
          form.setFieldValue('tagIds', Array.from(new Set(r.data.tags.map((v: any) => v.tagId))));
        }
        setOriginData(r.data);
        if (r.data.gameplay) {
          setGames(r.data.gameplay);
        }
      });
    }

    fetchUserClasses().then((res) => {
      classOptions.current = res.data.map((item: any) => {
        return {
          label: item.title,
          value: item.id
        }
      })
    })
  }, []);

  return (
    <PageContainer style={{ background: '#fff', position: 'relative' }}>
      <Button
        style={{
          position: 'fixed',
          right: 60,
          top: 75,
        }}
        type="primary"
        onClick={() => history.replace(`/ClassAreaProduct/edit/${params.id}`)}
      >
        编辑
      </Button>
      <h3>材料基本信息</h3>
      <Row>
      <AntForms
          style={{ width: '100%' }}
          form={form}
          formProps={{
            disabled: true,
            initialValues: childFormData,
          }}
          forms={[{
            label: '班级',
            name: 'classId',
            disabled: true,
            formType: FormTypes.select,
            options: classOptions.current
          }]}
        ></AntForms>
      </Row>
      <Row>
        <AntForms
          style={{ width: '100%' }}
          form={form}
          formProps={{
            disabled: true,
            initialValues: childFormData,
          }}
          forms={childForms}
        ></AntForms>
      </Row>
      <h3>材料分析</h3>
      <Row>
        <AntForms
          form={form}
          formProps={{
            disabled: true,
            initialValues: childFormData,
          }}
          forms={matForms}
        ></AntForms>
      </Row>
      <h3
        style={{
          padding: '12px 0',
        }}
      >
        玩法介绍：
      </h3>

      {games.map((g, index) => (
        <div style={{ padding: '12px 18px 12px 0', display: 'flex', marginLeft: 50 }} key={index}>
          <div style={{ width: 80, whiteSpace: 'nowrap' }}>玩法介绍：</div>
          <TextArea
            disabled
            value={g.game}
            onChange={(e) => {
              setGames([
                ...games.slice(0, index),
                {
                  ...g,
                  game: e.target.value,
                },
                ...games.slice(index + 1),
              ]);
            }}
          />
          <div style={{ width: 80 }}></div>
          <div style={{ width: 80, whiteSpace: 'nowrap' }}>教师支持：</div>
          <TextArea
            disabled
            value={g.teacher}
            onChange={(e) => {
              setGames([
                ...games.slice(0, index),
                {
                  ...g,
                  teacher: e.target.value,
                },
                ...games.slice(index + 1),
              ]);
            }}
          />
        </div>
      ))}

      <h3
        style={{
          padding: '12px 0',
        }}
      >
        核心经验：
      </h3>

      <div style={{ width: '100%' }}>
        <MatrixSelect disabled={disabled} form={matrixForm} activeRow={originData} />
      </div>
      <div style={{ width: '100%' }}>
        <AntForms
          form={form}
          formProps={{
            disabled: true,
            initialValues: childFormData,
          }}
          forms={lastForms}
        ></AntForms>
      </div>

     
    </PageContainer>
  );
};

export default ClassAreaProductDetail;
