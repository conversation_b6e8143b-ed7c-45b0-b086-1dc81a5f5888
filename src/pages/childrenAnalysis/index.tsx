// 幼儿进区记录
import { getCAList } from '@/services/api';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, Popover, message } from 'antd';
import React, { useRef, useState, useEffect } from 'react';
import { useModel } from '@umijs/max';
import dayjs from 'dayjs';

const ChildrenAnalysis = () => {
  const formRef = useRef<any>();
  const { initialState } = useModel('@@initialState');
  let { currentClassId } = initialState?.currentUser || {};
  let [dColumns, setDColumns] = useState([]);

  // 通过dayjs获取本周和上周的日期 格式为 2020-01-01 - 2020-01-07
  let dayJs = dayjs();

  // 新增配置项
  const columns = [
    {
      title: '姓名',
      dataIndex: 'title',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '日期',
      dataIndex: 'date',
      valueType: 'dateRange',
      width: 120,
      search: true,
      hidden: true,
    },
    {
      title: '日期范围',
      dataIndex: 'dateType',
      initialValue: 'semester',
      valueType: 'select',
      valueEnum: {
        today: '今日',
        yesterday: '昨日',
        dayBeforeYesterday: '前日',
        thisWeek: '本周',
        lastWeek: '上周',
        last7Days: '最近7天',
        thisMonth: '本月',
        lastMonth: '上月',
        last30Days: '最近30天',
        semester: '本学期',
        weeklyByArea: '各区域每周进区人数',
      },
      //   [
      //     {
      //       text: '今日',
      //       value: 'today',
      //     },
      //     {
      //       text: '昨日',
      //       value: 'yesterday',
      //     },
      //     {
      //       text: '前日',
      //       value: 'dayBeforeYesterday',
      //     },
      //     {
      //       text: '本周',
      //       value: 'thisWeek',
      //     },
      //     {
      //       text: '上周',
      //       value: 'lastWeek',
      //     },
      //     {
      //       text: '本月',
      //       value: 'thisMonth',
      //     },
      //     {
      //       text: '上月',
      //       value: 'lastMonth',
      //     },
      //     {
      //       text: '最近7天',
      //       value: 'last7Days',
      //     },
      //     {
      //       text: '最近30天',
      //       value: 'last30Days',
      //     },
      //     {
      //       text: '本学期',
      //       value: 'semester',
      //     },
      //     {
      //       text: '各区域每周进区人数',
      //       value: 'weeklyByArea',
      //     },
      //   ],
      width: 120,
      search: true,
      hidden: true,
    },
    ...dColumns,
  ];

  return (
    <PageContainer>
      <ProTable
        formRef={formRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        // 工具栏 默认谁关闭
        // columnsState={{
        //   defaultValue: {
        //     note: {
        //       show: false,
        //     },
        //   },
        // }}
        // 表格导航栏
        toolBarRender={false}
        request={async (values: any) => {
          console.log(values);

          let params = {
            classId: Number(currentClassId),
            dateType: values?.dateType,
            startDate: values?.date?.[0],
            endDate: values?.date?.[1],
          };
          let res = await getCAList(params);
          if (res.status === 0) {
            let data = res?.data || [];
            let columns = data[0]?.childAreaEntryCountList.map((item: any) => {
              return {
                title: item.area,
                valueType: 'text',
                width: 120,
                search: false,
                render: (value, record, index) => {
                  let count = value.childAreaEntryCountList.find(
                    (it) => it.area === item.area,
                  )?.count;
                  return <div>{count}</div>;
                },
              };
            });
            setDColumns(columns);
            return {
              data: res?.data || [],
              success: true,
            };
          }
        }}
        columns={columns}
        // scroll={{ x: 1000 }}
      />
    </PageContainer>
  );
};

export default ChildrenAnalysis;
