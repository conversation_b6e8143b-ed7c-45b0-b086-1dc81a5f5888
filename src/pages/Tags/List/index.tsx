/* eslint-disable guard-for-in */
import {
  getDictionary,
  updateDictionaryState,
  updateDictionary,
  addDictionary,
} from '@/services/api';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, Form, message } from 'antd';
import React, { useRef, useState } from 'react';
import Add from '../components/add';
import { DictionaryCategory } from '@/services/constants';

const TagsList: React.FC = () => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<any>({});

  const actionRef = useRef<ActionType>();

  const [form] = Form.useForm();

  const handleModalOpen = ({ type, record }) => {
    form.resetFields();
    setCurrentRow({});

    if (type === 'edit') {
      setCurrentRow(record);
    }

    setModalOpen(true);
  };

  // 更新标签
  const handleAdd = async (value: { value: string;  id?: string }) => {
    try {
      let res: API.AdminResponse;
      if (currentRow.id) {
        res = await updateDictionary({
          ...value,
          id: currentRow?.id,
          sort: currentRow.sort,
        });
      } else {
        res = await addDictionary({
          ...value,
          sort: 0,
          category: 32
        });
      }
      if (res?.status === 0) {
        message.success('更新成功');
        setModalOpen(false);
        if (actionRef.current) {
          actionRef.current.reload();
        }
      }
    } catch (error) {
      message.error(error?.message || '更新失败，请重试');
    }
  };

  const handleState = async ({ record }) => {
    try {
      let res: API.AdminResponse = await updateDictionaryState({
        id: record.id,
        state: record.state === 1 ? 0 : 1,
      });

      if (res?.status === 0) {
        message.success('更新成功');
        setModalOpen(false);
        if (actionRef.current) {
          actionRef.current.reload();
        }
      }
    } catch (error) {
      message.error(error?.message || '更新失败，请重试');
    }
  };

  const columns: ProColumns<API.TUserManager>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '名称',
      dataIndex: 'value',
      valueType: 'text',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '状态',
      dataIndex: 'state',
      valueType: 'text',
      hideInSearch: true,
      width: 80,
      render: (text) => {
        return text === 1 ? '激活' : '已关闭';
      },
    },
    {
      title: '操作',
      ellipsis: true,
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: '18em',
      search: false,
      render: (_, record) => [
        <Button
          size="small"
          type="primary"
          key="edit"
          onClick={() => {
            handleModalOpen({ type: 'edit', record });
          }}
        >
          编辑标签
        </Button>,
        <Button
          size="small"
          danger
          key="edit"
          onClick={() => {
            handleState({ record });
          }}
        >
          {record.state === 1 ? '关闭' : '激活'}
        </Button>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={false}
        // search={{
        //   labelWidth: 'auto',
        //   defaultCollapsed: false,
        //   collapsed: false,
        //   collapseRender: false,
        // }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalOpen({ type: 'add' });
            }}
          >
            <PlusOutlined /> 新建
          </Button>,
        ]}
        request={async (params) => {
          const res = await getDictionary({ ...params, category: DictionaryCategory.Tag });
          return res;
        }}
        columns={columns}
      />
      <Add
        form={form}
        modalOpen={modalOpen}
        setModalOpen={setModalOpen}
        currentRow={currentRow}
        // actionRef={actionRef}
        handleAdd={handleAdd}
      />
    </PageContainer>
  );
};

export default TagsList;
