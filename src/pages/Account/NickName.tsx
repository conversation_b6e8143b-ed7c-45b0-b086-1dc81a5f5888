// import { loginOut } from '@/components/RightContent/AvatarDropdown';
import { ProForm, PageContainer, ProFormText } from '@ant-design/pro-components';
import { useModel, history } from '@umijs/max';
import { message } from 'antd';
import React from 'react';

import { updateAvatarOrNickName } from '@/services/apis';

const NickName: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  // const { styles } = useStyles();
  // const intl = useIntl();
  const onFinish = async (values: any) => {
    const { id = 0 } = initialState?.currentUser || {};
    console.log(updateAvatarOrNickName);
    let params = { ...values, id };
    console.log(params);
    const res = await updateAvatarOrNickName(params);
    if(res.status === 0){
        // 去到/welcome页面并刷新
        message.success('修改成功');
        window.location.href = '/welcome';
        // history.push(`/welcome`);
        return
    }
    message.error(res.message);
  };
  //   const onFinish = (values: any) => {
  //     console.log('Received values of form: ', values);
  //   };

  return (
    <PageContainer>
      <ProForm
        style={{
          width: 300,
        }}
        initialValues={{
          nickname: initialState?.currentUser?.nickname,
        }}
        onFinish={onFinish}
        submitter={{
          searchConfig: {
            submitText: '确认修改',
          },
          resetButtonProps: {
            style: {
              // 隐藏重置按钮
              display: 'none',
            },
          },
        }}
      >
        <>
          <ProFormText label="昵称" name="nickname" placeholder={'请输入您的昵称'} required />
        </>
      </ProForm>
    </PageContainer>
  );
};

export default NickName;
