import { loginOut } from '@/components/RightContent/AvatarDropdown';
import { updatePwd, resetPwd } from '@/services/api';
import { ProForm, PageContainer, ProFormText } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { message } from 'antd';
import React from 'react';

const Login: React.FC = () => {
  const { setInitialState } = useModel('@@initialState');
  // const { styles } = useStyles();
  // const intl = useIntl();

  const handleSubmit = async (values: { pwd: string; newPwd: string }) => {
    try {
      // 修改密码
      const { pwd, newPwd } = values;
      const res = await updatePwd({ pwd, newPwd });

      if (res?.status === 0) {
        message.success('修改密码成功');

        setInitialState((s) => ({ ...s, currentUser: undefined } as any));
        loginOut(true);
        return;
      }

      message.error(res?.message);
    } catch (error: any) {
      message.error(error.message);
    }
  };

  // 重置密码
  const handleReset = async () => {
    let res = await resetPwd();
    if (res?.status === 0) {
      message.success('重置密码成功');
      return;
    } 
    message.error(res?.message);
  };

  return (
    <PageContainer>
      <ProForm
        // contentStyle={{
        //   minWidth: 280,
        //   maxWidth: '75vw',
        // }}
        style={{
          width: 300,
        }}
        initialValues={{
          autoLogin: true,
        }}
        onFinish={async (values) => {
          await handleSubmit(values as API.LoginParams);
        }}
        onReset={handleReset}
        submitter={{
          searchConfig: {
            submitText: '提交',
            resetText: '重置密码',
          },
        }}
      >
        <>
          <ProFormText.Password
            label="旧密码"
            name="pwd"
            placeholder={'默认为手机号后6位'}
            required
          />
          <ProFormText.Password label="新密码" name="newPwd" required />
          <ProFormText.Password
            label="确认新密码"
            name="submit-password"
            rules={[
              {
                required: true,
                message: '确认密码',
              },
              ({ getFieldValue }) => ({
                validator(rule, value) {
                  console.log('%c Line:71 🍎 rule', 'color:#42b983', rule, value);
                  if (!value || getFieldValue('newPwd') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject('两次密码输入不一致');
                },
              }),
            ]}
          />
        </>
      </ProForm>
    </PageContainer>
  );
};

export default Login;
