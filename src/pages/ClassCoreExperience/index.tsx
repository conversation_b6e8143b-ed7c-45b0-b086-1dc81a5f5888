import {
  <PERSON>Type,
  PageContainer,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Row, Select } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';

import { getTermColumn } from '@/components/CommonColumn';
import { fetchUserClasses } from '@/services/apis';
import { fetchClassCoreExperienceList } from '@/services/classCoreExperience';
import { useGetEnumOptionsByKey } from '@/services/enums';

export const getInitAreaProductMatrixProps = () => ({
  matrix1Id: 0,
  matrix1Title: '',
  matrix2Id: 0,
  matrix2Title: '',
  matrix3Id: 0,
  matrix3Title: '',
});

function sortActivities(activities: any[]) {
  if (!activities.length) {
    return [];
  }
  let stageTitle = '';
  const result: { type?: string; text: string; index: number; id: number }[] = [];
  let index = 1;
  activities.forEach((a) => {
    if (a.stageTitle !== stageTitle) {
      stageTitle = a.stageTitle;
      index = 1;
      result.push({
        type: 'stageTitle',
        text: a.stageTitle,
        id: a.id,
        index: 0,
      });
    }
    result.push({
      text: a.title,
      index: index++,
      id: a.id,
    });
  });

  return result;
}

function sortProducts(p: any) {
  return (
    <div>
      {p.map((child: any) => {
        return (
          <div key={child.areaId}>
            <b>{child.areaTitle}</b>
            {child.products.map((pro: any) => (
              <div key={pro.id}>{pro.title}</div>
            ))}
          </div>
        );
      })}
    </div>
  );
}

interface SummaryProps {
  /** 达标儿童数量 */
  childrenCount: number;
  /** 区域材料数量  */
  classAreaProductCount: number;
  /** 区域材料覆盖子维度数量 */
  classAreaProductMatrixCount: number;
  /** 年级 */
  grade: string;
  /** 年级分数 */
  gradeScore: string;
  /** 课程数量 */
  subjectCount: number;
}

const ClassAreaProduct = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const { initialState } = useModel('@@initialState');
  const classOptions = useRef<any[]>([]);
  const [dataSource, setDataSource] = useState([]);
  const [summaryData, setSummaryData] = useState<SummaryProps>({
    /**  */
    childrenCount: 0,
    classAreaProductCount: 0,
    classAreaProductMatrixCount: 0,
    grade: '',
    gradeScore: '',
    subjectCount: 0,
  });
  const updateTableData = async () => {
    const params: any = formRef.current?.getFieldsValue()
    if(params.term) {
      params.classId = params.term[0]
      params.term = params.term[1]

    }
    const response = await fetchClassCoreExperienceList(params);
    setDataSource(response.data.records);
    setSummaryData(response.data);
  };

  useEffect(() => {
    formRef.current?.setFieldsValue({
      term:  initialState?.currentClass
    });
    updateTableData();
    fetchUserClasses().then((res) => {
      classOptions.current = [
        { label: '全部', value: 0 },
        ...res.data.map((d: any) => ({ label: d.title, value: d.id })),
      ];
    });
  }, []);

  const baseColumn: ProColumns<any, 'text'>[] = [
    
    getTermColumn({ title: '班级/学期'}),
    {
      title: '领域',
      dataIndex: 'matrix1Title',
      fixed: 'left',
      search: false,
      width: 100,
    },
    {
      title: '维度',
      search: false,
      fixed: 'left',
      width: 200,
      dataIndex: 'matrix2Title',
    },
    {
      title: '子维度',
      search: false,
      fixed: 'left',
      width: 300,
      ellipsis: true,
      dataIndex: 'matrix3Title',
    },
    {
      title: '达标人数',
      search: false,
      width: 100,
      dataIndex: 'childrenCount',
      sorter(a: any, b: any) {
        return Number(a.childrenCount) - Number(b.childrenCount);
      },
    },
    {
      title: '达标率',
      search: false,
      width: 100,
      dataIndex: 'childrenPercent',
      sorter(a: any, b: any) {
        return Number(a.childrenPercent) - Number(b.childrenPercent);
      },
    },
    {
      title: '区域材料数量',
      search: false,
      width: 120,
      dataIndex: 'productCount',
      sorter(a: any, b: any) {
        return Number(a.productCount) - Number(b.productCount);
      },
    },
    {
      title: '区域材料明细',
      search: false,
      width: 120,
      render(dom, entity: any) {
        return sortProducts(entity.productDetail);
      },
      dataIndex: 'productDetail',
    },
    {
      title: '主题活动出现次数',
      search: false,
      width: 170,
      dataIndex: 'subjectActivityCount',
      sorter(a: any, b: any) {
        return Number(a.subjectActivityCount) - Number(b.subjectActivityCount);
      },
    },
    {
      title: '对应活动',
      search: false,
      dataIndex: 'subjectActivityDetail',
      render(dom, entity: any) {
        const activities = sortActivities(entity.subjectActivityDetail || []);
        if (!activities.length) {
          return <div>-</div>;
        }
        return (
          <div>
            {activities?.map((a) =>
              a.type === 'stageTitle' ? (
                <b key={a.text}>{a.text}</b>
              ) : (
                <div key={a.text}>
                  {a.id}、{a.text}
                </div>
              ),
            )}
          </div>
        );
      },
    },
  ];

  const summaryText = useMemo(() => {
    return `年级: ${summaryData.grade}; 年级分数: ${summaryData.gradeScore}; 课程数量: ${summaryData.subjectCount}; 达标儿童数量: ${summaryData.childrenCount}; 区域材料数量: ${summaryData.classAreaProductCount}; 区域材料覆盖子维度数量: ${summaryData.classAreaProductMatrixCount}`

  }, [summaryData]);

  return (
    <PageContainer style={{ background: '#fff' }}>
      <Row style={{ background: '#fff', marginBottom: '12px', padding: '12px' }}>
        <span style={{ fontWeight: 'bold' }}>{summaryText}</span>
      </Row>
      <ProTable<API.ClassAreaProductProps, API.PageParams>
        actionRef={actionRef}
        formRef={formRef}
        onSubmit={updateTableData}
        rowKey="id"
        scroll={{ y: 500, x: 'max-content' }}
        pagination={false}
        toolbar={{}}
        expandable={{ showExpandColumn: false }}
        toolBarRender={() => []}
        columns={baseColumn}
        dataSource={dataSource}
      />
    </PageContainer>
  );
};

export default ClassAreaProduct;
