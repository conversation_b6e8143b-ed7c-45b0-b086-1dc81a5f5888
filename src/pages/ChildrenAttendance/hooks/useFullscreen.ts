import { useState, useEffect } from 'react';
import { message } from 'antd';

export const useFullscreen = () => {
  // 全屏状态
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);

  // 切换全屏
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement
        .requestFullscreen()
        .then(() => {
          setIsFullscreen(true);
          // 禁用 body 滚动条
          document.body.style.overflow = 'hidden';
        })
        .catch((err) => {
          message.error(`无法进入全屏: ${err.message}`);
        });
    } else {
      if (document.exitFullscreen) {
        document
          .exitFullscreen()
          .then(() => {
            setIsFullscreen(false);
            // 恢复 body 滚动条
            document.body.style.overflow = 'auto';
          })
          .catch((err) => {
            message.error(`无法退出全屏: ${err.message}`);
          });
      }
    }
  };

  // 监听全屏变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isFullscreenNow = !!document.fullscreenElement;
      setIsFullscreen(isFullscreenNow);

      // 根据全屏状态设置 body 的滚动条
      document.body.style.overflow = isFullscreenNow ? 'hidden' : 'auto';
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);

    return () => {
      // 组件卸载时恢复 body 滚动条
      document.body.style.overflow = 'auto';
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  return {
    isFullscreen,
    toggleFullscreen,
  };
};
