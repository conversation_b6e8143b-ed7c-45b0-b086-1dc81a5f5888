import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, But<PERSON>, DatePicker, Tabs } from 'antd';
import { BarChartOutlined } from '@ant-design/icons';
import * as echarts from 'echarts';
import type { Dayjs } from 'dayjs';

const { RangePicker } = DatePicker;

// 图片压缩参数
const IMAGE_COMPRESS_PARAMS = '?x-oss-process=image/resize,m_fill,w_150';

// 添加图片压缩的工具函数
const addImageCompress = (url: string | undefined): string => {
  if (!url) return '';
  return url.includes('?')
    ? `${url}&x-oss-process=image/resize,m_fill,w_150`
    : `${url}${IMAGE_COMPRESS_PARAMS}`;
};

// 定义默认头像URL常量
const DEFAULT_BOY_AVATAR =
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png' +
  IMAGE_COMPRESS_PARAMS;
const DEFAULT_GIRL_AVATAR =
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png' +
  IMAGE_COMPRESS_PARAMS;

// 心情统计数据接口
interface MoodStatsData {
  count: number;
  imageId: number;
  imageUrl: string;
  moodId: number;
  moodName: string;
  sort: number;
}

// 儿童数据接口
interface ChildData {
  childId: number;
  childName: string;
  childAvatar: string;
  sex: number;
  sexDesc: string;
  state: number;
  stateDesc: string;
}

// 组件属性接口
interface MoodStatsModalProps {
  visible: boolean;
  onClose: () => void;
  statsData: MoodStatsData[] | null;
  radarStatsData: any | null; // 雷达图数据
  statsLoading: boolean;
  dateRange: [Dayjs, Dayjs];
  onDateRangeChange: (dates: any) => void;
  onRefreshData: () => void;
  // 儿童相关数据和函数
  childrenList: ChildData[];
  onChildSelect: (child: ChildData) => void;
  selectedChild: ChildData | null;
  childStatsData: any | null;
  childRadarStatsData: any | null;
}

const MoodStatsModal: React.FC<MoodStatsModalProps> = ({
  visible,
  onClose,
  statsData,
  radarStatsData,
  statsLoading,
  dateRange,
  onDateRangeChange,
  onRefreshData,
  childrenList,
  onChildSelect,
  selectedChild,
  childStatsData,
  childRadarStatsData,
}) => {
  // Tab 状态
  const [activeTab, setActiveTab] = useState('class');

  // 班级统计图表 refs
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const radarChartRef = useRef<HTMLDivElement>(null);
  const radarChartInstance = useRef<echarts.ECharts | null>(null);

  // 儿童统计图表 refs
  const childChartRef = useRef<HTMLDivElement>(null);
  const childChartInstance = useRef<echarts.ECharts | null>(null);
  const childRadarChartRef = useRef<HTMLDivElement>(null);
  const childRadarChartInstance = useRef<echarts.ECharts | null>(null);

  // 渲染图表的函数
  const renderChart = (data: MoodStatsData[]) => {
    if (!chartInstance.current || !data || data.length === 0) {
      return;
    }

    // 按sort字段排序
    const sortedData = [...data].sort((a, b) => a.sort - b.sort);

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: function (params: any) {
          const data = params[0];
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${data.name}</div>
              <div style="color: #666;">统计数量: ${data.value}人</div>
            </div>
          `;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: sortedData.map((item) => item.moodName),
        axisLabel: {
          interval: 0,
          rotate: 0,
          fontSize: 12,
          color: '#666',
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        name: '人数',
        nameTextStyle: {
          color: '#666',
          fontSize: 12,
        },
        axisLabel: {
          color: '#666',
          fontSize: 12,
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0',
          },
        },
      },
      series: [
        {
          name: '心情统计',
          type: 'bar',
          data: sortedData.map((item) => ({
            value: item.count,
            name: item.moodName,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#83bff6' },
                { offset: 0.5, color: '#188df0' },
                { offset: 1, color: '#188df0' },
              ]),
            },
          })),
          barWidth: '60%',
          label: {
            show: true,
            position: 'top',
            color: '#333',
            fontSize: 12,
            fontWeight: 'bold',
          },
          emphasis: {
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#2378f7' },
                { offset: 0.7, color: '#2378f7' },
                { offset: 1, color: '#83bff6' },
              ]),
            },
          },
        },
      ],
    };

    try {
      chartInstance.current.setOption(option);
    } catch (error) {
      console.error('设置图表配置失败:', error);
    }
  };

  // 渲染雷达图的函数
  const renderRadarChart = (data: any) => {
    if (!radarChartInstance.current || !data) {
      return;
    }

    console.log('雷达图数据:', data);

    // 处理雷达图数据格式
    const radarData = Array.isArray(data) ? data.sort((a: any, b: any) => a.sort - b.sort) : [];

    // 构建雷达图配置
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: function (params: any) {
          const data = params.data;
          const indicators = radarData.map((item: any) => item.moodName);
          let result = `<div style="padding: 8px;"><div style="font-weight: bold; margin-bottom: 4px;">${data.name}</div>`;

          data.value.forEach((value: number, index: number) => {
            const item = radarData[index];
            result += `<div style="color: #666;">${indicators[index]}: ${value}人 (${item.percentage}%)</div>`;
          });

          result += '</div>';
          return result;
        },
      },
      radar: {
        indicator: radarData.map((item: any) => ({
          name: item.moodName,
          max: 100, // 使用百分比，最大值为100
        })),
        center: ['50%', '55%'],
        radius: '60%',
        axisLabel: {
          show: true,
          formatter: '{value}%',
        },
      },
      series: [
        {
          name: '心情统计',
          type: 'radar',
          data: [
            {
              value: radarData.map((item: any) => item.percentage || 0),
              name: '心情分布',
              itemStyle: {
                color: '#188df0',
              },
              areaStyle: {
                color: 'rgba(24, 141, 240, 0.3)',
              },
              lineStyle: {
                width: 2,
              },
              symbol: 'circle',
              symbolSize: 6,
            },
          ],
        },
      ],
    };

    try {
      radarChartInstance.current.setOption(option);
    } catch (error) {
      console.error('设置雷达图配置失败:', error);
    }
  };

  // 渲染儿童点状图（散点图）的函数
  const renderChildSeriesChart = (data: any) => {
    if (!childChartInstance.current || !data) {
      return;
    }

    console.log('儿童点状图数据:', data);

    // 处理点状图数据格式 - 假设数据是时间序列
    const seriesData = Array.isArray(data) ? data : [];

    // 按日期排序数据
    const sortedData = [...seriesData].sort((a, b) =>
      new Date(a.recordDate).getTime() - new Date(b.recordDate).getTime()
    );

    // 找出连续相同心情的线段（只有连续的日期才连接）
    const lineSegments: any[] = [];
    for (let i = 0; i < sortedData.length - 1; i++) {
      const current = sortedData[i];
      const next = sortedData[i + 1];

      // 检查是否是连续的日期且心情相同
      const currentDate = new Date(current.recordDate.split(' ')[0]); // 只取日期部分
      const nextDate = new Date(next.recordDate.split(' ')[0]);
      const dayDiff = (nextDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24);

      // 只有相隔1天且心情相同才连线
      if (dayDiff === 1 && current.moodName === next.moodName) {
        lineSegments.push({
          coords: [
            [current.recordDate, current.moodName],
            [next.recordDate, next.moodName]
          ],
          lineStyle: {
            color: '#188df0',
            width: 2,
            type: 'solid'
          }
        });
      }
    }

    const option = {
      title: {
        text: `${selectedChild?.childName || '儿童'} 心情变化趋势`,
        left: 'center',
        textStyle: {
          fontSize: 18,
          fontWeight: 'bold',
          color: '#333',
        },
      },
      tooltip: {
        trigger: 'item',
        formatter: function (params: any) {
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${params.data.date}</div>
              <div style="color: #666;">心情: ${params.data.moodName}</div>
            </div>
          `;
        },
      },
      grid: {
        left: '10%',
        right: '10%',
        bottom: '15%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: sortedData.map((item: any) => item.recordDate),
        axisLabel: {
          rotate: 45,
          fontSize: 10,
          color: '#666',
          interval: 'auto', // 自动间隔显示标签，避免重叠
          formatter: function (value: string) {
            // 格式化日期显示，只显示月-日
            if (value) {
              // 处理 "2025-07-21 20:41:35" 格式的日期
              const dateStr = value.split(' ')[0]; // 取日期部分
              return dateStr.substring(5); // 从 YYYY-MM-DD 提取 MM-DD
            }
            return value;
          },
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'category',
        data: [...new Set(sortedData.map((item: any) => item.moodName))],
        axisLabel: {
          fontSize: 12,
          color: '#666',
        },
      },
      series: [
        // 连接线系列 - 用于连接相隔1-2天相同心情的点
        {
          name: '连续心情连线',
          type: 'lines',
          coordinateSystem: 'cartesian2d',
          data: lineSegments,
          silent: true, // 不响应鼠标事件
          lineStyle: {
            color: '#188df0',
            width: 2,
            opacity: 0.6,
          },
          z: 1, // 确保线在点的下方
        },
        // 散点图系列 - 使用心情图片作为点
        {
          name: '心情记录',
          type: 'scatter',
          data: sortedData.map((item: any) => ({
            value: [item.recordDate, item.moodName],
            date: item.recordDate,
            moodName: item.moodName,
            imageUrl: item.imageUrl,
            symbol: item.imageUrl ? `image://${addImageCompress(item.imageUrl)}` : 'circle',
            symbolSize: item.imageUrl ? [30, 30] : 12, // 图片大小或默认点大小
            itemStyle: {
              color: '#188df0',
            },
          })),
          emphasis: {
            symbolSize: function(params: any) {
              return params.data.imageUrl ? [35, 35] : 15; // 悬停时稍微放大
            },
          },
          z: 2, // 确保点在线的上方
        },
      ],
    };

    try {
      childChartInstance.current.setOption(option);
    } catch (error) {
      console.error('设置儿童点状图配置失败:', error);
    }
  };

  // 渲染儿童雷达图的函数
  const renderChildRadarChart = (data: any) => {
    if (!childRadarChartInstance.current || !data) {
      return;
    }

    console.log('儿童雷达图数据:', data);

    // 处理儿童雷达图数据格式
    const radarData = Array.isArray(data) ? data.sort((a: any, b: any) => a.sort - b.sort) : [];

    const option = {
      title: {
        text: `${selectedChild?.childName || '儿童'} 心情分布`,
        left: 'center',
        textStyle: {
          fontSize: 18,
          fontWeight: 'bold',
          color: '#333',
        },
      },
      tooltip: {
        trigger: 'item',
        formatter: function (params: any) {
          const data = params.data;
          const indicators = radarData.map((item: any) => item.moodName);
          let result = `<div style="padding: 8px;"><div style="font-weight: bold; margin-bottom: 4px;">${data.name}</div>`;

          data.value.forEach((value: number, index: number) => {
            const item = radarData[index];
            result += `<div style="color: #666;">${indicators[index]}: ${value}人 (${
              item.percentage || 0
            }%)</div>`;
          });

          result += '</div>';
          return result;
        },
      },
      radar: {
        indicator: radarData.map((item: any) => ({
          name: item.moodName,
          max: 100,
        })),
        center: ['50%', '55%'],
        radius: '60%',
        axisLabel: {
          show: true,
          formatter: '{value}%',
        },
      },
      series: [
        {
          name: '心情统计',
          type: 'radar',
          data: [
            {
              value: radarData.map((item: any) => item.percentage || 0),
              name: '心情分布',
              itemStyle: {
                color: '#188df0',
              },
              areaStyle: {
                color: 'rgba(24, 141, 240, 0.3)',
              },
              lineStyle: {
                width: 2,
              },
              symbol: 'circle',
              symbolSize: 6,
            },
          ],
        },
      ],
    };

    try {
      childRadarChartInstance.current.setOption(option);
    } catch (error) {
      console.error('设置儿童雷达图配置失败:', error);
    }
  };

  // 初始化图表
  useEffect(() => {
    if (visible) {
      // 延迟初始化，确保DOM完全渲染
      const timer = setTimeout(() => {
        // 初始化柱状图
        if (chartRef.current && !chartInstance.current) {
          try {
            chartInstance.current = echarts.init(chartRef.current, null, { renderer: 'svg' });

            // 如果有数据，立即渲染
            if (statsData && statsData.length > 0) {
              renderChart(statsData);
            }
          } catch (error) {
            console.error('柱状图初始化失败:', error);
          }
        }

        // 初始化雷达图
        if (radarChartRef.current && !radarChartInstance.current) {
          try {
            radarChartInstance.current = echarts.init(radarChartRef.current, null, { renderer: 'svg' });

            // 如果有数据，立即渲染
            if (radarStatsData) {
              renderRadarChart(radarStatsData);
            }
          } catch (error) {
            console.error('雷达图初始化失败:', error);
          }
        }

        // 初始化儿童点状图
        if (childChartRef.current && !childChartInstance.current) {
          try {
            childChartInstance.current = echarts.init(childChartRef.current, null, { renderer: 'svg' });

            // 如果有数据，立即渲染
            if (childStatsData) {
              renderChildSeriesChart(childStatsData);
            }
          } catch (error) {
            console.error('儿童点状图初始化失败:', error);
          }
        }

        // 初始化儿童雷达图
        if (childRadarChartRef.current && !childRadarChartInstance.current) {
          try {
            childRadarChartInstance.current = echarts.init(childRadarChartRef.current, null, { renderer: 'svg' });

            // 如果有数据，立即渲染
            if (childRadarStatsData) {
              renderChildRadarChart(childRadarStatsData);
            }
          } catch (error) {
            console.error('儿童雷达图初始化失败:', error);
          }
        }
      }, 300);

      return () => clearTimeout(timer);
    } else {
      // 弹窗关闭时清理实例
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
      if (radarChartInstance.current) {
        radarChartInstance.current.dispose();
        radarChartInstance.current = null;
      }
      if (childChartInstance.current) {
        childChartInstance.current.dispose();
        childChartInstance.current = null;
      }
      if (childRadarChartInstance.current) {
        childRadarChartInstance.current.dispose();
        childRadarChartInstance.current = null;
      }
    }
  }, [visible, statsData, radarStatsData, childStatsData, childRadarStatsData]);

  // 更新柱状图数据
  useEffect(() => {
    if (chartInstance.current && statsData && statsData.length > 0) {
      renderChart(statsData);
    }
  }, [statsData]);

  // 更新雷达图数据
  useEffect(() => {
    if (radarChartInstance.current && radarStatsData) {
      renderRadarChart(radarStatsData);
    }
  }, [radarStatsData]);

  // 更新儿童点状图数据
  useEffect(() => {
    if (childChartInstance.current && childStatsData) {
      renderChildSeriesChart(childStatsData);
    }
  }, [childStatsData]);

  // 更新儿童雷达图数据
  useEffect(() => {
    if (childRadarChartInstance.current && childRadarStatsData) {
      renderChildRadarChart(childRadarStatsData);
    }
  }, [childRadarStatsData]);

  // 窗口大小变化时重新调整图表
  useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
      if (radarChartInstance.current) {
        radarChartInstance.current.resize();
      }
      if (childChartInstance.current) {
        childChartInstance.current.resize();
      }
      if (childRadarChartInstance.current) {
        childRadarChartInstance.current.resize();
      }
    };

    if (visible) {
      window.addEventListener('resize', handleResize);
      // 延迟调整大小，确保Modal完全显示后再调整
      setTimeout(() => {
        if (chartInstance.current) {
          chartInstance.current.resize();
        }
        if (radarChartInstance.current) {
          radarChartInstance.current.resize();
        }
        if (childChartInstance.current) {
          childChartInstance.current.resize();
        }
        if (childRadarChartInstance.current) {
          childRadarChartInstance.current.resize();
        }
      }, 100);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [visible]);

  // 处理Tab切换，销毁图表实例避免缩放问题
  const handleTabChange = (key: string) => {
    // 销毁所有图表实例
    if (chartInstance.current) {
      chartInstance.current.dispose();
      chartInstance.current = null;
    }
    if (radarChartInstance.current) {
      radarChartInstance.current.dispose();
      radarChartInstance.current = null;
    }
    if (childChartInstance.current) {
      childChartInstance.current.dispose();
      childChartInstance.current = null;
    }
    if (childRadarChartInstance.current) {
      childRadarChartInstance.current.dispose();
      childRadarChartInstance.current = null;
    }

    setActiveTab(key);

    // 延迟重新初始化图表
    setTimeout(() => {
      if (key === 'class') {
        // 重新初始化班级图表
        if (chartRef.current && !chartInstance.current) {
          chartInstance.current = echarts.init(chartRef.current, null, { renderer: 'svg' });
          if (statsData && statsData.length > 0) {
            renderChart(statsData);
          }
        }
        if (radarChartRef.current && !radarChartInstance.current) {
          radarChartInstance.current = echarts.init(radarChartRef.current, null, { renderer: 'svg' });
          if (radarStatsData) {
            renderRadarChart(radarStatsData);
          }
        }
      } else if (key === 'child') {
        // 重新初始化儿童图表
        if (childChartRef.current && !childChartInstance.current) {
          childChartInstance.current = echarts.init(childChartRef.current, null, { renderer: 'svg' });
          if (childStatsData) {
            renderChildSeriesChart(childStatsData);
          }
        }
        if (childRadarChartRef.current && !childRadarChartInstance.current) {
          childRadarChartInstance.current = echarts.init(childRadarChartRef.current, null, { renderer: 'svg' });
          if (childRadarStatsData) {
            renderChildRadarChart(childRadarStatsData);
          }
        }
      }
    }, 100);
  };

  return (
    <Modal
      title={
        <div className="flex items-center gap-2 text-lg font-bold text-blue-600">
          <BarChartOutlined />
          心情统计分析
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      centered
      destroyOnClose
      zIndex={10002} // 确保在全屏模式下正确显示
    >
      <div className="py-4">
        {/* 日期选择器 */}
        <div className="mb-6 flex items-center gap-4">
          <span className="text-gray-700 font-medium">统计时间范围：</span>
          <RangePicker
            value={dateRange}
            onChange={onDateRangeChange}
            format="YYYY-MM-DD"
            placeholder={['开始日期', '结束日期']}
          />
          <Button type="primary" onClick={onRefreshData} loading={statsLoading}>
            刷新数据
          </Button>
        </div>

        {/* Tab 切换 */}
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          items={[
            {
              key: 'class',
              label: '按班级统计',
              children: (
                <div
                  className="bg-white rounded-lg border border-gray-200 p-4 relative"
                  style={{ height: '500px' }}
                >
                  <div className="flex gap-4" style={{ height: '100%' }}>
                    {/* 左侧：柱状图 */}
                    <div className="relative flex-1" style={{ height: '450px' }}>
                      <div className="text-center text-lg font-bold text-gray-800 mb-4">
                        按班级统计儿童心情值
                      </div>
                      <div ref={chartRef} style={{ width: '100%', height: 'calc(100% - 40px)' }} />
                    </div>

                    {/* 右侧：雷达图 */}
                    <div className="relative flex-1" style={{ height: '450px' }}>
                      <div className="text-center text-lg font-bold text-gray-800 mb-4">
                        按班级统计儿童心情
                      </div>
                      <div ref={radarChartRef} style={{ width: '100%', height: 'calc(100% - 40px)' }} />
                    </div>
                  </div>

                  {/* 加载状态覆盖层 */}
                  {statsLoading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
                        <div className="text-gray-600">正在加载统计数据...</div>
                      </div>
                    </div>
                  )}

                  {/* 空数据状态覆盖层 */}
                  {!statsLoading && (!statsData || statsData.length === 0) && !radarStatsData && (
                    <div className="absolute inset-0 flex items-center justify-center bg-white">
                      <div className="text-center text-gray-500">
                        <BarChartOutlined className="text-4xl mb-4" />
                        <div>暂无统计数据</div>
                        <div className="text-sm mt-2">请选择日期范围并点击刷新数据</div>
                      </div>
                    </div>
                  )}
                </div>
              ),
            },
            {
              key: 'child',
              label: '按儿童统计',
              children: (
                <div>
                  {/* 儿童列表 */}
                  <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                    <div className="flex flex-wrap gap-2">
                      {childrenList.map((child) => (
                        <div
                          key={child.childId}
                          className={`flex flex-col items-center cursor-pointer transition-all duration-300 hover:scale-105 select-none ${
                            selectedChild?.childId === child.childId
                              ? 'bg-blue-100 rounded-lg p-1'
                              : ''
                          }`}
                          onClick={() => onChildSelect(child)}
                          style={{ width: '60px', margin: '4px' }}
                        >
                          {/* 圆形头像容器 - 与主页样式一致 */}
                          <div
                            className={`relative flex items-center justify-center rounded-full bg-white shadow-lg border-2 overflow-hidden ${
                              child.sex === 1 ? 'border-blue-400' : 'border-pink-400'
                            }`}
                            style={{ width: '50px', height: '50px' }}
                          >
                            <img
                              src={
                                addImageCompress(child.childAvatar) ||
                                (child.sex === 1 ? DEFAULT_BOY_AVATAR : DEFAULT_GIRL_AVATAR)
                              }
                              alt={child.childName}
                              className="w-full h-full object-cover rounded-full"
                            />
                          </div>

                          {/* 名字显示在头像下方 */}
                          <div
                            className="text-center mt-1 text-gray-800 font-bold text-xs leading-tight overflow-hidden text-ellipsis whitespace-nowrap"
                            style={{ width: '50px', maxWidth: '50px' }}
                            title={child.childName.length > 4 ? child.childName : ''}
                          >
                            {child.childName.length > 4
                              ? child.childName.substring(0, 4)
                              : child.childName}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 儿童图表区域 */}
                  {selectedChild ? (
                    <div
                      className="bg-white rounded-lg border border-gray-200 p-4 relative"
                      style={{ height: '500px' }}
                    >
                      <div className="flex gap-4" style={{ height: '100%' }}>
                        {/* 左侧：点状图 */}
                        <div className="relative flex-1" style={{ height: '450px' }}>
                          <div ref={childChartRef} style={{ width: '100%', height: '100%' }} />
                        </div>

                        {/* 右侧：雷达图 */}
                        <div className="relative flex-1" style={{ height: '450px' }}>
                          <div ref={childRadarChartRef} style={{ width: '100%', height: '100%' }} />
                        </div>
                      </div>

                      {/* 加载状态覆盖层 */}
                      {statsLoading && (
                        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90">
                          <div className="text-center">
                            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
                            <div className="text-gray-600">正在加载统计数据...</div>
                          </div>
                        </div>
                      )}

                      {/* 空数据状态覆盖层 */}
                      {!statsLoading && !childStatsData && !childRadarStatsData && (
                        <div className="absolute inset-0 flex items-center justify-center bg-white">
                          <div className="text-center text-gray-500">
                            <BarChartOutlined className="text-4xl mb-4" />
                            <div>暂无统计数据</div>
                            <div className="text-sm mt-2">请选择日期范围并点击刷新数据</div>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="bg-gray-50 rounded-lg p-8 text-center">
                      <div className="text-gray-500">
                        <BarChartOutlined className="text-4xl mb-4" />
                        <div>请选择一个儿童查看统计数据</div>
                      </div>
                    </div>
                  )}
                </div>
              ),
            },
          ]}
        />
      </div>
    </Modal>
  );
};

export default MoodStatsModal;
