import React, { useState, useEffect } from 'react';
import { Drawer, Button, Modal, Input, Tag, message } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';

// 心情选项接口
interface MoodOption {
  label: string;
  value: string;
  emoji?: string; // 可选，用于默认表情
  imageUrl?: string; // 可选，用于接口返回的图片
  description: string;
}

// 儿童数据接口
interface ChildAttendanceData {
  childId: number;
  childName: string;
  childAvatar: string;
  sex: number; // 1-男，2-女
  sexDesc: string;
  state: number;
  stateDesc: string;
}

// 儿童头像组件接口
interface ChildAvatarProps {
  src: string;
  alt: string;
  size?: number;
  sex: number;
}

// 病假类型接口
interface SickLeaveType {
  dictCode: string;
  dictItemCode: string;
  dictItemId: number;
  dictItemName: string;
  dictItemSort: number;
}

// 弹窗组件接口
interface MoodSelectDrawerProps {
  visible: boolean;
  onClose: () => void;
  selectedChild: ChildAttendanceData | null;
  onMoodSelect: (moodValue: string) => void;
  onPersonalLeave: (remark: string) => void;
  onSickLeave: (typeCode: string, remark: string) => void;
  moodOptions: MoodOption[];
  ChildAvatar: React.FC<ChildAvatarProps>;
  getSickLeaveTypeList: () => Promise<any>;
  onRefreshMoodList?: () => Promise<void>; // 新增刷新心情列表的回调
}

const MoodSelectDrawer: React.FC<MoodSelectDrawerProps> = ({
  visible,
  onClose,
  selectedChild,
  onMoodSelect,
  onPersonalLeave,
  onSickLeave,
  moodOptions,
  ChildAvatar,
  getSickLeaveTypeList,
  onRefreshMoodList,
}) => {
  // 事假弹窗状态
  const [personalLeaveVisible, setPersonalLeaveVisible] = useState(false);
  const [personalLeaveRemark, setPersonalLeaveRemark] = useState('');

  // 病假弹窗状态
  const [sickLeaveVisible, setSickLeaveVisible] = useState(false);
  const [sickLeaveRemark, setSickLeaveRemark] = useState('');
  const [sickLeaveTypes, setSickLeaveTypes] = useState<SickLeaveType[]>([]);
  const [selectedSickLeaveType, setSelectedSickLeaveType] = useState<string>('');

  // 刷新心情列表状态
  const [refreshing, setRefreshing] = useState(false);

  // 预加载心情图片
  useEffect(() => {
    const preloadImages = () => {
      moodOptions.forEach((mood) => {
        if (mood.imageUrl) {
          const img = new Image();
          const compressedUrl = `${mood.imageUrl}?x-oss-process=image/resize,m_fill,w_150`;
          img.src = compressedUrl;
          // 预加载但不需要处理加载结果
        }
      });
    };

    if (moodOptions.length > 0) {
      preloadImages();
    }
  }, [moodOptions]);

  // 获取病假类型列表
  const fetchSickLeaveTypes = async () => {
    try {
      const response = await getSickLeaveTypeList();
      if (response.status === 0 && response.data) {
        setSickLeaveTypes(response.data);
      }
    } catch (error) {
      console.error('获取病假类型失败:', error);
    }
  };

  // 处理事假按钮点击
  const handlePersonalLeaveClick = () => {
    setPersonalLeaveVisible(true);
  };

  // 处理病假按钮点击
  const handleSickLeaveClick = async () => {
    await fetchSickLeaveTypes();
    setSickLeaveVisible(true);
  };

  // 确认事假
  const handlePersonalLeaveConfirm = () => {
    onPersonalLeave(personalLeaveRemark);
    setPersonalLeaveVisible(false);
    setPersonalLeaveRemark('');
  };

  // 确认病假
  const handleSickLeaveConfirm = () => {
    if (!selectedSickLeaveType) {
      message.warning('请选择病假类型');
      return;
    }
    onSickLeave(selectedSickLeaveType, sickLeaveRemark);
    setSickLeaveVisible(false);
    setSickLeaveRemark('');
    setSelectedSickLeaveType('');
  };

  // 处理刷新心情列表
  const handleRefreshMoodList = async () => {
    if (!onRefreshMoodList) return;

    setRefreshing(true);
    try {
      await onRefreshMoodList();
    } catch (error) {
      console.error('刷新心情列表失败:', error);
    } finally {
      setRefreshing(false);
    }
  };
  return (
    <Drawer
      title={
        <div className="flex items-center justify-between">
          <span>选择今天的心情</span>
          <Button
            type="text"
            icon={<ReloadOutlined />}
            loading={refreshing}
            onClick={handleRefreshMoodList}
            className="text-blue-500 hover:text-blue-600"
            size="small"
          >
            刷新
          </Button>
        </div>
      }
      open={visible}
      onClose={onClose}
      placement="bottom"
      height="60vh" // 减少弹窗高度
      zIndex={10002} // 提高 z-index 确保在全屏模式下正确显示
      styles={{
        body: {
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          padding: '16px 16px 24px 16px',
        },
      }}
    >
      {selectedChild && (
        <div className="flex flex-col items-center h-full select-none">
          {/* 儿童头像区域 - 压缩高度 */}
          <div className="flex items-center justify-center mb-4 gap-6">
            {/* 左侧：儿童头像和信息 */}
            <div className="flex flex-col items-center">
              <ChildAvatar
                src={selectedChild.childAvatar}
                alt={`${selectedChild.childName} ${selectedChild.sexDesc}`}
                size={100}
                sex={selectedChild.sex}
              />
              <h3 className="text-xl font-bold text-gray-800 mt-2">
                {selectedChild.childName}
              </h3>
              <p className="text-base text-gray-600 mt-1">今天的心情怎么样？</p>
            </div>

            {/* 右侧：事假和病假按钮 */}
            <div className="flex flex-col gap-3">
              <Button
                type="primary"
                size="middle"
                className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 border-0 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 rounded-xl px-6 py-1 font-bold text-white"
                onClick={handlePersonalLeaveClick}
              >
                事假
              </Button>
              <Button
                type="primary"
                size="middle"
                className="bg-gradient-to-r from-red-400 to-red-500 hover:from-red-500 hover:to-red-600 border-0 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 rounded-xl px-6 py-1 font-bold text-white"
                onClick={handleSickLeaveClick}
              >
                病假
              </Button>
            </div>
          </div>

          {/* 心情选择区域 */}
          <div className="flex-1 w-full">
            <div
              className={`grid gap-2 mx-auto pb-4 ${
                moodOptions.length < 7
                  ? 'grid-cols-3 max-w-md justify-center' // 小于7个时，3列布局，居中显示
                  : 'grid-cols-10 max-w-full' // 7个及以上时，10列布局
              }`}
            >
              {moodOptions.map((mood) => {
                // 添加图片压缩参数
                const compressedImageUrl = mood.imageUrl
                  ? `${mood.imageUrl}?x-oss-process=image/resize,m_fill,w_150`
                  : mood.imageUrl;

                // 根据心情数量调整卡片大小
                const isLargeLayout = moodOptions.length < 7;

                return (
                  <div
                    key={mood.value}
                    onClick={() => onMoodSelect(mood.value)}
                    className={`flex flex-col items-center rounded-lg border-2 border-gray-200 cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-lg hover:border-blue-300 bg-white select-none group ${
                      isLargeLayout
                        ? 'p-4 min-h-[140px]' // 大布局：更大的内边距和高度
                        : 'p-2 min-h-[90px]'   // 小布局：紧凑的内边距和高度
                    }`}
                  >
                    {/* 显示心情图片 - 优先使用接口返回的图片，否则使用默认表情 */}
                    {compressedImageUrl ? (
                      <img
                        src={compressedImageUrl}
                        alt={mood.label}
                        width={isLargeLayout ? "60" : "40"}
                        height={isLargeLayout ? "60" : "40"}
                        className={`object-contain transition-transform duration-300 group-hover:scale-150 ${
                          isLargeLayout
                            ? 'w-15 h-15 mb-3' // 大布局：更大的图片
                            : 'w-10 h-10 mb-1' // 小布局：紧凑的图片
                        }`}
                        loading="lazy" // 懒加载
                        decoding="async" // 异步解码
                        style={{ imageRendering: 'auto' }} // 优化图片渲染
                      />
                    ) : mood.emoji ? (
                      <picture className={`transition-transform duration-300 group-hover:scale-150 ${
                        isLargeLayout ? 'mb-3' : 'mb-1'
                      }`}>
                        <source
                          srcSet={`https://fonts.gstatic.com/s/e/notoemoji/latest/${mood.emoji}/512.webp`}
                          type="image/webp"
                        />
                        <img
                          src={`https://fonts.gstatic.com/s/e/notoemoji/latest/${mood.emoji}/512.gif`}
                          alt={mood.label}
                          width={isLargeLayout ? "60" : "40"}
                          height={isLargeLayout ? "60" : "40"}
                          className={isLargeLayout ? 'w-15 h-15' : 'w-10 h-10'}
                        />
                      </picture>
                    ) : (
                      <div className={`bg-gray-200 rounded-full flex items-center justify-center transition-transform duration-300 group-hover:scale-150 ${
                        isLargeLayout
                          ? 'w-15 h-15 mb-3'
                          : 'w-10 h-10 mb-1'
                      }`}>
                        <span className={isLargeLayout ? 'text-2xl' : 'text-lg'}>😊</span>
                      </div>
                    )}
                    <div className={`font-bold text-gray-800 text-center leading-tight ${
                      isLargeLayout ? 'text-sm' : 'text-xs'
                    }`}>
                      {mood.label}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* 事假弹窗 */}
      <Modal
        title={
          <div className="text-lg font-bold text-yellow-600">
            事假申请
          </div>
        }
        open={personalLeaveVisible}
        onOk={handlePersonalLeaveConfirm}
        onCancel={() => {
          setPersonalLeaveVisible(false);
          setPersonalLeaveRemark('');
        }}
        okText="确认"
        cancelText="取消"
        okButtonProps={{
          className: "bg-yellow-500 hover:bg-yellow-600 border-yellow-500 font-medium",
          size: "large"
        }}
        cancelButtonProps={{
          size: "large"
        }}
        width={500}
        centered
      >
        <div className="py-6">
          <p className="mb-4 text-gray-700 font-medium text-base">请填写事假备注：</p>
          <Input.TextArea
            value={personalLeaveRemark}
            onChange={(e) => setPersonalLeaveRemark(e.target.value)}
            placeholder="请填写"
            rows={5}
            maxLength={200}
            showCount
            className="rounded-lg"
            style={{ fontSize: '14px', lineHeight: '1.6' }}
          />
        </div>
      </Modal>

      {/* 病假弹窗 */}
      <Modal
        title={
          <div className="text-lg font-bold text-red-600">
            病假申请
          </div>
        }
        open={sickLeaveVisible}
        onOk={handleSickLeaveConfirm}
        onCancel={() => {
          setSickLeaveVisible(false);
          setSickLeaveRemark('');
          setSelectedSickLeaveType('');
        }}
        okText="确认"
        cancelText="取消"
        okButtonProps={{
          className: "bg-red-500 hover:bg-red-600 border-red-500 font-medium",
          size: "large"
        }}
        cancelButtonProps={{
          size: "large"
        }}
        width={700}
        centered
      >
        <div className="py-6">
          <div className="mb-8">
            <p className="mb-4 text-gray-700 font-medium text-base">请选择病假类型：</p>
            <div
              className="p-4 bg-gray-50 rounded-lg border border-gray-200"
              style={{
                columnCount: 3,
                columnGap: '16px',
                columnFill: 'balance',
              }}
            >
              {sickLeaveTypes.map((type) => (
                <Tag.CheckableTag
                  key={type.dictItemCode}
                  checked={selectedSickLeaveType === type.dictItemCode}
                  onChange={(checked) => {
                    setSelectedSickLeaveType(checked ? type.dictItemCode : '');
                  }}
                  className={`
                    inline-block w-full mb-3 px-3 py-2 text-sm font-medium rounded-lg border-2 transition-all duration-300 cursor-pointer select-none
                    ${selectedSickLeaveType === type.dictItemCode
                      ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white border-blue-500 shadow-lg transform scale-105'
                      : 'bg-white text-gray-700 border-gray-300 hover:border-blue-400 hover:bg-blue-50 hover:text-blue-600 shadow-sm hover:shadow-md hover:transform hover:scale-102'
                    }
                  `}
                  style={{
                    textAlign: 'center',
                    lineHeight: '1.3',
                    breakInside: 'avoid',
                    pageBreakInside: 'avoid',
                  }}
                >
                  {type.dictItemName}
                </Tag.CheckableTag>
              ))}
            </div>
          </div>

          <div>
            <p className="mb-4 text-gray-700 font-medium text-base">请填写病假备注：</p>
            <Input.TextArea
              value={sickLeaveRemark}
              onChange={(e) => setSickLeaveRemark(e.target.value)}
              placeholder="请填写"
              rows={5}
              maxLength={200}
              showCount
              className="rounded-lg"
              style={{ fontSize: '14px', lineHeight: '1.6' }}
            />
          </div>
        </div>
      </Modal>
    </Drawer>
  );
};

export default MoodSelectDrawer;
