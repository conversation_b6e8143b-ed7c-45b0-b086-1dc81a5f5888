import React, { useState, useEffect, useMemo } from 'react';
import { message, DatePicker, Popover, Button, Modal, Input, Tag } from 'antd';
import { BarChartOutlined, FullscreenOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { PageContainer } from '@ant-design/pro-components';
import {
  getChildrenAttendanceList,
  saveChildrenAttendance,
  getSickLeaveTypeList,
  getChildrenMoodList,
  saveChildrenMoodRecord,
  getChildrenMoodBarStats,
  getChildrenMoodRadarStats,
  getChildrenMoodSeriesStats,
  getChildrenMoodRadarSeriesStats,
  getChildrenMoodRecordList,
} from '@/services/childrenAttendance';
import MoodSelectDrawer from './components/MoodSelectDrawer';
import MoodStatsModal from './components/MoodStatsModal';
import FullscreenToolbar from './components/FullscreenToolbar';
import { useFullscreen } from './hooks';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';

// 心情数据接口
interface MoodData {
  id: number;
  moodName: string;
  imageUrl: string;
  imageId: number;
  category: number;
  classId: number;
  schoolId: number;
  sort: number;
  state: number;
  createdAt: string;
  createdBy: number;
  updatedAt: string;
  updatedBy: number;
}

// 儿童数据接口
interface ChildAttendanceData {
  childId: number;
  childName: string;
  childAvatar: string;
  sex: number; // 1-男，2-女
  sexDesc: string;
  state: number; // 状态 1-已入园 2-已离园 3-事假 4-病假 5-考勤无记录
  stateDesc: string;
}

// 心情记录数据接口
interface MoodRecordData {
  id: number;
  childId: number;
  childName: string;
  moodId: number;
  moodName: string;
  imageUrl?: string; // 可选字段，后端暂时没有返回
  classId: number;
  className: string;
  schoolId: number;
  schoolName: string;
  state: number;
  createdAt: string;
  createdBy: number;
  updatedAt: string;
  updatedBy: number;
}

// 图片压缩参数
const IMAGE_COMPRESS_PARAMS = '?x-oss-process=image/resize,m_fill,w_150';

// 添加图片压缩的工具函数
const addImageCompress = (url: string | undefined): string => {
  if (!url) return '';
  return url.includes('?')
    ? `${url}&x-oss-process=image/resize,m_fill,w_150`
    : `${url}${IMAGE_COMPRESS_PARAMS}`;
};

// 定义默认头像URL常量
const DEFAULT_BOY_AVATAR =
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png' +
  IMAGE_COMPRESS_PARAMS;
const DEFAULT_GIRL_AVATAR =
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png' +
  IMAGE_COMPRESS_PARAMS;

// 心情选项 - 从接口获取，这里作为默认备用
const defaultMoodOptions = [
  {
    label: '开心',
    value: 'happy',
    emoji: '1f603', // 😃
    description: '今天很开心',
  },
  {
    label: '兴奋',
    value: 'excited',
    emoji: '1f929', // 🤩
    description: '今天很兴奋',
  },
  {
    label: '爱心',
    value: 'love',
    emoji: '1f970', // 🥰
    description: '今天充满爱',
  },
  {
    label: '平静',
    value: 'calm',
    emoji: '1f60c', // 😌
    description: '今天很平静',
  },
  {
    label: '难过',
    value: 'sad',
    emoji: '1f622', // �
    description: '今天有点难过',
  },
  {
    label: '调皮',
    value: 'naughty',
    emoji: '1f61c', // 😜
    description: '今天很调皮',
  },
];

// 自定义加载组件
const LoadingSpinner: React.FC = () => (
  <div className="flex items-center justify-center h-full">
    <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
  </div>
);

// 自定义儿童头像组件
const ChildAvatar: React.FC<{ src: string; alt: string; size?: number; sex: number }> = ({
  src,
  alt,
  size = 80,
  sex,
}) => {
  const defaultAvatar = sex === 1 ? DEFAULT_BOY_AVATAR : DEFAULT_GIRL_AVATAR;

  return (
    <div
      className="rounded-full overflow-hidden border-4 border-white shadow-lg"
      style={{ width: size, height: size }}
    >
      <img
        src={addImageCompress(src) || defaultAvatar}
        alt={alt}
        className="w-full h-full object-cover"
      />
    </div>
  );
};

// 自定义儿童卡片组件
const ChildCard: React.FC<{
  child: ChildAttendanceData;
  onClick?: () => void;
  moodRecord?: MoodRecordData;
  showStatus?: boolean; // 是否显示状态，默认为 true
}> = ({ child, onClick, moodRecord, showStatus = true }) => {
  // 根据性别确定边框颜色
  const borderColor = child.sex === 1 ? 'border-blue-400' : 'border-pink-400';

  // 当姓名超过4个字符时，只显示前4个字符
  const displayName =
    child.childName.length > 4 ? child.childName.substring(0, 4) : child.childName;

  return (
    <div
      className="flex flex-col items-center cursor-pointer transition-all duration-300 hover:scale-105 select-none"
      onClick={onClick}
      style={{
        width: '80px',
        margin: '4px',
        // 确保心情角标不被裁剪
        overflow: 'visible',
      }}
    >
      {/* 圆形头像容器 - 移除 overflow-hidden 让角标可以超出边界 */}
      <div
        className={`relative flex items-center justify-center rounded-full bg-white shadow-lg border-2 ${borderColor}`}
        style={{
          width: '60px',
          height: '60px',
        }}
      >
        <img
          src={
            addImageCompress(child.childAvatar) ||
            (child.sex === 1 ? DEFAULT_BOY_AVATAR : DEFAULT_GIRL_AVATAR)
          }
          alt={`${child.childName} ${child.sexDesc}`}
          className="w-full h-full object-cover rounded-full"
        />

        {/* 心情角标 - 位置稍微往里，无边框，无阴影 */}
        {moodRecord && moodRecord.imageUrl && (
          <div
            className="absolute rounded-full flex items-center justify-center overflow-hidden z-10"
            style={{
              width: '24px',
              height: '24px',
              top: '-3px',
              right: '-3px',
            }}
            title={`心情: ${moodRecord.moodName}`}
          >
            <img
              src={addImageCompress(moodRecord.imageUrl)}
              alt={moodRecord.moodName}
              className="w-full h-full object-cover rounded-full"
            />
          </div>
        )}
      </div>

      {/* 名字显示在圆形容器外面 */}
      <div
        className="text-center mt-1 text-gray-800 font-bold text-xs leading-tight overflow-hidden text-ellipsis whitespace-nowrap"
        style={{
          width: '60px',
          maxWidth: '60px',
        }}
        title={child.childName.length > 4 ? child.childName : ''}
      >
        {displayName}
      </div>

      {/* 状态标签 */}
      {showStatus && (
        <div className="text-xs text-gray-500 mt-0.5">{child.stateDesc}</div>
      )}
    </div>
  );
};

const ChildrenAttendance: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const [selectedDate, setSelectedDate] = useState<Dayjs>(dayjs());
  const [childrenList, setChildrenList] = useState<ChildAttendanceData[]>([]);
  const [loading, setLoading] = useState(false);

  // 心情数据状态
  const [moodList, setMoodList] = useState<MoodData[]>([]);

  // 心情记录状态
  const [moodRecordsMap, setMoodRecordsMap] = useState<Map<number, MoodRecordData>>(new Map());

  // 将接口获取的心情数据转换为组件需要的格式
  const moodOptions = useMemo(() => {
    if (moodList.length > 0) {
      // 检查是否有 category: 5 的心情
      const category5Moods = moodList.filter((mood) => mood.category === 5);
      const category1Moods = moodList.filter((mood) => mood.category === 1);

      // 如果有 category: 5 的心情，只显示 category: 5 的；否则显示 category: 1 的
      const filteredMoods = category5Moods.length > 0 ? category5Moods : category1Moods;

      console.log('心情筛选结果:', {
        总数: moodList.length,
        category5数量: category5Moods.length,
        category1数量: category1Moods.length,
        最终显示: filteredMoods.length,
        显示类型: category5Moods.length > 0 ? 'category: 5 (班级限定版)' : 'category: 1 (通用版)'
      });

      return filteredMoods
        .sort((a, b) => a.sort - b.sort) // 按sort字段排序
        .map((mood) => ({
          label: mood.moodName,
          value: mood.id.toString(), // 使用id作为value
          imageUrl: mood.imageUrl, // 使用接口返回的图片URL
          description: mood.moodName,
        }));
    }
    // 如果没有从接口获取到数据，使用默认选项
    return defaultMoodOptions;
  }, [moodList]);

  // 全屏功能
  const { isFullscreen, toggleFullscreen } = useFullscreen();

  // 选中的儿童状态
  const [selectedChildForAction, setSelectedChildForAction] = useState<ChildAttendanceData | null>(
    null,
  );

  // 心情选择弹窗状态（未入园时使用）
  const [moodSelectVisible, setMoodSelectVisible] = useState(false);

  // 事假弹窗状态
  const [personalLeaveVisible, setPersonalLeaveVisible] = useState(false);
  const [personalLeaveRemark, setPersonalLeaveRemark] = useState('');

  // 病假弹窗状态
  const [sickLeaveVisible, setSickLeaveVisible] = useState(false);
  const [sickLeaveRemark, setSickLeaveRemark] = useState('');
  const [sickLeaveTypes, setSickLeaveTypes] = useState<any[]>([]);
  const [selectedSickLeaveType, setSelectedSickLeaveType] = useState<string>('');

  // 统计分析弹窗状态
  const [statsVisible, setStatsVisible] = useState(false);
  const [statsLoading, setStatsLoading] = useState(false);
  const [statsData, setStatsData] = useState<any>(null);
  const [radarStatsData, setRadarStatsData] = useState<any>(null);
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>([
    dayjs().startOf('month'), // 当前月的1号
    dayjs().endOf('month'), // 当前月的最后一号
  ]);

  // 儿童统计相关状态
  const [selectedChildForStats, setSelectedChildForStats] = useState<any>(null);
  const [childStatsData, setChildStatsData] = useState<any>(null);
  const [childRadarStatsData, setChildRadarStatsData] = useState<any>(null);

  // 添加全局样式设置PageContainer的padding
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      /* 设置页面容器的padding - 减少padding值 */
      .children-attendance-container .ant-pro-page-container-children-container {
        padding-block-end: 8px;
        padding-inline: 8px;
        background: #FFF;
      }
      /* 全屏状态下的特殊样式 */
      .children-attendance-container.fullscreen-mode {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        z-index: 999 !important;
      }
      .children-attendance-container.fullscreen-mode .ant-pro-page-container-children-content {
        padding: 0 !important;
      }
      /* 确保弹窗在全屏状态下正确显示 */
      .ant-modal-root {
        z-index: 10000 !important;
      }
      .ant-modal-mask {
        z-index: 10000 !important;
      }
      .ant-modal {
        z-index: 10001 !important;
      }
      .ant-drawer-root {
        z-index: 10000 !important;
      }
      .ant-drawer-mask {
        z-index: 10000 !important;
      }
      .ant-drawer {
        z-index: 10001 !important;
      }
      .ant-drawer-content-wrapper {
        z-index: 10001 !important;
      }
      .ant-drawer-content {
        z-index: 10001 !important;
      }
      /* 禁用文本选择 */
      .children-attendance-container * {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }
      .children-attendance-container *::selection {
        background: transparent;
      }
      .children-attendance-container *::-moz-selection {
        background: transparent;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // 获取儿童心情记录
  const fetchChildrenMoodRecords = async () => {
    if (!initialState?.currentClass?.[0]) {
      return;
    }

    try {
      const currentDate = selectedDate.format('YYYY-MM-DD');
      const params = {
        startDate: `${currentDate} 00:00:00`,
        endDate: `${selectedDate.add(1, 'day').format('YYYY-MM-DD')} 00:00:00`,
        pageNum: 1,
        pageSize: 1000, // 获取所有数据
      };

      console.log('获取儿童心情记录参数:', params);
      const response: any = await getChildrenMoodRecordList(params);

      console.log('儿童心情记录响应:', response);

      if (response.status === 0 && response.data && response.data.records) {
        const records = response.data.records as MoodRecordData[];

        // 创建 childId 到心情记录的映射
        const recordsMap = new Map<number, MoodRecordData>();
        records.forEach((record) => {
          recordsMap.set(record.childId, record);
        });
        setMoodRecordsMap(recordsMap);

        console.log('获取到的心情记录数据:', records);
        console.log('心情记录映射:', recordsMap);
      } else {
        console.log('获取儿童心情记录失败:', response.message);
        setMoodRecordsMap(new Map());
      }
    } catch (error: any) {
      console.error('获取儿童心情记录失败:', error);
      setMoodRecordsMap(new Map());
    }
  };

  // 获取儿童出勤列表
  const fetchChildrenAttendanceList = async () => {
    if (!initialState?.currentClass?.[0]) {
      message.error('请先选择班级');
      return;
    }

    setLoading(true);
    try {
      const params = {
        classId: initialState.currentClass[0],
        attendanceDate: selectedDate.format('YYYY-MM-DD'),
      };

      const response: any = await getChildrenAttendanceList(params);

      if (response.status === 0 && response.data) {
        console.log('获取到的数据:', response.data);
        console.log('儿童列表:', response.data.childDetailList);
        setChildrenList(response.data.childDetailList || []);

        // 同时获取心情记录
        await fetchChildrenMoodRecords();
      } else {
        message.error(response.message || '获取儿童出勤列表失败');
      }
    } catch (error: any) {
      console.error('获取儿童出勤列表失败:', error);
      message.error(error.message || '获取儿童出勤列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取儿童心情列表
  const fetchChildrenMoodList = async (forceRefresh = false) => {
    if (!initialState?.currentClass?.[0]) {
      message.error('请先选择班级');
      return;
    }

    // 如果已有数据且不是强制刷新，则直接返回
    if (moodList.length > 0 && !forceRefresh) {
      console.log('使用缓存的心情数据');
      return;
    }

    try {
      const classId = initialState.currentClass[0];
      const params = {
        pageNum: 1,
        pageSize: 100, // 获取所有数据
      };

      console.log('获取儿童心情列表参数:', { classId, ...params });
      const response: any = await getChildrenMoodList(classId, params);

      console.log('儿童心情列表响应:', response);

      if (response.status === 0 && response.data) {
        console.log('获取到的心情数据:', response.data);
        // 存储心情数据到状态中
        if (Array.isArray(response.data)) {
          // 统计不同category的数量
          const categoryStats = response.data.reduce((acc: Record<number, number>, mood: MoodData) => {
            acc[mood.category] = (acc[mood.category] || 0) + 1;
            return acc;
          }, {} as Record<number, number>);

          console.log('心情数据统计:', {
            总数: response.data.length,
            按category分组: categoryStats
          });

          setMoodList(response.data);
          if (forceRefresh) {
            message.success('心情数据刷新成功');
          }
        }
      } else {
        console.log('获取儿童心情列表失败:', response.message);
        message.error(response.message || '获取心情数据失败');
      }
    } catch (error: any) {
      console.error('获取儿童心情列表失败:', error);
      message.error('获取心情数据失败');
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchChildrenAttendanceList();
    fetchChildrenMoodList(); // 同时获取心情数据
  }, [selectedDate, initialState?.currentClass]);

  // 班级变化时清空心情缓存
  useEffect(() => {
    setMoodList([]);
  }, [initialState?.currentClass]);

  // 处理日期变化
  const handleDateChange = (date: Dayjs | null) => {
    if (date) {
      setSelectedDate(date);
    }
  };

  // 获取考勤日期参数 - 如果选择的不是当前日期，则添加 attendanceDate 参数
  const getAttendanceDateParams = () => {
    const today = dayjs().format('YYYY-MM-DD');
    const selectedDateStr = selectedDate.format('YYYY-MM-DD');

    // 如果选择的不是当前日期，添加 attendanceDate 参数
    if (selectedDateStr !== today) {
      // 历史日期 + 当前操作时间
      const currentTime = dayjs().format('HH:mm:ss');
      const attendanceDate = `${selectedDateStr} ${currentTime}`;
      return { attendanceDate };
    }

    // 如果是当前日期，不传 attendanceDate 参数
    return {};
  };

  // 处理儿童点击 - 未入园儿童
  const handleNoRecordChildClick = async (child: ChildAttendanceData) => {
    setSelectedChildForAction(child);
    // 使用缓存的心情数据，不每次都重新获取
    await fetchChildrenMoodList();
    setMoodSelectVisible(true);
  };

  // 处理儿童点击 - 其他状态儿童（已入园、事假、病假）
  const handleOtherChildClick = (child: ChildAttendanceData) => {
    setSelectedChildForAction(child);
    // 通过Popover处理
  };

  // 处理心情选择（未入园状态下的入园考勤）
  const handleMoodSelect = async (moodValue: string) => {
    if (!selectedChildForAction) return;

    // 根据moodValue找到对应的心情名称
    const selectedMood = moodOptions.find((mood) => mood.value === moodValue);
    const moodName = selectedMood ? selectedMood.label : moodValue;

    try {
      // 1. 先保存考勤记录
      const attendanceParams = {
        userType: 0, // 儿童-0，非儿童-1
        userIdList: [selectedChildForAction.childId], // childId数组
        state: 1, // 考勤状态 1-出勤
        remark: `心情: ${moodName}`, // 备注：记录心情名称
        ...getAttendanceDateParams(), // 根据选择的日期决定是否添加 attendanceDate 参数
      };

      console.log('保存考勤参数:', attendanceParams);
      const attendanceResponse: any = await saveChildrenAttendance(attendanceParams);

      if (attendanceResponse.status === 0) {
        // 2. 考勤成功后，保存心情记录
        const moodParams = {
          childId: selectedChildForAction.childId,
          classId: initialState?.currentClass?.[0],
          schoolId: initialState?.currentUser?.currentSchoolId,
          moodId: parseInt(moodValue),
        };

        console.log('保存心情记录参数:', moodParams);
        const moodResponse: any = await saveChildrenMoodRecord(moodParams);

        if (moodResponse.status === 0) {
          message.success(`${selectedChildForAction.childName} 入园考勤成功！心情: ${moodName}`);
        } else {
          console.warn('心情记录保存失败:', moodResponse.message);
          message.success(`${selectedChildForAction.childName} 入园考勤成功！心情记录保存失败`);
        }

        // 关闭弹窗
        setMoodSelectVisible(false);
        setSelectedChildForAction(null);

        // 刷新数据
        await fetchChildrenAttendanceList();
      } else {
        message.error(attendanceResponse.message || '保存考勤失败');
      }
    } catch (error: any) {
      console.error('保存失败:', error);
      message.error(error.message || '保存失败');
    }
  };

  // 处理未入园
  const handleNoRecord = async (child: ChildAttendanceData) => {
    try {
      const params = {
        userType: 0, // 儿童-0，非儿童-1
        userIdList: [child.childId], // childId数组
        state: 5, // 考勤状态 5-未入园
        remark: '未入园', // 备注
        ...getAttendanceDateParams(), // 根据选择的日期决定是否添加 attendanceDate 参数
      };

      console.log('保存未入园参数:', params);
      const response: any = await saveChildrenAttendance(params);

      if (response.status === 0) {
        message.success(`${child.childName} 已标记为未入园`);
        // 刷新数据
        await fetchChildrenAttendanceList();
      } else {
        message.error(response.message || '保存失败');
      }
    } catch (error: any) {
      console.error('保存失败:', error);
      message.error(error.message || '保存失败');
    }
  };

  // 处理事假
  const handlePersonalLeaveSubmit = async (remark: string) => {
    if (!selectedChildForAction) return;

    try {
      const params = {
        userType: 0, // 儿童-0，非儿童-1
        userIdList: [selectedChildForAction.childId], // childId数组
        state: 3, // 考勤状态 3-事假
        remark: remark, // 备注：事假原因
        ...getAttendanceDateParams(), // 根据选择的日期决定是否添加 attendanceDate 参数
      };

      console.log('保存事假参数:', params);
      const response: any = await saveChildrenAttendance(params);

      if (response.status === 0) {
        message.success(`${selectedChildForAction.childName} 事假申请成功！`);

        // 关闭弹窗
        setPersonalLeaveVisible(false);
        setPersonalLeaveRemark('');
        setMoodSelectVisible(false);
        setSelectedChildForAction(null);

        // 刷新数据
        await fetchChildrenAttendanceList();
      } else {
        message.error(response.message || '保存事假失败');
      }
    } catch (error: any) {
      console.error('保存事假失败:', error);
      message.error(error.message || '保存事假失败');
    }
  };

  // 处理病假
  const handleSickLeaveSubmit = async (typeCode: string, remark: string) => {
    if (!selectedChildForAction) return;

    try {
      const params = {
        userType: 0, // 儿童-0，非儿童-1
        userIdList: [selectedChildForAction.childId], // childId数组
        state: 4, // 考勤状态 4-病假
        sickLeaveTypeCode: typeCode, // 病假类型编码
        remark: remark, // 备注：病假详情
        ...getAttendanceDateParams(), // 根据选择的日期决定是否添加 attendanceDate 参数
      };

      console.log('保存病假参数:', params);
      const response: any = await saveChildrenAttendance(params);

      if (response.status === 0) {
        message.success(`${selectedChildForAction.childName} 病假申请成功！`);

        // 关闭弹窗
        setSickLeaveVisible(false);
        setSickLeaveRemark('');
        setSelectedSickLeaveType('');
        setMoodSelectVisible(false);
        setSelectedChildForAction(null);

        // 刷新数据
        await fetchChildrenAttendanceList();
      } else {
        message.error(response.message || '保存病假失败');
      }
    } catch (error: any) {
      console.error('保存病假失败:', error);
      message.error(error.message || '保存病假失败');
    }
  };

  // 获取病假类型列表
  const fetchSickLeaveTypes = async () => {
    try {
      const response = await getSickLeaveTypeList();
      if (response.status === 0 && response.data) {
        setSickLeaveTypes(response.data);
      }
    } catch (error) {
      console.error('获取病假类型失败:', error);
    }
  };

  // 处理事假按钮点击
  const handlePersonalLeaveClick = (child: ChildAttendanceData) => {
    setSelectedChildForAction(child);
    setPersonalLeaveVisible(true);
  };

  // 处理病假按钮点击
  const handleSickLeaveClick = async (child: ChildAttendanceData) => {
    setSelectedChildForAction(child);
    await fetchSickLeaveTypes();
    setSickLeaveVisible(true);
  };

  // 确认事假
  const handlePersonalLeaveConfirm = () => {
    handlePersonalLeaveSubmit(personalLeaveRemark);
  };

  // 确认病假
  const handleSickLeaveConfirm = () => {
    if (!selectedSickLeaveType) {
      message.warning('请选择病假类型');
      return;
    }
    handleSickLeaveSubmit(selectedSickLeaveType, sickLeaveRemark);
  };

  // 获取心情统计数据
  const fetchMoodStats = async () => {
    if (!initialState?.currentClass?.[0]) {
      message.error('请先选择班级');
      return;
    }

    setStatsLoading(true);
    try {
      const params = {
        classId: initialState.currentClass[0], // 使用动态班级ID
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'), // 使用日期范围的结束日期
      };

      console.log('获取心情统计参数:', params);

      // 同时获取柱状图和雷达图数据
      const [barResponse, radarResponse] = await Promise.all([
        getChildrenMoodBarStats(params),
        getChildrenMoodRadarStats(params),
      ]);

      console.log('柱状图统计响应:', barResponse);
      console.log('雷达图统计响应:', radarResponse);

      if (barResponse.status === 0 && barResponse.data) {
        setStatsData(barResponse.data);
      } else {
        message.error(barResponse.message || '获取柱状图数据失败');
      }

      if (radarResponse.status === 0 && radarResponse.data) {
        setRadarStatsData(radarResponse.data);
      } else {
        message.error(radarResponse.message || '获取雷达图数据失败');
      }
    } catch (error: any) {
      console.error('获取统计数据失败:', error);
      message.error(error.message || '获取统计数据失败');
    } finally {
      setStatsLoading(false);
    }
  };

  // 打开统计分析弹窗
  const handleOpenStats = () => {
    setStatsVisible(true);
    fetchMoodStats(); // 打开时立即获取数据
  };

  // 获取儿童统计数据
  const fetchChildMoodStats = async (child: any) => {
    if (!child?.childId) {
      message.error('请选择儿童');
      return;
    }

    setStatsLoading(true);
    try {
      const params = {
        childId: child.childId, // 使用动态儿童ID
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
      };

      console.log('获取儿童心情统计参数:', params);

      // 同时获取儿童点状图和雷达图数据
      const [seriesResponse, radarResponse] = await Promise.all([
        getChildrenMoodSeriesStats(params),
        getChildrenMoodRadarSeriesStats(params),
      ]);

      console.log('儿童点状图统计响应:', seriesResponse);
      console.log('儿童雷达图统计响应:', radarResponse);

      if (seriesResponse.status === 0 && seriesResponse.data) {
        setChildStatsData(seriesResponse.data);
      } else {
        message.error(seriesResponse.message || '获取儿童点状图数据失败');
      }

      if (radarResponse.status === 0 && radarResponse.data) {
        setChildRadarStatsData(radarResponse.data);
      } else {
        message.error(radarResponse.message || '获取儿童雷达图数据失败');
      }
    } catch (error: any) {
      console.error('获取儿童统计数据失败:', error);
      message.error(error.message || '获取儿童统计数据失败');
    } finally {
      setStatsLoading(false);
    }
  };

  // 处理儿童选择
  const handleChildSelect = (child: any) => {
    setSelectedChildForStats(child);
    fetchChildMoodStats(child);
  };

  // 关闭统计分析弹窗
  const handleCloseStats = () => {
    setStatsVisible(false);
    setStatsData(null);
    setRadarStatsData(null);
    setSelectedChildForStats(null);
    setChildStatsData(null);
    setChildRadarStatsData(null);
  };

  // 日期范围变化处理
  const handleDateRangeChange = (dates: any) => {
    if (dates && dates.length === 2) {
      setDateRange([dates[0], dates[1]]);
    }
  };

  // 根据不同状态分类儿童列表
  const presentChildren = childrenList.filter((child) => child.state === 1); // 已入园
  const sickLeaveChildren = childrenList.filter((child) => child.state === 4); // 病假
  const personalLeaveChildren = childrenList.filter((child) => child.state === 3); // 事假
  const noRecordChildren = childrenList.filter((child) => child.state === 5); // 未入园

  // 将待出勤儿童按性别分组
  const boyChildren = noRecordChildren.filter((child) => child.sex === 1); // 男生
  const girlChildren = noRecordChildren.filter((child) => child.sex === 2); // 女生

  console.log('所有儿童列表:', childrenList);
  console.log('已入园儿童:', presentChildren);
  console.log('病假儿童:', sickLeaveChildren);
  console.log('事假儿童:', personalLeaveChildren);
  console.log('未入园儿童:', noRecordChildren);

  return (
    <PageContainer
      className={`children-attendance-container ${isFullscreen ? 'fullscreen-mode' : ''}`}
      style={{
        background: '#fff',
        height: '100vh', // 占满整个视口高度
        padding: 0, // 移除默认padding
      }}
      title={!isFullscreen ? "儿童出勤管理" : undefined} // 全屏时隐藏标题
      extra={
        !isFullscreen
          ? [
              <DatePicker
                key="datePicker"
                value={selectedDate}
                onChange={handleDateChange}
                placeholder="选择日期"
                size="large"
              />,
              <Button
                key="fullscreen"
                icon={<FullscreenOutlined />}
                onClick={toggleFullscreen}
                size="large"
                style={{ marginRight: '8px' }}
              >
                全屏显示
              </Button>,
              <Button
                key="stats"
                type="primary"
                icon={<BarChartOutlined />}
                onClick={handleOpenStats}
                size="large"
              >
                统计分析
              </Button>,
            ]
          : undefined
      }
    >
      {/* 全屏时的顶部工具栏 */}
      {isFullscreen && (
        <FullscreenToolbar onToggleFullscreen={toggleFullscreen} />
      )}

      <div className="flex flex-col overflow-hidden flex-1">
        <div
          className="flex flex-col overflow-hidden"
          style={{
            height: isFullscreen
              ? '100vh' // 全屏时使用100vh
              : 'calc(100vh - 56px - 64px - 16px)', // 非全屏时减去菜单栏56px + 标题栏64px + padding 16px
          }}
        >
          {/* 上部分 - 占据55%高度 */}
          <div className="flex-none px-4 py-2 overflow-hidden" style={{ height: '55%' }}>
            <div className="h-full flex gap-4">
              {/* 左侧 - 今日出勤板块 - 占60%宽度 */}
              <div
                className="flex-none bg-white rounded-lg shadow-md border overflow-hidden"
                style={{ width: '60%' }}
              >
                <div className="h-full flex flex-col">
                  <div className="flex-none px-4 py-3 bg-green-50 border-b">
                    <h3 className="text-lg font-bold text-green-700">
                      今日出勤 ({presentChildren.length}人)
                    </h3>
                  </div>
                  <div className="flex-1 overflow-hidden">
                    {loading ? (
                      <LoadingSpinner />
                    ) : presentChildren.length > 0 ? (
                      <div className="h-full overflow-y-auto p-4">
                        <div
                          className="flex flex-wrap justify-start items-start gap-2"
                          style={{ overflow: 'visible' }}
                        >
                          {presentChildren.map((child) => (
                            <Popover
                              key={child.childId}
                              content={
                                <div className="flex flex-col gap-2 p-2">
                                  <Button
                                    type="primary"
                                    size="small"
                                    className="bg-gray-500 hover:bg-gray-600 border-gray-500"
                                    onClick={() => handleNoRecord(child)}
                                  >
                                    未入园
                                  </Button>
                                  <Button
                                    type="primary"
                                    size="small"
                                    className="bg-yellow-500 hover:bg-yellow-600 border-yellow-500"
                                    onClick={() => handlePersonalLeaveClick(child)}
                                  >
                                    事假
                                  </Button>
                                  <Button
                                    type="primary"
                                    size="small"
                                    className="bg-red-500 hover:bg-red-600 border-red-500"
                                    onClick={() => handleSickLeaveClick(child)}
                                  >
                                    病假
                                  </Button>
                                </div>
                              }
                              title={`${child.childName} - 选择操作`}
                              trigger="click"
                              placement="top"
                            >
                              <div>
                                <ChildCard
                                  child={child}
                                  onClick={() => handleOtherChildClick(child)}
                                  moodRecord={moodRecordsMap.get(child.childId)}
                                />
                              </div>
                            </Popover>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <div className="h-full flex flex-col items-center justify-center text-gray-500">
                        <div className="text-sm">暂无出勤儿童</div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 右侧 - 占40%宽度，分为上下两部分 */}
              <div className="flex-none flex flex-col gap-4" style={{ width: '40%' }}>
                {/* 上半部分 - 今日病假 */}
                <div className="flex-1 bg-white rounded-lg shadow-md border overflow-hidden">
                  <div className="h-full flex flex-col">
                    <div className="flex-none px-4 py-3 bg-red-50 border-b">
                      <h3 className="text-lg font-bold text-red-700">
                        今日病假 ({sickLeaveChildren.length}人)
                      </h3>
                    </div>
                    <div className="flex-1 overflow-hidden">
                      {loading ? (
                        <LoadingSpinner />
                      ) : sickLeaveChildren.length > 0 ? (
                        <div className="h-full overflow-y-auto p-4">
                          <div
                            className="flex flex-wrap justify-start items-start gap-2"
                            style={{ overflow: 'visible' }}
                          >
                            {sickLeaveChildren.map((child) => (
                              <Popover
                                key={child.childId}
                                content={
                                  <div className="flex flex-col gap-2 p-2">
                                    <Button
                                      type="primary"
                                      size="small"
                                      className="bg-gray-500 hover:bg-gray-600 border-gray-500"
                                      onClick={() => handleNoRecord(child)}
                                    >
                                      未入园
                                    </Button>
                                    <Button
                                      type="primary"
                                      size="small"
                                      className="bg-yellow-500 hover:bg-yellow-600 border-yellow-500"
                                      onClick={() => handlePersonalLeaveClick(child)}
                                    >
                                      事假
                                    </Button>
                                    <Button
                                      type="primary"
                                      size="small"
                                      className="bg-red-500 hover:bg-red-600 border-red-500"
                                      onClick={() => handleSickLeaveClick(child)}
                                    >
                                      病假
                                    </Button>
                                  </div>
                                }
                                title={`${child.childName} - 选择操作`}
                                trigger="click"
                                placement="top"
                              >
                                <div>
                                  <ChildCard
                                    child={child}
                                    onClick={() => handleOtherChildClick(child)}
                                  />
                                </div>
                              </Popover>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <div className="h-full flex flex-col items-center justify-center text-gray-500">
                          <div className="text-sm">暂无病假儿童</div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* 下半部分 - 今日事假 */}
                <div className="flex-1 bg-white rounded-lg shadow-md border overflow-hidden">
                  <div className="h-full flex flex-col">
                    <div className="flex-none px-4 py-3 bg-yellow-50 border-b">
                      <h3 className="text-lg font-bold text-yellow-700">
                        今日事假 ({personalLeaveChildren.length}人)
                      </h3>
                    </div>
                    <div className="flex-1 overflow-hidden">
                      {loading ? (
                        <LoadingSpinner />
                      ) : personalLeaveChildren.length > 0 ? (
                        <div className="h-full overflow-y-auto p-4">
                          <div
                            className="flex flex-wrap justify-start items-start gap-2"
                            style={{ overflow: 'visible' }}
                          >
                            {personalLeaveChildren.map((child) => (
                              <Popover
                                key={child.childId}
                                content={
                                  <div className="flex flex-col gap-2 p-2">
                                    <Button
                                      type="primary"
                                      size="small"
                                      className="bg-gray-500 hover:bg-gray-600 border-gray-500"
                                      onClick={() => handleNoRecord(child)}
                                    >
                                      未入园
                                    </Button>
                                    <Button
                                      type="primary"
                                      size="small"
                                      className="bg-yellow-500 hover:bg-yellow-600 border-yellow-500"
                                      onClick={() => handlePersonalLeaveClick(child)}
                                    >
                                      事假
                                    </Button>
                                    <Button
                                      type="primary"
                                      size="small"
                                      className="bg-red-500 hover:bg-red-600 border-red-500"
                                      onClick={() => handleSickLeaveClick(child)}
                                    >
                                      病假
                                    </Button>
                                  </div>
                                }
                                title={`${child.childName} - 选择操作`}
                                trigger="click"
                                placement="top"
                              >
                                <div>
                                  <ChildCard
                                    child={child}
                                    onClick={() => handleOtherChildClick(child)}
                                    moodRecord={moodRecordsMap.get(child.childId)}
                                  />
                                </div>
                              </Popover>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <div className="h-full flex flex-col items-center justify-center text-gray-500">
                          <div className="text-sm">暂无事假儿童</div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 下部分 - 待出勤板块 - 占据45%高度 */}
          <div className="flex-none px-4 py-2 overflow-hidden" style={{ height: '45%' }}>
            <div className="h-full flex gap-4">
              {/* 左侧 - 男生区域 */}
              <div className="flex-1 bg-white rounded-lg shadow-md border overflow-hidden">
                <div className="h-full flex flex-col">
                  <div className="flex-none px-4 py-3 bg-blue-50 border-b">
                    <h3 className="text-lg font-bold text-blue-700">
                      男生待出勤 ({boyChildren.length}人)
                    </h3>
                  </div>
                  <div className="flex-1 overflow-hidden">
                    {loading ? (
                      <LoadingSpinner />
                    ) : boyChildren.length > 0 ? (
                      <div className="h-full overflow-y-auto p-4">
                        <div
                          className="flex flex-wrap justify-start items-start gap-2"
                          style={{ overflow: 'visible' }}
                        >
                          {boyChildren.map((child) => (
                            <ChildCard
                              key={child.childId}
                              child={child}
                              onClick={() => handleNoRecordChildClick(child)}
                              moodRecord={moodRecordsMap.get(child.childId)}
                              showStatus={false}
                            />
                          ))}
                        </div>
                      </div>
                    ) : (
                      <div className="h-full flex flex-col items-center justify-center text-gray-500">
                        <div className="text-sm">暂无男生待出勤</div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 右侧 - 女生区域 */}
              <div className="flex-1 bg-white rounded-lg shadow-md border overflow-hidden">
                <div className="h-full flex flex-col">
                  <div className="flex-none px-4 py-3 bg-pink-50 border-b">
                    <h3 className="text-lg font-bold text-pink-700">
                      女生待出勤 ({girlChildren.length}人)
                    </h3>
                  </div>
                  <div className="flex-1 overflow-hidden">
                    {loading ? (
                      <LoadingSpinner />
                    ) : girlChildren.length > 0 ? (
                      <div className="h-full overflow-y-auto p-4">
                        <div
                          className="flex flex-wrap justify-start items-start gap-2"
                          style={{ overflow: 'visible' }}
                        >
                          {girlChildren.map((child) => (
                            <ChildCard
                              key={child.childId}
                              child={child}
                              onClick={() => handleNoRecordChildClick(child)}
                              moodRecord={moodRecordsMap.get(child.childId)}
                              showStatus={false}
                            />
                          ))}
                        </div>
                      </div>
                    ) : (
                      <div className="h-full flex flex-col items-center justify-center text-gray-500">
                        <div className="text-sm">暂无女生待出勤</div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 事假弹窗 */}
      <Modal
        title={<div className="text-lg font-bold text-yellow-600">事假申请</div>}
        open={personalLeaveVisible}
        onOk={handlePersonalLeaveConfirm}
        onCancel={() => {
          setPersonalLeaveVisible(false);
          setPersonalLeaveRemark('');
        }}
        okText="确认"
        cancelText="取消"
        okButtonProps={{
          className: 'bg-yellow-500 hover:bg-yellow-600 border-yellow-500 font-medium',
          size: 'large',
        }}
        cancelButtonProps={{
          size: 'large',
        }}
        width={500}
        centered
        zIndex={10002} // 确保在全屏模式下正确显示
      >
        <div className="py-6">
          <p className="mb-4 text-gray-700 font-medium text-base">请填写事假备注：</p>
          <Input.TextArea
            value={personalLeaveRemark}
            onChange={(e) => setPersonalLeaveRemark(e.target.value)}
            placeholder="请填写"
            rows={5}
            maxLength={200}
            showCount
            className="rounded-lg"
            style={{ fontSize: '14px', lineHeight: '1.6' }}
          />
        </div>
      </Modal>

      {/* 病假弹窗 */}
      <Modal
        title={<div className="text-lg font-bold text-red-600">病假申请</div>}
        open={sickLeaveVisible}
        onOk={handleSickLeaveConfirm}
        onCancel={() => {
          setSickLeaveVisible(false);
          setSickLeaveRemark('');
          setSelectedSickLeaveType('');
        }}
        okText="确认"
        cancelText="取消"
        okButtonProps={{
          className: 'bg-red-500 hover:bg-red-600 border-red-500 font-medium',
          size: 'large',
        }}
        cancelButtonProps={{
          size: 'large',
        }}
        width={700}
        centered
        zIndex={10002} // 确保在全屏模式下正确显示
      >
        <div className="py-6">
          <div className="mb-8">
            <p className="mb-4 text-gray-700 font-medium text-base">请选择病假类型：</p>
            <div
              className="p-4 bg-gray-50 rounded-lg border border-gray-200"
              style={{
                columnCount: 3,
                columnGap: '16px',
                columnFill: 'balance',
              }}
            >
              {sickLeaveTypes.map((type: any) => (
                <Tag.CheckableTag
                  key={type.dictItemCode}
                  checked={selectedSickLeaveType === type.dictItemCode}
                  onChange={(checked) => {
                    setSelectedSickLeaveType(checked ? type.dictItemCode : '');
                  }}
                  className={`
                    inline-block w-full mb-3 px-3 py-2 text-sm font-medium rounded-lg border-2 transition-all duration-300 cursor-pointer select-none
                    ${
                      selectedSickLeaveType === type.dictItemCode
                        ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white border-blue-500 shadow-lg transform scale-105'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-blue-400 hover:bg-blue-50 hover:text-blue-600 shadow-sm hover:shadow-md hover:transform hover:scale-102'
                    }
                  `}
                  style={{
                    textAlign: 'center',
                    lineHeight: '1.3',
                    breakInside: 'avoid',
                    pageBreakInside: 'avoid',
                  }}
                >
                  {type.dictItemName}
                </Tag.CheckableTag>
              ))}
            </div>
          </div>

          <div>
            <p className="mb-4 text-gray-700 font-medium text-base">请填写病假备注：</p>
            <Input.TextArea
              value={sickLeaveRemark}
              onChange={(e) => setSickLeaveRemark(e.target.value)}
              placeholder="请填写"
              rows={5}
              maxLength={200}
              showCount
              className="rounded-lg"
              style={{ fontSize: '14px', lineHeight: '1.6' }}
            />
          </div>
        </div>
      </Modal>

      {/* 心情选择弹窗（未入园状态使用） */}
      <MoodSelectDrawer
        visible={moodSelectVisible}
        onClose={() => {
          setMoodSelectVisible(false);
          setSelectedChildForAction(null);
        }}
        selectedChild={selectedChildForAction}
        onMoodSelect={handleMoodSelect}
        onPersonalLeave={handlePersonalLeaveSubmit}
        onSickLeave={handleSickLeaveSubmit}
        moodOptions={moodOptions}
        ChildAvatar={ChildAvatar}
        getSickLeaveTypeList={getSickLeaveTypeList}
        onRefreshMoodList={() => fetchChildrenMoodList(true)} // 传递强制刷新函数
      />

      {/* 统计分析弹窗 */}
      <MoodStatsModal
        visible={statsVisible}
        onClose={handleCloseStats}
        statsData={statsData}
        radarStatsData={radarStatsData}
        statsLoading={statsLoading}
        dateRange={dateRange}
        onDateRangeChange={handleDateRangeChange}
        onRefreshData={fetchMoodStats}
        childrenList={childrenList}
        onChildSelect={handleChildSelect}
        selectedChild={selectedChildForStats}
        childStatsData={childStatsData}
        childRadarStatsData={childRadarStatsData}
      />
    </PageContainer>
  );
};

export default ChildrenAttendance;
