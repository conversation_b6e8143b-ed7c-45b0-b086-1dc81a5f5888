import { Footer } from '@/components';
import { adminLogin, adminLoginPwd, setToken, getLoginQr, getQrcodeStatus } from '@/services/api';
// import {getCaptcha} from '@/services/api';
// import { LockOutlined, MobileOutlined, UserOutlined } from '@ant-design/icons';
// import { LoginForm, ProFormCaptcha, ProFormText } from '@ant-design/pro-components';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { LoginForm, ProFormText } from '@ant-design/pro-components';

// import { FormattedMessage, Helmet, useIntl, useModel } from '@umijs/max';
import { Helmet, useIntl, useModel } from '@umijs/max';
import { Alert, Tabs, message } from 'antd';
import { createStyles } from 'antd-style';
import React, { useState, useEffect } from 'react';
import Settings from '../../../../config/defaultSettings';

const useStyles = createStyles(({ token }) => {
  return {
    action: {
      marginLeft: '8px',
      color: 'rgba(0, 0, 0, 0.2)',
      fontSize: '24px',
      verticalAlign: 'middle',
      cursor: 'pointer',
      transition: 'color 0.3s',
      '&:hover': {
        color: token.colorPrimaryActive,
      },
    },
    lang: {
      width: 42,
      height: 42,
      lineHeight: '42px',
      position: 'fixed',
      right: 16,
      borderRadius: token.borderRadius,
      ':hover': {
        backgroundColor: token.colorBgTextHover,
      },
    },
    container: {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage:
        "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
      backgroundSize: '100% 100%',
    },
  };
});

const LoginMessage: React.FC<{
  content: string;
}> = ({ content }) => {
  return (
    <Alert
      style={{
        marginBottom: 24,
      }}
      message={content}
      type="error"
      showIcon
    />
  );
};

type LoginType = 'phone' | 'account';

const Login: React.FC = () => {
  const [userLoginState, setUserLoginState] = useState({ errorMessage: '' });
  const { setInitialState } = useModel('@@initialState');
  const { styles } = useStyles();
  const intl = useIntl();

  const { errorMessage } = userLoginState;

  const [loginType, setLoginType] = useState<LoginType>('account');
  const [qrcodeUrl, setQrcodeUrl] = useState<string>('');
  const handleSubmit = async (values: API.LoginParams) => {
    try {
      // 登录
      let res;
      if (loginType === 'phone') {
        res = await adminLogin({ ...values, countryCode: '+86' });
      } else {
        res = await adminLoginPwd({ ...values });
      }

      if (res?.status === 0) {
        message.success('登录成功');

        await setToken(res?.data?.token);
        await setInitialState((s) => ({ ...s, currentUser: res?.data?.user } as any));

        /** 此方法会跳转到 redirect 参数所在的位置 */
        const urlParams = new URL(window.location.href).searchParams;
        window.location.href = urlParams.get('redirect') || '/';

        return;
      }

      message.error(res?.message);
    } catch (error: any) {
      setUserLoginState({ errorMessage: error.message });
      message.error(error.message);
    }
  };
  const handleQrcodeUrl = async () => {
    // 判断环境
    console.log(process.env.APP_ENV);
    let envVersion = process.env.APP_ENV === 'production'? 'release' : 'trial'
    let o = {
      appId: 'wx81c6c590a0ec7e37',
      page: 'pages/login/authorizeLogin',
      envVersion,
    };
    getLoginQr(o).then((res) => {
      if (res?.status === 0) {
        let payload = res?.data?.payload;
        setQrcodeUrl('https://s.mypacelab.com/' + res?.data?.qrCodeUrl);
        // 轮询二维码状态
        let timer = setInterval(async () => {
          const res = await getQrcodeStatus({ payload });
          if (res?.status === 0) {
            if (res.data.token) {
              const { token } = res.data.token;
              clearInterval(timer);
              message.success('登录成功');
              await setToken(token);
              await setInitialState((s) => ({ ...s, currentUser: res?.data?.user } as any));

              /** 此方法会跳转到 redirect 参数所在的位置 */
              const urlParams = new URL(window.location.href).searchParams;
              window.location.href = urlParams.get('redirect') || '/';
            }
          }
        }, 4000);
        return;
      }
      message.error('生成失败，请刷新重试');
    });
  };
  useEffect(() => {
    if (loginType === 'phone' && !qrcodeUrl) {
      handleQrcodeUrl();
    }
  }, []);

  return (
    <div className={styles.container}>
      <Helmet>
        <title>
          {intl.formatMessage({
            id: 'menu.login',
            defaultMessage: '登录页',
          })}
          - {Settings.title}
        </title>
      </Helmet>
      {/* <Lang /> */}
      <div
        style={{
          flex: '1',
          padding: '32px 0',
        }}
      >
        <div style={{ margin: '16px auto 16px', width: 328, fontSize: '16px' }}>幼立方</div>

        <LoginForm
          contentStyle={{
            minWidth: 280,
            maxWidth: '75vw',
          }}
          // logo={<img alt="logo" src="/logo.png" />}
          // title="登录"
          // subTitle="Persistent Action Creates Education"
          initialValues={{
            autoLogin: true,
          }}
          onFinish={async (values) => {
            await handleSubmit(values as API.LoginParams);
          }}
        >
          {errorMessage && <LoginMessage content={errorMessage} />}
          <Tabs
            centered
            activeKey={loginType}
            onChange={(activeKey) => {
              setLoginType(activeKey as LoginType);
              if (activeKey === 'phone' && !qrcodeUrl) {
                handleQrcodeUrl();
              }
            }}
          >
            <Tabs.TabPane key={'account'} tab={'账号密码登录'} />
            <Tabs.TabPane key={'phone'} tab={'扫码登录'} />
          </Tabs>
          {loginType === 'phone' && (
            <>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: '100%',
                  height: '200px',
                  margin: '20px 0',
                }}
              >
                {qrcodeUrl ? (
                  <img src={qrcodeUrl} style={{ width: '200px', height: '200px' }} alt={'生成中'} />
                ) : (
                  <span>生成中...</span>
                )}
              </div>
              {/* <ProFormText
                fieldProps={{
                  size: 'large',
                  prefix: <MobileOutlined />,
                }}
                name="mobile"
                placeholder={intl.formatMessage({
                  id: 'pages.login.phoneNumber.placeholder',
                  defaultMessage: '手机号',
                })}
                rules={[
                  {
                    required: true,
                    message: (
                      <FormattedMessage
                        id="pages.login.phoneNumber.required"
                        defaultMessage="请输入手机号！"
                      />
                    ),
                  },
                  {
                    pattern: /^1\d{10}$/,
                    message: (
                      <FormattedMessage
                        id="pages.login.phoneNumber.invalid"
                        defaultMessage="手机号格式错误！"
                      />
                    ),
                  },
                ]}
              />
              <ProFormCaptcha
                fieldProps={{
                  size: 'large',
                  prefix: <LockOutlined />,
                }}
                captchaProps={{
                  size: 'large',
                }}
                placeholder={intl.formatMessage({
                  id: 'pages.login.captcha.placeholder',
                  defaultMessage: '请输入验证码',
                })}
                captchaTextRender={(timing, count) => {
                  if (timing) {
                    return `${count} ${intl.formatMessage({
                      id: 'pages.getCaptchaSecondText',
                      defaultMessage: '获取验证码',
                    })}`;
                  }
                  return intl.formatMessage({
                    id: 'pages.login.phoneLogin.getVerificationCode',
                    defaultMessage: '获取验证码',
                  });
                }}
                name="code"
                rules={[
                  {
                    required: true,
                    message: (
                      <FormattedMessage
                        id="pages.login.captcha.required"
                        defaultMessage="请输入验证码！"
                      />
                    ),
                  },
                ]}
                phoneName="mobile"
                onGetCaptcha={async (phone) => {
                  const result = await getCaptcha({
                    mobile: phone,
                    countryCode: '+86',
                  });

                  if (!result) {
                    return;
                  }
                  message.success('获取验证码成功！');
                }}
              /> */}
            </>
          )}
          {loginType === 'account' && (
            <>
              <ProFormText
                name="mobile"
                fieldProps={{
                  size: 'large',
                  prefix: <UserOutlined className={'prefixIcon'} />,
                }}
                placeholder={'手机号'}
                rules={[
                  {
                    required: true,
                    message: '请输入用户名!',
                  },
                ]}
              />
              <ProFormText.Password
                name="pwd"
                fieldProps={{
                  size: 'large',
                  prefix: <LockOutlined className={'prefixIcon'} />,
                  strengthText:
                    'Password should contain numbers, letters and special characters, at least 8 characters long.',
                  // statusRender: (value) => {
                  //   const getStatus = () => {
                  //     if (value && value.length > 12) {
                  //       return 'ok';
                  //     }
                  //     if (value && value.length > 6) {
                  //       return 'pass';
                  //     }
                  //     return 'poor';
                  //   };
                  //   const status = getStatus();
                  //   if (status === 'pass') {
                  //     return (
                  //       <div style={{ color: token.colorWarning }}>
                  //         强度：中
                  //       </div>
                  //     );
                  //   }
                  //   if (status === 'ok') {
                  //     return (
                  //       <div style={{ color: token.colorSuccess }}>
                  //         强度：强
                  //       </div>
                  //     );
                  //   }
                  //   return (
                  //     <div style={{ color: token.colorError }}>强度：弱</div>
                  //   );
                  // },
                }}
                placeholder={'密码'}
                rules={[
                  {
                    required: true,
                    message: '请输入密码！',
                  },
                ]}
              />
            </>
          )}
        </LoginForm>
      </div>
      <Footer />
    </div>
  );
};

export default Login;
