import { DeleteOutlined } from '@ant-design/icons';
import { ProFormSelect } from '@ant-design/pro-components';
import { Flex, FormInstance } from 'antd';
import { FormListOperation } from 'antd/lib';
import { useEffect, useState } from 'react';
import { targets } from './mock';

const getTarget = (id: any) => {
  return Promise.resolve(targets.filter((t) => Number(t.matrix3_id) === id));
};
export interface MatrixeOptionProps {
  next: MatrixeOptionProps[];
  label: string;
  value: number;
}

function getMatrixeOptionsById(options: MatrixeOptionProps[], id: number) {
  const t = options.find((o) => o.value === id);
  return t ? t.next : [];
}

export const MatrixSelectItem = (props: {
  action: FormListOperation & {
    getCurrentRowData: () => any;
    setCurrentRowData: (data: any) => void;
  };

  activeRow?: API.ClassAreaProductProps;
  index: number;
  matrixeOptions: MatrixeOptionProps[];
  disabled?: boolean;
  form: FormInstance;
}) => {
  const [matrix2Options, setMatrix2Options] = useState<MatrixeOptionProps[]>([]);
  const [matrix3Options, setMatrix3Options] = useState<MatrixeOptionProps[]>([]);

  const [targetOptions, setTargetOptions] = useState<any>([]);

  const { action, index, matrixeOptions } = props;

  useEffect(() => {
    const data = action.getCurrentRowData();
    let o2: any = [];
    let o3: any = [];

    if (data.matrix1Id) {
      o2 = getMatrixeOptionsById(matrixeOptions, data.matrix1Id);
    }
    if (data.matrix2Id) {
      o3 = getMatrixeOptionsById(o2, data.matrix2Id);
    }
    if (data.matrix3Id) {
      getTarget(data.matrix3Id).then((res) => {
        setTargetOptions(
          res.map((v) => ({
            label: v.title,
            value: v.id,
          })),
        );
      });
    }

    setMatrix2Options(o2);
    setMatrix3Options(o3);
  }, [props.activeRow, matrixeOptions]);

  return (
    <div style={{ position: 'relative' }}>
      <Flex wrap={'wrap'} align="center" gap={16}>
        <ProFormSelect
          className="MatrixSelectItem-select"
          width={280}
          style={{marginBottom: 0}}
          disabled={props.disabled}
          rules={[{ required: true }]}
          onChange={(matrix1Id: number, option: any) => {
            const data = action.getCurrentRowData();
            action.setCurrentRowData({
              ...data,
              matrix1Title: option.label,
            });
            setMatrix2Options(getMatrixeOptionsById(matrixeOptions, matrix1Id));
          }}
          options={matrixeOptions}
          name="matrix1Id"
          label="领域"
        />
        <ProFormSelect
        className="MatrixSelectItem-select"
          width={280}
          disabled={props.disabled}
          rules={[{ required: true }]}
          onChange={(matrix2Id: number, option: any) => {
            const data = action.getCurrentRowData();
            action.setCurrentRowData({
              ...data,
              matrix2Title: option.label,
            });
            setMatrix3Options(getMatrixeOptionsById(matrix2Options, matrix2Id));
          }}
          options={matrix2Options}
          name="matrix2Id"
          label="维度"
        />
        <ProFormSelect
        className="MatrixSelectItem-select"
          width={280}
          disabled={props.disabled}
          rules={[{ required: true }]}
          onChange={(matrix3Id: number, option: any) => {
            const data = action.getCurrentRowData();
            getTarget(matrix3Id).then((res) => {
              console.log(res, matrix3Id);
              setTargetOptions(
                res.map((v: any) => ({
                  label: v.title,
                  value: v.id,
                })),
              );
            });
            action.setCurrentRowData({
              ...data,
              matrix3Title: option.label,
            });
          }}
          options={matrix3Options}
          name="matrix3Id"
          label="子维度"
        />
        <ProFormSelect
        className="MatrixSelectItem-select"
          width={280}
          disabled={props.disabled}
          rules={[{ required: true }]}
          onChange={(_: number, option: any) => {
            const data = action.getCurrentRowData();
            action.setCurrentRowData({
              ...data,
              targetTitle: option.label,
            });
          }}
          options={targetOptions}
          name="targetId"
          label="指标"
        />
        {!props.disabled ? (
          <DeleteOutlined
            className='DeleteOutlined-delete'
            style={{ marginBottom: 24 }}
            onClick={() => (action as any)?.remove?.(index)} // 删除当前行
          />
        ) : null}
      </Flex>
    </div>
  );
};
