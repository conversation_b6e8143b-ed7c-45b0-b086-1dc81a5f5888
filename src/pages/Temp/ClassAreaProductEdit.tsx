import AntForms, { FormFields, FormTypes } from '@/components/UseForms';

import { DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { PageContainer, ProFormUploadButton } from '@ant-design/pro-components';
import { Col, Row } from 'antd';
import { useForm } from 'antd/es/form/Form';
import TextArea from 'antd/es/input/TextArea';
import { useEffect, useState } from 'react';
import MatrixSelect from './MatrixSelect';
import { areaOptions, matrixeOptions, SubjectActivityInterestScoreEnumDesc } from './mock';
import './media.scss'
const ClassAreaProductDetail = () => {
  const [form] = useForm();
  const [matrixForm] = useForm();

  useEffect(() => {
    form.setFieldsValue({
      title: '数字分解滚珠',
      areaId: 3128,
      interest: 8,
      info: '底盒+珠子10颗+水性笔+股子1个',
      purpose: '学习10以内数字的合成与分解，直观了解数与量的关系，同时锻炼幼儿的手眼协调能力。',
      minAge: 4,
      maxAge: 6,
      suitable: 1,
    });
  }, []);
  const [games, setGames] = useState<{ game: string; teacher: string }[]>([
    {
      game: `1.写出需要分解的数字，取出相同数量的珠子:
2.从顶部倒入让珠子通过棍子分到两边;`,
      teacher: `引导幼儿记录要分解的数字和分解后的两个数字，当幼儿写不出来时，教师可以引导幼儿数珠子数量。如果幼儿不能书写数字，则需要引导幼儿观察旁边的数字形`,
    },
    {
      game: `1.抛十面般子，取般子朝上数字
2.首先书写需要分解骰子数字;`,
      teacher: `引导幼儿记录要分解的数字和分解后的两个数字，当幼儿写不出来时，教师可以引导幼儿数珠子数量。如果幼儿不能书写数字，则需要引导幼儿观察旁边的数字形`,
    },
    {
      game: `1.随意抛投珠子数量;
2.通过珠子顶部柱子分到两侧:`,
      teacher: `观察幼儿对材料的理解与命题书写的认识`,
    },
  ]);

  const [originData] = useState<any>({});

  const [childFormData] = useState({});
  const [fileList, setFileList] = useState([
    {
      url: 'https://s.mypacelab.com/3d/5a/8be46bf3d3504ebd81922a0b924c5a3d.jpg',
      uid: '1',
      name: '预览图',
    },
  ]);
  const childForms: FormFields = [
    {
      label: '材料名称',
      name: 'title',
      formType: FormTypes.input,
      rules: [{ required: true }],
    },
  ];

  const matForms: FormFields = [
    {
      label: '区域',
      name: 'areaId',
      rules: [{ required: true }],
      formType: FormTypes.select,

      showSearch: true,
      filterOption(input, option) {
        return !!option?.value?.toString().includes(input);
      },
      value: 'id',
      options: areaOptions,
    },

    {
      label: '幼儿兴趣',
      name: 'interest',
      formType: FormTypes.select,
      options: SubjectActivityInterestScoreEnumDesc,
    },

    {
      label: '材料概述',
      name: 'info',
      placeholder: `请输入材料概述，可以参考如下内容：材料由什么部分构成（这份材料包含什么部分，比如“1. 彩色积木块20块；2. 名字卡片；3. 筛子；4. 笔；5. 记录单”）？幼儿会怎么操作这份材料？材料和操作单是否易于幼儿理解和操作？这份材料有什么难点？玩的时候可能会出现什么情况？这份材料是否可以达成其他区域或领域的目标？`,
      formType: FormTypes.textarea,
    },

    {
      label: '投放目的',
      name: 'purpose',
      placeholder:
        '请输入投放目的，可以参考如下内容：为什么要投放这份材料？材料的目标是什么？是否是幼儿的需求？幼儿是否有兴趣？是否符合幼儿生活经验？是否吸引幼儿？',
      formType: FormTypes.textarea,
    },

    {
      label: '最小年龄',
      name: 'minAge',
      formType: FormTypes.input,
      type: 'number',
    },
    {
      label: '最大年龄',
      name: 'maxAge',
      formType: FormTypes.input,
      type: 'number',
    },
    {
      label: '适宜人数',
      name: 'suitable',
      formType: FormTypes.input,
      type: 'number',
    },
  ];

  return (
    <PageContainer
      style={{
        background: '#fff',
        position: 'fixed',
        width: '100%',
        height: '100%',
        overflowY: 'auto',
      }}
    >
      <h3>材料基本信息</h3>

      <Row>
        <AntForms
          style={{ width: '100%' }}
          form={form}
          formProps={{
            disabled: true,
            initialValues: childFormData,
          }}
          forms={childForms}
        ></AntForms>
      </Row>
      <Row>
        <Col span={24}>
          <div className="preview-image-text">
            预览图：
          </div>
        </Col>

        <div className="preview-image-content">
          <ProFormUploadButton
            fileList={fileList}
            onChange={({ file }) => {
              setFileList([file as any]);
            }}
          />
        </div>
      </Row>

      <h3>材料分析</h3>
      <Row>
        <AntForms
          style={{ width: '100%' }}
          form={form}
          formProps={{
            disabled: true,
            initialValues: childFormData,
          }}
          forms={matForms}
        ></AntForms>
      </Row>
      <h3
        style={{
          padding: '12px 0',
        }}
      >
        玩法介绍：
        <PlusCircleOutlined onClick={() => setGames([...games, { game: '', teacher: '' }])} />
      </h3>

      {games.map((g, index) => (
        <div
          style={{
            marginBottom: 12,
          }}
          key={index}
        >
          <Row style={{ lineHeight: '32px' }}>
            玩法介绍：{' '}
            <DeleteOutlined
              onClick={() => setGames((games) => games.filter((_, i) => i !== index))}
              style={{ marginLeft: 12, fontSize: 16, cursor: 'pointer' }}
            />
          </Row>
          <Row>
            <TextArea
              value={g.game}
              placeholder="请输入玩法介绍，一种玩法一行，玩法需要体现材料的层次性，玩法由易到难均需要考虑到位，可以参考核心经验进行玩法编写。可以思考：这份材料怎么玩？（可以列出玩法的步骤）这个玩法对应什么核心经验？玩法的重难点是什么？幼儿可能会遇到什么困难？"
              onChange={(e) => {
                setGames([
                  ...games.slice(0, index),
                  {
                    ...g,
                    game: e.target.value,
                  },
                  ...games.slice(index + 1),
                ]);
              }}
            />
          </Row>
          <Row style={{ lineHeight: '32px' }}>教师支持：</Row>
          <Row>
            <TextArea
              value={g.teacher}
              placeholder="请输入本玩法的教师支持，教师在幼儿遇到困难时，可以如何支持和引导"
              onChange={(e) => {
                setGames([
                  ...games.slice(0, index),
                  {
                    ...g,
                    teacher: e.target.value,
                  },
                  ...games.slice(index + 1),
                ]);
              }}
            />
          </Row>
        </div>
      ))}

      <h3
        style={{
          padding: '12px 0',
        }}
      >
        核心经验：
      </h3>

      <div style={{ width: '100%' }}>
        <MatrixSelect
          matrixeOptions={matrixeOptions}
          isTemp={true}
          form={matrixForm}
          activeRow={originData}
        />
      </div>
    </PageContainer>
  );
};

export default ClassAreaProductDetail;
