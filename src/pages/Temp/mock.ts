const o = [
  {
    id: 1244,
    title: '数学',
    grade: 1,
    isLeaf: false,
    pid: 0,
    children: [
      {
        id: 1245,
        title: '数字、基数和计数',
        grade: 2,
        isLeaf: false,
        pid: 1244,
        children: [
          {
            id: 1246,
            title: '基数',
            grade: 3,
            isLeaf: true,
            pid: 1245,
          },
          {
            id: 1247,
            title: '计数',
            grade: 3,
            isLeaf: true,
            pid: 1245,
          },
          {
            id: 1248,
            title: '数的表征系统',
            grade: 3,
            isLeaf: true,
            pid: 1245,
          },
          {
            id: 1249,
            title: '数量比较、数序与排序',
            grade: 3,
            isLeaf: true,
            pid: 1245,
          },
          {
            id: 1254,
            title: '序数',
            grade: 3,
            isLeaf: true,
            pid: 1245,
          },
          {
            id: 1255,
            title: '估数',
            grade: 3,
            isLeaf: true,
            pid: 1245,
          },
        ],
      },
      {
        id: 1250,
        title: '代数和算式',
        grade: 2,
        isLeaf: false,
        pid: 1244,
        children: [
          {
            id: 1253,
            title: '数量的分合',
            grade: 3,
            isLeaf: true,
            pid: 1250,
          },
          {
            id: 1257,
            title: '数的运算',
            grade: 3,
            isLeaf: true,
            pid: 1250,
          },
        ],
      },
      {
        id: 1252,
        title: '几何和空间',
        grade: 2,
        isLeaf: false,
        pid: 1244,
        children: [
          {
            id: 1258,
            title: '几何图形的特征',
            grade: 3,
            isLeaf: true,
            pid: 1252,
          },
          {
            id: 1259,
            title: '分析、比较、创造和组合形状',
            grade: 3,
            isLeaf: true,
            pid: 1252,
          },
          {
            id: 1260,
            title: '空间方位',
            grade: 3,
            isLeaf: true,
            pid: 1252,
          },
        ],
      },
      {
        id: 1262,
        title: '测量和数据',
        grade: 2,
        isLeaf: false,
        pid: 1244,
        children: [
          {
            id: 1263,
            title: '长度、面积和体积测量',
            grade: 3,
            isLeaf: true,
            pid: 1262,
          },
          {
            id: 1264,
            title: '测量行为',
            grade: 3,
            isLeaf: true,
            pid: 1262,
          },
          {
            id: 1266,
            title: '数据分析',
            grade: 3,
            isLeaf: true,
            pid: 1262,
          },
        ],
      },
    ],
  },
];

function filterMatrix(matrix: any) {
  return matrix.map((m: any) => {
    if (m.children) {
      return {
        label: m.title,
        value: m.id,
        next: filterMatrix(m.children),
      };
    }
    return {
      label: m.title,
      value: m.id,
    };
  });
}

export const matrixeOptions = filterMatrix(o);

export const SubjectActivityInterestScoreEnumDesc = [
  {
    value: 2,
    label: '非常不感兴趣',
  },
  {
    value: 4,
    label: '比较不感兴趣',
  },
  {
    value: 6,
    label: '一般',
  },
  {
    value: 8,
    label: '比较感兴趣',
  },
  {
    value: 10,
    label: '非常感兴趣',
  },
];

export const areaOptions = [
  {
    state: 1,
    id: 3142,
    createdAt: '2024-03-28 20:30:23',
    updatedAt: '2024-03-28 20:30:23',
    deletedAt: '',
    updatedBy: 1,
    category: 11,
    key: '',
    value: 3142,
    sort: 0,
    label: '社会区',
  },
  {
    state: 1,
    id: 3141,
    createdAt: '2024-03-28 20:29:53',
    updatedAt: '2024-03-28 20:30:10',
    deletedAt: '',
    updatedBy: 1,
    category: 11,
    key: '',
    value: 3141,
    sort: 0,
    label: '建构区',
  },
  {
    state: 1,
    id: 3140,
    createdAt: '2024-03-28 20:29:47',
    updatedAt: '2024-03-28 20:29:47',
    deletedAt: '',
    updatedBy: 1,
    category: 11,
    key: '',
    value: 3140,
    sort: 0,
    label: '积木区',
  },
  {
    state: 1,
    id: 3139,
    createdAt: '2024-03-28 20:29:26',
    updatedAt: '2024-03-28 20:29:26',
    deletedAt: '',
    updatedBy: 1,
    category: 11,
    key: '',
    value: 3139,
    sort: 0,
    label: '诱导区',
  },
  {
    state: 1,
    id: 3138,
    createdAt: '2024-03-28 20:29:09',
    updatedAt: '2024-03-28 20:29:09',
    deletedAt: '',
    updatedBy: 1,
    category: 11,
    key: '',
    value: 3138,
    sort: 0,
    label: '棋区',
  },
  {
    state: 1,
    id: 3137,
    createdAt: '2024-03-28 20:29:01',
    updatedAt: '2024-03-28 20:29:01',
    deletedAt: '',
    updatedBy: 1,
    category: 11,
    key: '',
    value: 3137,
    sort: 0,
    label: '生活区',
  },
  {
    state: 1,
    id: 3136,
    createdAt: '2024-03-28 20:28:52',
    updatedAt: '2024-03-28 20:28:52',
    deletedAt: '',
    updatedBy: 1,
    category: 11,
    key: '',
    value: 3136,
    sort: 0,
    label: '音乐区',
  },
  {
    state: 1,
    id: 3135,
    createdAt: '2024-03-28 20:28:46',
    updatedAt: '2024-03-28 20:28:46',
    deletedAt: '',
    updatedBy: 1,
    category: 11,
    key: '',
    value: 3135,
    sort: 0,
    label: '美工区',
  },
  {
    state: 1,
    id: 3134,
    createdAt: '2024-03-28 20:28:38',
    updatedAt: '2024-03-28 20:28:38',
    deletedAt: '',
    updatedBy: 1,
    category: 11,
    key: '',
    value: 3134,
    sort: 0,
    label: '扮演区',
  },
  {
    state: 1,
    id: 3133,
    createdAt: '2024-03-28 20:28:32',
    updatedAt: '2024-03-28 20:28:32',
    deletedAt: '',
    updatedBy: 1,
    category: 11,
    key: '',
    value: 3133,
    sort: 0,
    label: '角色区',
  },
  {
    state: 1,
    id: 3132,
    createdAt: '2024-03-28 20:28:23',
    updatedAt: '2024-03-28 20:28:23',
    deletedAt: '',
    updatedBy: 1,
    category: 11,
    key: '',
    value: 3132,
    sort: 0,
    label: '表演区',
  },
  {
    state: 1,
    id: 3131,
    createdAt: '2024-03-28 20:28:15',
    updatedAt: '2024-03-28 20:28:15',
    deletedAt: '',
    updatedBy: 1,
    category: 11,
    key: '',
    value: 3131,
    sort: 0,
    label: '操作区',
  },
  {
    state: 1,
    id: 3130,
    createdAt: '2024-03-28 20:28:05',
    updatedAt: '2024-03-28 20:28:05',
    deletedAt: '',
    updatedBy: 1,
    category: 11,
    key: '',
    value: 3130,
    sort: 0,
    label: '益智区',
  },
  {
    state: 1,
    id: 3129,
    createdAt: '2024-03-28 20:27:57',
    updatedAt: '2024-03-28 20:27:57',
    deletedAt: '',
    updatedBy: 1,
    category: 11,
    key: '',
    value: 3129,
    sort: 0,
    label: '科学区',
  },
  {
    state: 1,
    id: 3128,
    createdAt: '2024-03-28 20:27:46',
    updatedAt: '2024-03-28 20:27:46',
    deletedAt: '',
    updatedBy: 1,
    category: 11,
    key: '',
    value: 3128,
    sort: 0,
    label: '数学区',
  },
  {
    state: 1,
    id: 3127,
    createdAt: '2024-03-28 20:27:38',
    updatedAt: '2024-03-28 20:27:38',
    deletedAt: '',
    updatedBy: 1,
    category: 11,
    key: '',
    value: 3127,
    sort: 0,
    label: '书写区',
  },
  {
    state: 1,
    id: 3126,
    createdAt: '2024-03-28 20:27:25',
    updatedAt: '2024-03-28 20:27:25',
    deletedAt: '',
    updatedBy: 1,
    category: 11,
    key: '',
    value: 3126,
    sort: 0,
    label: '阅读区',
  },
  {
    state: 1,
    id: 3125,
    createdAt: '2024-03-28 20:27:17',
    updatedAt: '2024-03-28 20:27:17',
    deletedAt: '',
    updatedBy: 1,
    category: 11,
    key: '',
    value: 3125,
    sort: 0,
    label: '语言区',
  },
];

export const targets = [
  {
    id: '2453',
    title: '进行100以内的逐个唱数或以10为单位进行唱数',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1246',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 15:12:30.787468',
    updated_at: '2/4/2024 15:12:30.787468',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2454',
    title: '从已知序列中的给定数字开始向前数（而不是从 1 开始）',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1246',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:08:05.516217',
    updated_at: '2/4/2024 16:08:05.516217',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2455',
    title: '用点数的方式对20以内数量的物体进行准确计数',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1247',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '1',
    product_titles: '["串珠数数"]',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:08:33.849794',
    updated_at: '19/11/2024 02:02:10',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2456',
    title: '学习运用接数、按群计数、目测数群等多种计数方法计数',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1247',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:08:47.466386',
    updated_at: '2/4/2024 16:08:47.466386',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2457',
    title: '区分基数和序数',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1254',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:09:39.229025',
    updated_at: '2/4/2024 16:09:39.229025',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2458',
    title: '借助百数表初步感知100以内数的系统，初步理解系统的排列规律',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1248',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:10:03.088928',
    updated_at: '2/4/2024 16:10:03.088928',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2459',
    title: '认出20以内的数字',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1248',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '1',
    product_titles: '["串珠数数"]',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:10:39.360565',
    updated_at: '19/11/2024 02:02:10',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2460',
    title: '将20以内的数字和相应数量的物体或集合匹配',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1248',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '1',
    product_titles: '["串珠数数"]',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:10:58.999199',
    updated_at: '19/11/2024 02:02:10',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2461',
    title: '书写从0到20的数字',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1248',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:11:17.374981',
    updated_at: '2/4/2024 16:12:01',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2462',
    title: '辨别一组中物体的数量是大于、小于还是等于另一组中物体的数量（如，使用匹配和计数策略）',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1249',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:14:14.43394',
    updated_at: '2/4/2024 16:14:14.43394',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2463',
    title: '比较以书面数字形式呈现的1到10之间的2个或3个数字',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1249',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '1',
    product_titles: '["算数学习盒"]',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:14:40.385972',
    updated_at: '19/11/2024 02:02:10',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2464',
    title: '理解10以内数与数之间的数差关系的可逆性、传递性',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1249',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:14:58.791418',
    updated_at: '17/9/2024 20:49:46',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2465',
    title: '按大小、长短、高矮、粗细、厚薄、宽窄差异对10以内物体进行“正向排序”和“逆向排序”',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1249',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:15:55.600249',
    updated_at: '17/9/2024 20:50:02',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2466',
    title: '理解估数的意义，对物体数量有初步的数感',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1255',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:16:13.037317',
    updated_at: '2/4/2024 16:16:13.037317',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2467',
    title: '根据已知线索，推断未知物群的数量',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1255',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:16:24.691364',
    updated_at: '2/4/2024 16:16:24.691364',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2468',
    title: '在不数数的情况下，估算6以内物体的数量',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1255',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:16:37.567441',
    updated_at: '2/4/2024 16:16:37.567441',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2469',
    title:
      '找出从1到9的任何数字与给定数字相加后组成10的数字（例如，使用实物或图画，并用图画或等式记录答案）',
    matrix1_id: '1244',
    matrix2_id: '1250',
    matrix3_id: '1253',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '1',
    product_titles: '["磁力数蛋游戏"]',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:26:06.210766',
    updated_at: '19/11/2024 02:02:10',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2470',
    title:
      '学习10的分解，用不止一种方法分解10以内的数字并用图画或等式记录每次分解（如5=2+3和5=4+1），了解数分解的递增递减规律及部分数互换位置总数不变',
    matrix1_id: '1244',
    matrix2_id: '1250',
    matrix3_id: '1253',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '1',
    product_titles: '["磁力数蛋游戏"]',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:26:39.600058',
    updated_at: '19/11/2024 02:02:10',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2471',
    title: '能对一定数量的物体进行等分，如二等分和四等分',
    matrix1_id: '1244',
    matrix2_id: '1250',
    matrix3_id: '1253',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:26:53.402865',
    updated_at: '2/4/2024 16:26:53.402865',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2472',
    title: '认识+、-、=和加减算式，初步理解算式表示的意义',
    matrix1_id: '1244',
    matrix2_id: '1250',
    matrix3_id: '1257',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '1',
    product_titles: '["算数学习盒"]',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:30:17.380234',
    updated_at: '19/11/2024 02:02:10',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2473',
    title:
      '将11到19的数组成和分解为10和其他一些1（例如，使用实物或图画，并用图画或等式记录每次组成或分解，如18=10+8）；理解这些数由10和1、2、3、4、5、6、7、8或9个1组成',
    matrix1_id: '1244',
    matrix2_id: '1250',
    matrix3_id: '1257',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:31:20.835696',
    updated_at: '2/4/2024 16:31:20.835696',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2474',
    title:
      '借助手指、动作、心理图像、声音（如拍手）、实物、图画、口头解释或算式进行10以内的加减运算，理解加减运算，理解加减的实际意义',
    matrix1_id: '1244',
    matrix2_id: '1250',
    matrix3_id: '1257',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:31:34.642271',
    updated_at: '2/4/2024 16:31:34.642271',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2475',
    title: '解决加减法文字问题，以及10以内的加减法问题（如，用实物或图画表示问题）',
    matrix1_id: '1244',
    matrix2_id: '1250',
    matrix3_id: '1257',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '1',
    product_titles: '["算数学习盒"]',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:32:00.398981',
    updated_at: '19/11/2024 02:02:10',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2476',
    title: '流利地进行5以内加减法运算',
    matrix1_id: '1244',
    matrix2_id: '1250',
    matrix3_id: '1257',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:32:10.636404',
    updated_at: '2/4/2024 16:32:10.636404',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2477',
    title:
      '识别和描述形状（正方形、圆形、三角形、长方形、六边形、长方体、圆锥体、圆柱体和球体），认识长方体、正方体的面',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1258',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:32:44.734299',
    updated_at: '2/4/2024 16:32:44.734299',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2478',
    title: '无论形状的方向或整体大小如何，都能说出它们的名称',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1258',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:32:57.409424',
    updated_at: '2/4/2024 16:32:57.409424',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2479',
    title: '识别形状是二维的还是三维的',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1258',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:33:08.312785',
    updated_at: '2/4/2024 16:33:08.312785',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2480',
    title: '理解图形的对称性并学习等分图形',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1258',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:33:21.25044',
    updated_at: '2/4/2024 16:33:21.25044',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2481',
    title: '用图形及图形组合进行较为复杂的组合与分解，理解其中的组合替代关系',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1259',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:33:46.034209',
    updated_at: '2/4/2024 16:33:46.034209',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2482',
    title: '分析和比较不同大小和方向的二维和三维图形，用非正式语言描述它们的异同、部分和其他属性',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1259',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '0',
    sort: '0',
    created_at: '2/4/2024 16:33:59.63291',
    updated_at: '17/9/2024 20:51:04',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2483',
    title:
      '分析和比较不同大小和方向的二维和三维图形，用非正式语言描述它们的异同、部分（如边和顶点/"角 "的数量）和其他属性（如边的长度相等）',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1259',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:34:12.998856',
    updated_at: '2/4/2024 16:34:12.998856',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2484',
    title: '用各种部件（如木棍和泥球）拼出各种形状，并画出各种形状，以此来模拟世界中的形状',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1259',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:34:24.345321',
    updated_at: '2/4/2024 16:34:24.345321',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2485',
    title: '使用适当的术语描述物体的相对位置（如，上面、下面、旁边、前面、后面、旁边）',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1260',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:34:52.739672',
    updated_at: '2/4/2024 16:34:52.739672',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2486',
    title: '学习用符号表示物体在二维空间中的位置和运动方向',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1260',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:35:05.216213',
    updated_at: '2/4/2024 16:35:05.216213',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2487',
    title: '进行图形拼搭时，有意识地预期旋转和翻转的结果',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1260',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:35:37.395629',
    updated_at: '17/9/2024 20:51:28',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2488',
    title: '理解简单示意图中的空间关系',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1260',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:35:47.757366',
    updated_at: '17/9/2024 20:52:57',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2489',
    title: '理解并重现观察三维物体的不同视角',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1260',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:35:57.329038',
    updated_at: '17/9/2024 20:53:06',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2490',
    title: '重复使用一个单位量进行长度的自然测量',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1263',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:36:10.836412',
    updated_at: '2/4/2024 16:36:10.836412',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2491',
    title: '理解测量同一长度时，单位长度的长短和所需单位数量之间的相反关系',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1263',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:36:22.971605',
    updated_at: '2/4/2024 16:36:22.971605',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2492',
    title: '描述物体的可测量属性，如长度、重量、面积或容量',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1264',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:36:36.997864',
    updated_at: '2/4/2024 16:36:36.997864',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2493',
    title: '描述单个物体的几个可测量属性',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1264',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:36:49.34131',
    updated_at: '2/4/2024 16:36:49.34131',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2494',
    title:
      '比较两个具有共同可测量属性的物体，看哪个物体的属性 "多"/"少"，并描述其差异，例如，直接比较两个孩子的身高，描述其中一个孩子更高/更矮',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1264',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:37:21.21852',
    updated_at: '2/4/2024 16:37:21.21852',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2495',
    title: '在解决数学问题时考虑可用的工具（包括估算）',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1264',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:37:39.600813',
    updated_at: '2/4/2024 16:37:39.600813',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2496',
    title: '通过用单位体积（立方块）填充的方式，体验体积和体积测量的意义',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1263',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:37:51.796939',
    updated_at: '17/9/2024 20:53:25',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2497',
    title: '学习对物体进行多重角度分类、层级分类以及同时按物体的两种以上特征进行分类',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1266',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:38:01.660352',
    updated_at: '2/4/2024 16:38:01.660352',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2498',
    title: '使用一种属性将最多20个对象分类，显示每个类别中的对象数量，计算并比较每个类别的数量',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1266',
    grade_id: '2822',
    grade_title: '大班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:38:11.449723',
    updated_at: '2/4/2024 16:38:11.449723',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2499',
    title: '进行50以内的唱数',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1246',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 16:52:22.655478',
    updated_at: '2/4/2024 16:52:22.655478',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2500',
    title: '说明数出的物体总数，理解最后一个数字表示数出的物体',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1246',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '1',
    product_titles: '["小小邮递员"]',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:09:49.605648',
    updated_at: '19/11/2024 02:02:10',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2501',
    title: '感知10以内数量，发现物体的数量不会因其排列方式的改变而变化',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1246',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:10:18.393616',
    updated_at: '2/4/2024 17:10:18.393616',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2502',
    title: '用点数的方式对10以内数量的物体进行准确计数',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1247',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '2',
    product_titles: '["小小邮递员", "磁力数蛋游戏"]',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:10:36.582749',
    updated_at: '19/11/2024 02:02:10',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2503',
    title: '提供特定数字时，进行计数',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1247',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '1',
    product_titles: '["小小邮递员"]',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:10:47.619936',
    updated_at: '19/11/2024 02:02:10',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2504',
    title: '认识10以内的数字，并了解数字的抽象意义',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1248',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:11:26.612125',
    updated_at: '2/4/2024 17:11:26.612125',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2505',
    title: '将10以内的数字与相应数量的物体或集合匹配',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1248',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '2',
    product_titles: '["小小邮递员", "磁力数蛋游戏"]',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:12:37.447427',
    updated_at: '19/11/2024 02:02:10',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2506',
    title: '用书写的数字表示10以内物体的数量',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1248',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '1',
    product_titles: '["磁力数蛋游戏"]',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:12:47.794353',
    updated_at: '19/11/2024 02:02:10',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2507',
    title: '用计数的方法比较10以内数量的多少',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1249',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:13:06.337246',
    updated_at: '2/4/2024 17:13:06.337246',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2508',
    title: '比较 5以内的两个书面数字',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1249',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:13:19.080929',
    updated_at: '2/4/2024 17:13:19.080929',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2509',
    title: '在数量比较的基础上将数量为7以内的集合按多少排序',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1249',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:13:36.086351',
    updated_at: '17/9/2024 20:55:08',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2510',
    title: '认识10以内数序，感知10以内相邻数的等差关系',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1249',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:13:49.467192',
    updated_at: '17/9/2024 20:55:18',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2511',
    title: '使用序数词描述物体的位置（第一、第二、最后）',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1254',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:14:43.760134',
    updated_at: '2/4/2024 17:14:43.760134',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2512',
    title: '学习10以内的序数，能从不同的方面正确指出某一物体在序列中的位置',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1254',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:14:55.790734',
    updated_at: '2/4/2024 17:14:55.790734',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2513',
    title: '按大小、长短、高矮、粗细、厚薄、宽窄差异对7个以内物体进行排序',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1249',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:15:33.175765',
    updated_at: '17/9/2024 20:55:32',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2514',
    title:
      '找出从1到4的任何数字与给定数字相加后组成5的数字（例如，使用实物或图画，并用图画或等式记录答案）',
    matrix1_id: '1244',
    matrix2_id: '1250',
    matrix3_id: '1253',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:16:04.274507',
    updated_at: '2/4/2024 17:16:04.274507',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2515',
    title:
      '进行5以内数量的分解与组合，体验一个量可以分成两个部分量，两个部分量合起来就是原来的总量',
    matrix1_id: '1244',
    matrix2_id: '1250',
    matrix3_id: '1253',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:16:16.336837',
    updated_at: '2/4/2024 17:16:16.336837',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2516',
    title: '使用基本数学词汇，解释6以内一组物体的加法和减法（如，拼、加、减、拆、取）',
    matrix1_id: '1244',
    matrix2_id: '1250',
    matrix3_id: '1257',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:16:50.233611',
    updated_at: '2/4/2024 17:16:50.233611',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2517',
    title: '借助实物或情境理解10以内集合的数量变化，理解加法含义，学习10以内的加法运算',
    matrix1_id: '1244',
    matrix2_id: '1250',
    matrix3_id: '1257',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '1',
    product_titles: '["磁力数蛋游戏"]',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:17:00.697347',
    updated_at: '19/11/2024 02:02:10',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2518',
    title: '感知和发现常见几何图形的基本特征，并进行分类',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1258',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:23:19.915622',
    updated_at: '2/4/2024 17:23:19.915622',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2519',
    title: '认识并命名立体图形上的平面图形，如三角形、长方形、正方形、梯形、圆形、椭圆形等',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1258',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:23:41.428253',
    updated_at: '2/4/2024 17:23:41.428253',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2520',
    title: '使用图形名称描述环境中的物体',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1258',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:23:59.606443',
    updated_at: '17/9/2024 20:56:53',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2521',
    title: '分析和比较不同大小和方向的二维和三维图形，使用非正式语言描述它们的异同、部分和其他属性',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1258',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:24:54.455222',
    updated_at: '2/4/2024 17:24:54.455222',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2522',
    title: '认识平面图形（如三角形）的各种变式',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1258',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:26:38.744534',
    updated_at: '2/4/2024 17:26:38.744534',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2523',
    title: '用简单的图形拼成较大的图形',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1259',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:27:05.684001',
    updated_at: '2/4/2024 17:27:05.684001',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2524',
    title: '不用借助分割线的提示，进行简单的图形组合与分解',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1259',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:27:20.259363',
    updated_at: '2/4/2024 17:27:20.259363',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2525',
    title: '使用拼图板和橡皮筋制作图形',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1259',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:27:35.939128',
    updated_at: '2/4/2024 17:27:35.939128',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2526',
    title: '用上下、前后、里外、中间、旁边等方位词描述物体的位置和运动方向',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1260',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:28:08.615076',
    updated_at: '2/4/2024 17:28:08.615076',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2527',
    title: '探索图形，常见物品中简单的镜像对称关系',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1260',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:28:27.792311',
    updated_at: '17/9/2024 20:57:58',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2528',
    title: '有意识地运用平移，旋转和翻转进行图形拼搭',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1260',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:28:41.073621',
    updated_at: '17/9/2024 20:58:14',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2529',
    title: '通过用部件构建形状和绘制形状来模拟世界中的形状',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1260',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:28:52.481469',
    updated_at: '17/9/2024 20:58:24',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2530',
    title: '用首尾相接摆放单位量的方式，进行长度的自然测量',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1263',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:29:10.053439',
    updated_at: '2/4/2024 17:29:10.053439',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2531',
    title: '使用非标准物品测量物体的长度（如，手、毛线）',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1263',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:29:30.090496',
    updated_at: '2/4/2024 17:29:30.090496',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2532',
    title: '练习使用标准测量工具',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1264',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:29:59.124968',
    updated_at: '2/4/2024 17:29:59.124968',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2533',
    title: '练习使用测量词汇',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1264',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:30:15.545536',
    updated_at: '2/4/2024 17:30:15.545536',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2534',
    title: '比较两个具有共同可测量属性的物体，看哪个物体的属性 "多"/"少"，并描述其差异',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1264',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:30:28.789178',
    updated_at: '2/4/2024 17:30:28.789178',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2535',
    title: '通过用非标准物体（如，鞋、方块）覆盖的方式，体验面积和面积测量的意义',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1263',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:30:44.657178',
    updated_at: '17/9/2024 20:58:40',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2536',
    title: '按物体的内部特征（性质、功能、用途等）进行分类',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1266',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:31:07.551158',
    updated_at: '2/4/2024 17:31:07.551158',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2537',
    title: '使用一种属性将最多 10 个对象分类',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1266',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:31:25.66795',
    updated_at: '2/4/2024 17:31:25.66795',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2538',
    title: '显示每个类别中的物体数量',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1266',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:31:37.022499',
    updated_at: '2/4/2024 17:31:37.022499',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2539',
    title: '计算并比较每个类别的数量，以描述哪个类别的属性 "多"/"少"',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1266',
    grade_id: '2821',
    grade_title: '中班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 17:31:49.704775',
    updated_at: '2/4/2024 17:31:49.704775',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2540',
    title: '用一一对应的方法做等量集合',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1246',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:27:18.467322',
    updated_at: '2/4/2024 22:27:18.467322',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2541',
    title: '进行20以内的常唱数',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1246',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:27:29.903373',
    updated_at: '2/4/2024 22:27:29.903373',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2542',
    title: '根据数量属性将数量为5以内的集合分类',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1246',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:27:50.550443',
    updated_at: '2/4/2024 22:27:50.550443',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2543',
    title:
      '在动作的基础上，理解“1”和“许多”之间的关系，即1个、1个××合起来是“许多”，“许多”可以分成1个、1个××',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1247',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:28:04.112754',
    updated_at: '2/4/2024 22:28:04.112754',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2544',
    title: '学习手口一致点数5以内的物体，并说出总数',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1247',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '1',
    product_titles: '["计数图解游戏"]',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:28:15.950705',
    updated_at: '19/11/2024 02:02:10',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2545',
    title: '通过直接感知说出3以内物体的数量',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1247',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:28:25.139259',
    updated_at: '2/4/2024 22:28:25.139259',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2546',
    title: '感知5以内数量，学习给5以内的点子卡片匹配等量的实物',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1247',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '1',
    product_titles: '["计数图解游戏"]',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:28:38.371841',
    updated_at: '19/11/2024 02:02:10',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2547',
    title: '按实物范例的数目或指定数目取出相应5以内数量的物体',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1247',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:28:48.482173',
    updated_at: '2/4/2024 22:28:48.482173',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2548',
    title: '用点子等非正式方法表示5以内的数量',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1248',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '1',
    product_titles: '["计数图解游戏"]',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:29:00.08086',
    updated_at: '19/11/2024 02:02:10',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2549',
    title: '用一一对应的方法比较5以内的数量的多少',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1249',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:29:14.035987',
    updated_at: '2/4/2024 22:29:14.035987',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2550',
    title: '在感知的基础上将数量为5以内的集合按多少排序',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1249',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:29:24.984448',
    updated_at: '17/9/2024 20:59:20',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2551',
    title: '按大小、长短等差异对5以内物体进行排序',
    matrix1_id: '1244',
    matrix2_id: '1245',
    matrix3_id: '1249',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:29:40.374218',
    updated_at: '17/9/2024 20:59:29',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2552',
    title: '探索物体较明显的形状特征，并用自己的语言描述',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1258',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:29:54.298069',
    updated_at: '2/4/2024 22:29:54.298069',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2553',
    title: '借助分割线的提示进行简单的图形组合',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1258',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:30:07.364118',
    updated_at: '2/4/2024 22:30:07.364118',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2554',
    title: '用上下、前后、里外等方位词描述物体的位置',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1260',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:30:18.138609',
    updated_at: '2/4/2024 22:30:18.138609',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2555',
    title: '尝试运用平移、旋转进行图形拼搭',
    matrix1_id: '1244',
    matrix2_id: '1252',
    matrix3_id: '1260',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:30:29.124079',
    updated_at: '17/9/2024 20:59:40',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2556',
    title: '根据标记将相同的物体集中在一起，进行简单的归类',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1266',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:30:40.940316',
    updated_at: '2/4/2024 22:30:40.940316',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2557',
    title: '按物体的一种外部特征（颜色、形状、大小、高矮、长短等）进行简单的分类',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1266',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:30:53.221554',
    updated_at: '2/4/2024 22:30:53.221554',
    updated_by: '1',
    deleted_at: '',
  },
  {
    id: '2558',
    title: '根据物体的特点、关系寻找相关物体，将相关的物体相匹配',
    matrix1_id: '1244',
    matrix2_id: '1262',
    matrix3_id: '1266',
    grade_id: '2820',
    grade_title: '小班',
    product_quantity: '0',
    product_titles: '',
    state: '1',
    sort: '0',
    created_at: '2/4/2024 22:31:04.517324',
    updated_at: '2/4/2024 22:31:04.517324',
    updated_by: '1',
    deleted_at: '',
  },
];
