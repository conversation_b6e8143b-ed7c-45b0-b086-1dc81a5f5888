import { fetchMatrix } from '@/services/apis';
import { ProForm, ProFormList } from '@ant-design/pro-components';

import { useEffect, useState } from 'react';
import { MatrixeOptionProps, MatrixSelectItem } from './MatrixSelectItem';

function filterMatrix(matrix: any) {
  return matrix.map((m: any) => {
    if (m.children) {
      return {
        label: m.title,
        value: m.id,
        next: filterMatrix(m.children),
      };
    }
    return {
      label: m.title,
      value: m.id,
    };
  });
}
const MatrixSelect = (props: {
  activeRow?: API.ClassAreaProductProps;
  form: any;
  disabled?: boolean;
  isTemp?: boolean;
  matrixeOptions?: MatrixeOptionProps[];
}) => {
  const form = props.form;

  const [matrixeOptions, setMatrixeOptions] = useState<MatrixeOptionProps[]>(
    props.matrixeOptions || [],
  );

  useEffect(() => {
    if (props.activeRow && props.activeRow.extra) {
      form.setFieldsValue(props.activeRow.extra);
    }
  }, [props.activeRow]);
  useEffect(() => {
    if (!props.matrixeOptions) {
      fetchMatrix().then((res) => {
        setMatrixeOptions(filterMatrix(res.data));
      });
    }
  }, []);
  return (
    <ProForm
      form={form}
      layout="horizontal"
      submitter={false}
      initialValues={{
        matrixes: [],
      }}
    >
      <ProFormList
        name="matrixes"
        creatorButtonProps={
          !props.disabled
            ? {
                position: 'bottom', // 新增按钮位置
                creatorButtonText: '新增数据',
              }
            : false
        }
        itemRender={({ listDom }) => <>{listDom}</>}
      >
        {(meta, index, action) => (
          <MatrixSelectItem
            index={index}
            activeRow={props.activeRow}
            action={action}
            matrixeOptions={matrixeOptions.filter((f) =>
              props.isTemp ? f.label === '数学' : true,
            )}
            disabled={props.disabled}
            form={form}
          />
        )}
      </ProFormList>
    </ProForm>
  );
};

export default MatrixSelect;
