import type { ActionType, ProColumns } from '@ant-design/pro-components';
import {
  PageContainer,
  ProTable,
  ModalForm,
  ProFormText,
  ProFormSelect,
  ProFormDigit,
} from '@ant-design/pro-components';
import { Button, message, Form, Modal, Tooltip, Image } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import React, { useRef, useState, useEffect } from 'react';
import { useModel, useParams, useLocation } from '@umijs/max';
import {
  queryMoodDictionaryPageList,
  saveOrUpdateMoodDictionary,
  deleteMoodDictionary,
} from '@/services/childMood';
import UploadImageComponent from '@/components/UploadImage';
import { fetchClient } from '@/services/fileUpload';

const ChildMoodManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [form] = Form.useForm();
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<any>({});
  const [client, setClient] = useState<any>(null);
  const [imageList, setImageList] = useState<any[]>([]);
  const [defaultMoodList, setDefaultMoodList] = useState<any[]>([]);
  const [selectedDefaultMood, setSelectedDefaultMood] = useState<any>(null);

  // URL参数和查询参数处理
  const params = useParams<{ classId?: string }>();
  const location = useLocation();
  const query = new URLSearchParams(location.search);
  const classIdFromQuery = query.get('classId');

  // 获取全局状态中的当前班级ID
  const { initialState } = useModel('@@initialState');
  const currentClassIdFromState = initialState?.currentUser?.currentClassId || initialState?.currentClass?.[0];

  // 当前班级ID - 优先使用URL参数，其次使用全局状态
  const [classId] = useState<number | undefined>(
    Number(params.classId || classIdFromQuery) || currentClassIdFromState || undefined,
  );

  // 调试班级ID获取
  useEffect(() => {
    console.log('班级ID调试信息:', {
      'URL params.classId': params.classId,
      'Query classIdFromQuery': classIdFromQuery,
      'State currentClassIdFromState': currentClassIdFromState,
      '最终使用的classId': classId,
      '当前用户信息': initialState?.currentUser
    });
  }, [params.classId, classIdFromQuery, currentClassIdFromState, classId, initialState]);

  // 加载默认心情列表（使用现有的心情列表接口）
  const loadDefaultMoodList = async () => {
    try {
      const response = await queryMoodDictionaryPageList({
        pageNum: 1,
        pageSize: 1000, // 获取所有心情
      });
      if (response?.status === 0) {
        // 过滤掉自定义心情（category为5），只保留基础心情
        const basicMoodList = (response.data.records || []).filter((mood: any) => mood.category !== 5);
        setDefaultMoodList(basicMoodList);
      }
    } catch (error) {
      console.error('获取默认心情列表失败:', error);
    }
  };

  // 初始化上传客户端和默认心情列表
  useEffect(() => {
    fetchClient(setClient);
    loadDefaultMoodList();
  }, []);

  // 处理新增
  const handleAdd = () => {
    setCurrentRow({});
    setImageList([]);
    setSelectedDefaultMood(null);
    form.resetFields();
    setModalOpen(true);
  };

  // 处理编辑
  const handleEdit = (record: any) => {
    setCurrentRow(record);
    setSelectedDefaultMood(null); // 编辑时不需要选择默认心情
    // 如果有图片ID，构造图片列表数据
    if (record.imageId) {
      // 构造图片数据，兼容 imageUrl 字段
      const imageData = {
        id: record.imageId,
        uri: record.imageUrl || '', // 优先使用 imageUrl
        filename: `mood_image_${record.imageId}`,
        uid: record.imageId,
        hash: record.imageId,
      };
      setImageList([imageData]);
    } else {
      setImageList([]);
    }
    form.setFieldsValue(record);
    setModalOpen(true);
  };

  // 处理保存
  const handleSave = async (values: any) => {
    try {
      // 验证心情图标是否已上传
      if (imageList.length === 0) {
        message.error('请上传心情图标');
        return false;
      }

      const params = {
        ...values,
        imageId: imageList[0].id,
        // 自动传递学校和班级ID
        classId: classId,
        schoolId: initialState?.currentUser?.currentSchoolId,
        category: 5, // 新增时固定传5
        // 如果是基于默认心情创建的，传递关联的基础心情ID
        linkId: selectedDefaultMood?.id || null,
      };

      if (currentRow.id) {
        params.id = currentRow.id;
      }

      const response = await saveOrUpdateMoodDictionary(params);

      if (response?.status === 0) {
        message.success(currentRow.id ? '修改成功' : '新增成功');
        setModalOpen(false);
        actionRef.current?.reload();
        return true;
      } else {
        message.error(response?.message || '操作失败');
        return false;
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('操作失败');
      return false;
    }
  };

  // 处理删除
  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除心情"${record.moodName}"吗？此操作不可恢复。`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          const response = await deleteMoodDictionary(record.id);
          console.log('删除响应:', response);

          if (response?.status === 0) {
            message.success('删除成功');
            actionRef.current?.reload();
          } else {
            // 处理不同的错误响应格式
            let errorMessage = '删除失败';

            if (response?.status === 500 && response?.error?.stack) {
              // 处理后端业务错误
              errorMessage = response.error.stack;
            } else if (response?.message && response?.message !== 'UNHANDLED_EXCEPTION') {
              // 处理其他类型的错误消息
              errorMessage = response.message;
            } else if (response?.error?.message) {
              // 处理嵌套的错误消息
              errorMessage = response.error.message;
            }

            message.error(errorMessage);
          }
        } catch (error: any) {
          console.error('删除失败:', error);

          // 处理网络错误或其他异常
          let errorMessage = '删除失败';

          if (error?.response?.data?.error?.stack) {
            errorMessage = error.response.data.error.stack;
          } else if (error?.response?.data?.message && error.response.data.message !== 'UNHANDLED_EXCEPTION') {
            errorMessage = error.response.data.message;
          } else if (error?.message) {
            errorMessage = error.message;
          }

          message.error(errorMessage);
        }
      },
    });
  };

  // 处理默认心情选择
  const handleDefaultMoodChange = (value: any, option: any) => {
    if (value && option) {
      const selectedMood = defaultMoodList.find((mood) => mood.id === value);
      if (selectedMood) {
        setSelectedDefaultMood(selectedMood);
        // 自动填充表单数据
        form.setFieldsValue({
          moodName: selectedMood.moodName,
          state: selectedMood.state,
          sort: selectedMood.sort,
        });

        // 如果有默认图片，设置到图片列表中
        if (selectedMood.imageId && selectedMood.imageUrl) {
          const imageData = {
            id: selectedMood.imageId,
            uri: selectedMood.imageUrl,
            filename: `default_mood_${selectedMood.imageId}`,
            uid: selectedMood.imageId,
            hash: selectedMood.imageId,
          };
          setImageList([imageData]);
        }
      }
    } else {
      setSelectedDefaultMood(null);
      setImageList([]);
    }
  };

  // 定义表格列
  const columns: ProColumns<any>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '心情名称',
      dataIndex: 'moodName',
      valueType: 'text',
      width: 200,
      render: (result) => {
        return (
          <Tooltip title={result}>
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: '180px',
              }}
            >
              {result}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '心情图片',
      dataIndex: 'imageUrl',
      valueType: 'text',
      width: 100,
      search: false,
      render: (_, record) => {
        // 优先显示 imageUrl，如果没有则显示 imageId
        if (record.imageUrl) {
          return (
            <Image
              src={record.imageUrl}
              alt="心情图片"
              width={60}
              height={60}
              style={{ objectFit: 'cover', borderRadius: 4 }}
              fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
            />
          );
        } else if (record.imageId) {
          return <span style={{ color: '#666', fontSize: '12px' }}>ID: {record.imageId}</span>;
        }
        return '-';
      },
    },
    {
      title: '学校名称',
      dataIndex: 'schoolName',
      valueType: 'text',
      width: 150,
      search: false,
      render: (result) => {
        return result || '-';
      },
    },
    {
      title: '班级名称',
      dataIndex: 'className',
      valueType: 'text',
      width: 120,
      search: false,
      render: (result) => {
        return result || '-';
      },
    },
    {
      title: '排序',
      dataIndex: 'sort',
      valueType: 'text',
      width: 80,
      search: false,
      render: (result) => {
        return result || '-';
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      valueType: 'text',
      width: 100,
      search: false,
      render: (result) => {
        const stateMap: Record<number, { text: string; color: string }> = {
          0: { text: '禁用', color: '#ff4d4f' },
          1: { text: '启用', color: '#52c41a' },
        };
        const state = stateMap[result as number];
        return state ? <span style={{ color: state.color }}>{state.text}</span> : result;
      },
      valueEnum: {
        0: { text: '禁用', status: 'Error' },
        1: { text: '启用', status: 'Success' },
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'text',
      width: 150,
      search: false,
      render: (result) => {
        return result || '-';
      },
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      valueType: 'text',
      width: 150,
      search: false,
      render: (result) => {
        return result || '-';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      valueType: 'option',
      width: 160,
      fixed: 'right',
      render: (_, record) => [
        <Button key="edit" type="link" size="small" onClick={() => handleEdit(record)}>
          编辑
        </Button>,
        <Button key="delete" type="link" size="small" danger onClick={() => handleDelete(record)}>
          删除
        </Button>,
      ],
    },
  ];

  // 如果没有classId，显示提示信息
  if (!classId) {
    return (
      <PageContainer>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <p>请先选择班级</p>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <ProTable<any>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        toolBarRender={() => [
          <Button type="primary" key="primary" onClick={handleAdd} icon={<PlusOutlined />}>
            新建心情
          </Button>,
        ]}
        request={async (params) => {
          try {
            console.log('请求参数:', params);
            const response = await queryMoodDictionaryPageList({
              pageNum: params.current,
              pageSize: 100, // 增加页面大小以获取所有数据
            });
            console.log('接口返回:', response);

            if (response?.status === 0) {
              // 获取当前学校和班级ID
              const currentSchoolId = initialState?.currentUser?.currentSchoolId || initialState?.currentSchool?.[0];
              const currentClassId = classId;

              console.log('筛选条件:', {
                currentSchoolId,
                currentClassId,
                category: 5,
                'URL参数classId': params.classId,
                'Query参数classId': classIdFromQuery,
                '全局状态classId': currentClassIdFromState
              });

              // 过滤出category为5且匹配当前学校和班级的数据
              const filteredData = response.data.records.filter((item: any) => {
                const matchCategory = item.category === 5;
                const matchSchool = !currentSchoolId || item.schoolId === currentSchoolId;
                const matchClass = !currentClassId || item.classId === currentClassId;

                console.log(`心情 ${item.moodName} (ID: ${item.id}):`, {
                  category: item.category,
                  schoolId: item.schoolId,
                  classId: item.classId,
                  schoolName: item.schoolName,
                  className: item.className,
                  matchCategory,
                  matchSchool: `${matchSchool} (${item.schoolId} === ${currentSchoolId})`,
                  matchClass: `${matchClass} (${item.classId} === ${currentClassId})`,
                  finalMatch: matchCategory && matchSchool && matchClass
                });

                return matchCategory && matchSchool && matchClass;
              });

              console.log('筛选结果:', {
                原始数据数量: response.data.records.length,
                category5数量: response.data.records.filter((item: any) => item.category === 5).length,
                筛选后数量: filteredData.length,
                筛选后的心情: filteredData.map((item: any) => `${item.moodName} (${item.className})`)
              });

              return {
                data: filteredData,
                success: true,
                total: filteredData.length,
              };
            } else {
              message.error(response?.message || '获取数据失败');
              return {
                data: [],
                success: false,
                total: 0,
              };
            }
          } catch (error) {
            console.error('获取心情字典列表失败:', error);
            message.error('获取数据失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        columns={columns}
        pagination={{
          defaultPageSize: 20,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        scroll={{ x: 1000 }}
      />

      <ModalForm
        title={currentRow.id ? '编辑心情' : '新增心情'}
        width="600px"
        form={form}
        open={modalOpen}
        modalProps={{
          maskClosable: false,
          destroyOnClose: true,
        }}
        onOpenChange={(open) => {
          setModalOpen(open);
          if (!open) {
            setCurrentRow({});
            setImageList([]);
            setSelectedDefaultMood(null);
            form.resetFields();
          }
        }}
        onFinish={handleSave}
      >
        {/* 只在新增时显示默认心情选择 */}
        {!currentRow.id && (
          <ProFormSelect
            name="defaultMoodId"
            label="选择默认心情"
            placeholder="请选择一个默认心情作为模板"
            options={defaultMoodList.map((mood) => ({
              label: mood.moodName,
              value: mood.id,
            }))}
            onChange={handleDefaultMoodChange}
            allowClear
            fieldProps={{
              showSearch: true,
              filterOption: (input: string, option: any) =>
                option?.label?.toLowerCase().includes(input.toLowerCase()),
            }}
          />
        )}

        <ProFormText
          name="moodName"
          label="心情名称"
          rules={[{ required: true, message: '请输入心情名称' }]}
          placeholder="请输入心情名称"
        />

        <ProFormSelect
          name="state"
          label="状态"
          rules={[{ required: true, message: '请选择状态' }]}
          options={[
            { label: '禁用', value: 0 },
            { label: '启用', value: 1 },
          ]}
          placeholder="请选择状态"
          initialValue={1}
        />

        <ProFormDigit name="sort" label="排序" placeholder="请输入排序值" min={0} />

        <UploadImageComponent
          key="upload"
          fileName="imageId"
          label="心情图标"
          max={1}
          client={client}
          fileList={imageList}
          setClient={setClient}
          setFileList={setImageList}
          accept=".png,.jpg,.jpeg,.gif"
        />
      </ModalForm>
    </PageContainer>
  );
};

export default ChildMoodManagement;
