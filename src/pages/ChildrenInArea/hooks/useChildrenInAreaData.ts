import { useState, useEffect, useCallback, useMemo } from 'react';
import { message } from 'antd';
import { listArea, listChild, listAreaHistory, listChildHistory } from '@/services/childrenInArea';

// 接口类型定义
export interface ChildData {
  childId: number;
  areaId?: number | null;
  title: string;
  area: string;
  areaAlias: string;
  childCount?: number;
  childAvatar: string;
  limitNum?: number;
  sex: number; // API返回的性别: 1为男，2为女
  gender?: 'boy' | 'girl'; // 前端自定义字段，用于区分性别
  status?: number;
}

export interface AreaData {
  areaId: number;
  area: string;
  areaAlias: string;
  childCount: number;
  limitNum: number;
  areaImg?: string;
  childList?: ChildData[];
  status?: number;
  backgroundColor?: string;
}

export const useChildrenInAreaData = (classId: number | undefined, selectedDate?: string) => {
  // 区域和儿童数据
  const [areas, setAreas] = useState<AreaData[]>([]);
  const [children, setChildren] = useState<ChildData[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 判断是否是历史模式 - 使用useMemo避免重复计算
  const isHistoryMode = useMemo(() => {
    const today = new Date().toISOString().split('T')[0];
    return selectedDate && selectedDate !== today;
  }, [selectedDate]);

  // 获取区域背景颜色 - 通过区域ID确保颜色一致性
  const getAreaColor = (areaId: number) => {
    const colors = [
      'rgba(255, 145, 143, 1)',
      'rgba(249, 140, 255, 1)',
      'rgba(77, 219, 255, 1)',
      'rgba(109, 227, 135, 1)',
      'rgba(107, 184, 255, 1)',
    ];
    // 使用区域ID取模运算确保相同ID总是获得相同颜色
    return colors[areaId % colors.length];
  };

  // 加载班级区域列表 - 恢复useCallback
  const fetchAreas = useCallback(async () => {
    if (!classId) return;
    try {
      setLoading(true); // 恢复loading状态
      // 判断是否是历史日期（不是今天）
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式
      const isHistoryDate = selectedDate && selectedDate !== today;

      let res;
      if (isHistoryDate) {
        res = await listAreaHistory({ classId, entryDate: selectedDate });
      } else {
        res = await listArea({ classId });
      }

      if (res.status === 0 && res.data) {
        // 过滤掉未进区并转换数据格式
        const filteredData = res.data
          .filter((area: any) => area.areaId > 0) // 过滤掉 areaId: -1 的未进区
          .map((area: any) => ({
            areaId: area.areaId,
            area: area.area,
            areaAlias: area.areaAlias,
            limitNum: area.limitNum,
            childCount: area.childCount,
            childList: area.childList || [], // 确保包含儿童列表
            areaImg: area.areaImg, // 包含区域头像图片
            backgroundColor: getAreaColor(area.areaId),
          }));

        setAreas(filteredData);
      } else {
        message.error(res.message || '获取区域列表失败'); // 恢复错误提示
      }
    } catch (error: any) {
      message.error(error.message || '获取区域列表失败');
    } finally {
      setLoading(false); // 恢复loading状态
    }
  }, [classId, selectedDate, isHistoryMode]);

  // 加载区域儿童列表 - 恢复useCallback
  const fetchChildren = useCallback(async () => {
    if (!classId) return;

    try {
      setLoading(true); // 恢复loading状态
      // 判断是否是历史日期（不是今天）
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式
      const isHistoryDate = selectedDate && selectedDate !== today;

      let res: any;
      if (isHistoryDate) {
        res = await listChildHistory({ classId, entryDate: selectedDate });

        // 处理历史数据
        if (res.status === 0 && res.data) {
          if (Array.isArray(res.data)) {
            const childrenData = res.data.map((child: any) => {
              const gender = child.sex === 1 ? 'boy' : 'girl';
              return {
                ...child,
                gender,
              };
            });

            setChildren(childrenData);
          } else {
            console.error('历史儿童数据格式错误:', res.data);
            message.error('获取历史儿童列表格式不正确');
          }
        } else {
          message.error(res.message || '获取历史儿童列表失败');
        }
        return;
      } else {
        res = await listChild({ classId });
      }

      if (res.status === 0 && res.data) {
        // API返回了一个儿童数组，根据sex字段区分性别
        if (Array.isArray(res.data)) {
          const childrenData = res.data.map((child: any) => {
            // 确保性别字段正确，sex=1为男，sex=2为女
            const gender = child.sex === 1 ? 'boy' : 'girl';

            return {
              ...child,
              gender,
            };
          });
          setChildren(childrenData);
        } else {
          console.error('儿童数据格式错误:', res.data);
          message.error('获取儿童列表格式不正确'); // 恢复错误提示
        }
      } else {
        message.error(res.message || '获取儿童列表失败'); // 恢复错误提示
      }
    } catch (error: any) {
      console.error('获取儿童列表时出错:', error);
      message.error(error.message || '获取儿童列表失败');
    } finally {
      setLoading(false); // 恢复loading状态
    }
  }, [classId, selectedDate, isHistoryMode]);

  // 统一的数据刷新函数 - 恢复useCallback
  const refreshData = useCallback(
    async (options: { areas?: boolean; children?: boolean } = { areas: true, children: true }) => {
      // 历史模式下也允许刷新数据，因为进区操作后需要更新显示

      const promises = [];

      if (options.areas) {
        promises.push(fetchAreas());
      }

      if (options.children) {
        promises.push(fetchChildren());
      }

      try {
        await Promise.all(promises);
      } catch (error) {
        console.error('数据刷新失败:', error);
      }
    },
    [isHistoryMode, fetchAreas, fetchChildren],
  );

  // 统一的区域人数计算函数
  const getAreaChildCount = useCallback(
    (areaId: number) => {
      // 先查找对应的区域
      const area = areas.find((a) => a.areaId === areaId);

      // 如果区域存在且有childList属性，则使用childList（优先使用API返回的数据）
      if (area && area.childList) {
        return area.childList.length;
      }

      // 回退到从children数组中筛选（确保数据一致性）
      return children.filter((child) => child.areaId === areaId).length;
    },
    [areas, children],
  );

  // 获取未分配的儿童列表 - 恢复功能
  const getUnassignedChildren = useCallback(() => {
    // 筛选所有未进区的儿童
    return children.filter((child) => {
      // 如果areaId为null或未定义，或者area字段值为"未进区"
      return !child.areaId || child.area === '未进区' || child.areaAlias === '未进区';
    });
  }, [children]);

  // 获取特定区域的儿童列表
  const getChildrenInArea = useCallback(
    (areaId: number) => {
      // 历史模式下，优先从children数组中筛选（因为历史接口分别返回区域和儿童数据）
      if (isHistoryMode) {
        const filteredChildren = children.filter((child) => child.areaId === areaId);
        return filteredChildren.map((child) => ({
          ...child,
          gender: child.sex === 1 ? 'boy' : 'girl',
        }));
      }

      // 非历史模式下，先查找对应的区域
      const area = areas.find((a) => a.areaId === areaId);

      // 如果区域存在且有childList属性，则使用childList
      if (area && area.childList && Array.isArray(area.childList)) {
        return area.childList.map((child) => ({
          ...child,
          gender: child.sex === 1 ? 'boy' : 'girl',
        }));
      }

      // 回退到从children数组中筛选
      const filteredChildren = children.filter((child) => child.areaId === areaId);
      return filteredChildren.map((child) => ({
        ...child,
        gender: child.sex === 1 ? 'boy' : 'girl',
      }));
    },
    [areas, children, isHistoryMode],
  );

  // 按性别分组未分配的儿童 - 恢复功能
  const boysList = useMemo(() => {
    return getUnassignedChildren().filter((child) => child.gender === 'boy');
  }, [getUnassignedChildren]);

  const girlsList = useMemo(() => {
    return getUnassignedChildren().filter((child) => child.gender === 'girl');
  }, [getUnassignedChildren]);

  // 初始加载和日期变化时重新加载
  useEffect(() => {
    if (classId) {
      fetchAreas();
      fetchChildren();
    }
  }, [classId, selectedDate, fetchAreas, fetchChildren]);

  return {
    // 状态
    areas,
    children,
    loading,
    boysList,
    girlsList,
    isHistoryMode, // 暴露历史模式状态

    // 方法
    setAreas,
    setChildren,
    setLoading,
    fetchAreas,
    fetchChildren,
    refreshData,
    getAreaChildCount,
    getUnassignedChildren,
    getChildrenInArea,
  };
};
