import { useState } from 'react';
import {
  useSensor,
  useSensors,
  PointerSensor,
  TouchSensor,
  KeyboardSensor,
  DragEndEvent,
} from '@dnd-kit/core';
import { message } from 'antd';
import { entry, exitArea, entryHistory } from '@/services/childrenInArea';
import dayjs from 'dayjs';
import { ChildData, AreaData } from './useChildrenInAreaData';

interface UseDragDropProps {
  children: ChildData[];
  areas: AreaData[];
  setChildren: React.Dispatch<React.SetStateAction<ChildData[]>>;
  setLoading: React.Dispatch<React.SetStateAction<boolean>>;
  refreshData: () => Promise<void>;
  getAreaChildCount: (areaId: number) => number;
  isHistoryMode?: boolean;
  selectedDate?: string;
}

export const useDragDrop = ({
  children,
  areas,
  setChildren,
  setLoading,
  refreshData,
  getAreaChildCount,
  isHistoryMode = false,
  selectedDate,
}: UseDragDropProps) => {
  // 拖拽状态
  const [activeId, setActiveId] = useState<string | null>(null);
  const [startContainer, setStartContainer] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState<boolean>(false);

  // Configure sensors to detect drag events
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 300,
        tolerance: 8,
      },
    }),
    useSensor(KeyboardSensor),
  );

  // Get the active child data
  const getActiveChild = () => {
    if (!activeId) return null;

    const childId = activeId.replace('child-', '');
    const child = children.find((c) => c.childId.toString() === childId);
    return child || null;
  };

  const handleDragStart = (event: any) => {
    setIsDragging(true);
    setActiveId(event.active.id);

    const childId = event.active.id;
    const actualChildId = event.active.data.current?.childId;

    let child = children.find(
      (c) => `child-${c.childId}` === childId || c.childId === actualChildId,
    );

    if (!child) {
      for (const area of areas) {
        if (area.childList) {
          const foundChild = area.childList.find(
            (c) => `child-${c.childId}` === childId || c.childId === actualChildId,
          );
          if (foundChild) {
            child = foundChild;
            break;
          }
        }
      }
    }

    if (child && child.areaId) {
      setStartContainer(`area-${child.areaId}`);
    } else {
      setStartContainer('unassigned-container');
    }
  };

  const handleDragCancel = () => {
    setActiveId(null);
    setIsDragging(false);
    setStartContainer(null);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    setActiveId(null);
    setIsDragging(false);

    if (!over) {
      setStartContainer(null);
      return;
    }

    const activeId = active.id.toString();
    const overId = over.id.toString();

    if (overId === startContainer) {
      setStartContainer(null);
      return;
    }

    const childId = active.data.current?.childId || parseInt(activeId.replace('child-', ''), 10);

    let child = children.find((c) => c.childId === childId);

    if (!child) {
      for (const area of areas) {
        if (area.childList) {
          const foundChild = area.childList.find((c) => c.childId === childId);
          if (foundChild) {
            child = foundChild;
            break;
          }
        }
      }
    }

    // 处理拖拽到未分配区域
    if (overId === 'unassigned-container') {
      try {
        if (child && child.areaId) {
          // 历史模式下禁用出区操作
          if (isHistoryMode) {
            message.info('历史模式下无法进行出区操作');
            setStartContainer(null);
            return;
          }

          setLoading(true);
          const res = await exitArea({ childId });
          if (res.status === 0) {
            message.success('儿童已出区');
            setChildren((prevChildren) =>
              prevChildren.map((c) =>
                c.childId === childId
                  ? { ...c, areaId: null, area: '未进区', areaAlias: '未进区' }
                  : c,
              ),
            );
            refreshData();
          } else {
            message.error(res.message || '操作失败');
          }
          setLoading(false);
        }
        setStartContainer(null);
        return;
      } catch (error: any) {
        setLoading(false);
        message.error(error.message || '操作失败');
        setStartContainer(null);
        return;
      }
    }

    // 处理拖拽到待选区
    if (overId === 'boys-waiting-area' || overId === 'girls-waiting-area') {
      try {
        if (child && child.areaId && startContainer && startContainer.startsWith('area-')) {
          // 历史模式下禁用出区操作
          if (isHistoryMode) {
            message.info('历史模式下无法进行出区操作');
            setStartContainer(null);
            return;
          }

          setLoading(true);
          const res = await exitArea({ childId });
          if (res.status === 0) {
            message.success('儿童已出区');
            setChildren((prevChildren) =>
              prevChildren.map((c) =>
                c.childId === childId
                  ? { ...c, areaId: null, area: '未进区', areaAlias: '未进区' }
                  : c,
              ),
            );
            refreshData();
          } else {
            message.error(res.message || '操作失败');
          }
          setLoading(false);
        }
        setStartContainer(null);
        return;
      } catch (error: any) {
        setLoading(false);
        message.error(error.message || '操作失败');
        setStartContainer(null);
        return;
      }
    }

    try {
      // 处理拖拽到区域
      if (overId.startsWith('area-')) {
        const areaId = over.data.current?.areaId || parseInt(overId.replace('area-', ''), 10);

        const overData = over.data.current;
        if (!overData || overData.accepts !== 'child') {
          setStartContainer(null);
          return;
        }

        const currentAreaId = child?.areaId;

        if (currentAreaId !== areaId) {
          const targetArea = areas.find((a) => a.areaId === areaId);
          if (targetArea && getAreaChildCount(areaId) >= targetArea.limitNum) {
            message.warning(`${targetArea.areaAlias || targetArea.area}区域人数已满啦`);
            return;
          }

          setLoading(true);

          // 根据历史模式选择接口
          let res;
          if (isHistoryMode && selectedDate) {
            res = await entryHistory({
              childId,
              areaId,
              entryDate: selectedDate,
            });
          } else {
            const currentDate = dayjs().format('YYYY-MM-DD');
            res = await entry({
              childId,
              areaId,
              entryDate: currentDate,
            });
          }

          if (res.status === 0) {
            message.success('儿童已进入区域');

            if (child) {
              setChildren((prevChildren) =>
                prevChildren.map((c) => (c.childId === childId ? { ...c, areaId } : c)),
              );
            }

            refreshData();
          } else {
            message.error(res.message || '操作失败');
          }
          setLoading(false);
        }
      }
    } catch (error: any) {
      setLoading(false);
      message.info('区域人数已满');
    }

    setStartContainer(null);
  };

  return {
    activeId,
    startContainer,
    isDragging,
    sensors,
    getActiveChild,
    handleDragStart,
    handleDragCancel,
    handleDragEnd,
  };
};
