import { useState, useEffect } from 'react';
import { ChildData, AreaData } from './useChildrenInAreaData';

interface UseSelectionProps {
  children: ChildData[];
  areas: AreaData[];
  refreshData: (options?: { areas?: boolean; children?: boolean }) => Promise<void>;
}

export const useSelection = ({ children, areas, refreshData }: UseSelectionProps) => {
  // 点击选择状态
  const [selectedChildId, setSelectedChildId] = useState<number | null>(null);

  // 区域选择弹窗状态
  const [areaSelectVisible, setAreaSelectVisible] = useState(false);
  const [selectedChildForArea, setSelectedChildForArea] = useState<number | null>(null);

  // 添加全局点击事件监听器，点击页面其他地方取消选中状态
  useEffect(() => {
    const handleGlobalClick = (event: MouseEvent) => {
      // 检查点击的元素是否是儿童或区域相关的元素
      const target = event.target as HTMLElement;

      // 如果点击的不是儿童、区域或相关UI元素，则清除选中状态
      if (
        !target.closest('[data-child-element]') && // 儿童元素
        !target.closest('[data-area-element]') && // 区域元素
        !target.closest('.ant-drawer') && // 弹窗
        !target.closest('.ant-modal') && // 模态框
        !target.closest('.ant-popover') && // 气泡卡片
        !target.closest('.ant-tooltip') // 工具提示
      ) {
        setSelectedChildId(null);
      }
    };

    // 添加事件监听器
    document.addEventListener('click', handleGlobalClick);

    // 清理函数
    return () => {
      document.removeEventListener('click', handleGlobalClick);
    };
  }, []);

  // 选择儿童处理
  const handleSelectChild = (childId: number) => {
    // 如果点击的是已选中的儿童，则取消选择
    if (selectedChildId === childId) {
      setSelectedChildId(null);
    } else {
      setSelectedChildId(childId);
    }
  };

  // 获取选中儿童的性别
  const getSelectedChildGender = () => {
    if (!selectedChildId) return null;

    // 先从children数组中查找
    let child = children.find((c) => c.childId === selectedChildId);

    // 如果找不到，从区域中查找
    if (!child) {
      for (const area of areas) {
        if (area.childList) {
          child = area.childList.find((c) => c.childId === selectedChildId);
          if (child) break;
        }
      }
    }

    return child ? (child.sex === 1 ? 'boy' : 'girl') : null;
  };

  // 处理儿童头像点击，打开区域选择弹窗
  const handleChildAvatarClick = (childId: number) => {
    // 先打开弹窗，提升用户体验
    setSelectedChildForArea(childId);
    setAreaSelectVisible(true);

    // 弹窗打开后异步刷新区域数据，确保区域人数信息最新
    refreshData({ areas: true, children: false }).catch((error) => {
      console.error('刷新区域数据失败:', error);
    });
  };

  // 关闭区域选择弹窗
  const closeAreaSelectDrawer = () => {
    setAreaSelectVisible(false);
    setSelectedChildForArea(null);
  };

  return {
    // 状态
    selectedChildId,
    areaSelectVisible,
    selectedChildForArea,
    
    // 方法
    setSelectedChildId,
    setAreaSelectVisible,
    setSelectedChildForArea,
    handleSelectChild,
    getSelectedChildGender,
    handleChildAvatarClick,
    closeAreaSelectDrawer,
  };
};
