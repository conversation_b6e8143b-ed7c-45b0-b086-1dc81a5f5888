import { useState, useEffect } from 'react';
import { message } from 'antd';

export const useFullscreenAndZoom = () => {
  // 全屏状态
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);

  // 缩放状态
  const [zoomScale, setZoomScale] = useState<number>(1);

  // 缩放级别
  const zoomLevels = [0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2];

  // 计算各元素的动态尺寸
  const getScaledSize = (baseSize: number) => {
    if (!isFullscreen) return baseSize; // 非全屏时使用原始尺寸
    return Math.round(baseSize * zoomScale); // 全屏时根据缩放比例调整
  };

  // 计算字体大小
  const getScaledFontSize = (baseFontSize: number) => {
    if (!isFullscreen) return baseFontSize;
    return Math.round(baseFontSize * zoomScale);
  };

  // 获取区域头像高度（增高一些）
  const getAreaAvatarHeight = () => {
    const baseHeight = isFullscreen ? 160 : 150; // 增大尺寸：全屏时120px，非全屏时110px
    return getScaledSize(baseHeight);
  };

  // 获取区域标题字体大小
  const getAreaTitleFontSize = () => {
    const baseSize = isFullscreen ? 18 : 16; // 增大字体：全屏时18px，非全屏时16px
    return getScaledFontSize(baseSize);
  };

  // 获取儿童头像尺寸
  const getChildAvatarSize = () => getScaledSize(72);

  // 获取儿童头像内部尺寸
  const getChildAvatarInnerSize = () => getScaledSize(68);

  // 获取儿童姓名字体大小
  const getChildNameFontSize = () => getScaledFontSize(16);

  // 获取区域占位符尺寸
  const getPlaceholderSize = () => {
    const baseSize = isFullscreen ? 60 : 50; // 全屏时增大到60px
    return getScaledSize(baseSize);
  };

  // 获取区域内儿童的较小尺寸
  const getAreaChildAvatarSize = () => getScaledSize(50); // 区域内儿童容器尺寸
  const getAreaChildAvatarInnerSize = () => getScaledSize(46); // 区域内儿童头像尺寸
  const getAreaChildNameFontSize = () => getScaledFontSize(12); // 区域内儿童姓名字体

  // 获取删除按钮的动态尺寸
  const getDeleteButtonSize = (containerSize: number) => {
    // 删除按钮大小根据容器大小动态调整，约为容器大小的30%
    return Math.max(12, Math.round(containerSize * 0.3));
  };

  // 获取删除按钮图标的动态尺寸
  const getDeleteButtonIconSize = (buttonSize: number) => {
    // 图标大小约为按钮大小的50%
    return Math.max(8, Math.round(buttonSize * 0.5));
  };

  // 切换全屏
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement
        .requestFullscreen()
        .then(() => {
          setIsFullscreen(true);
          // 禁用 body 滚动条
          document.body.style.overflow = 'hidden';
        })
        .catch((err) => {
          message.error(`无法进入全屏: ${err.message}`);
        });
    } else {
      if (document.exitFullscreen) {
        document
          .exitFullscreen()
          .then(() => {
            setIsFullscreen(false);
            // 恢复 body 滚动条
            document.body.style.overflow = 'auto';
            // 退出全屏时重置缩放
            setZoomScale(1);
          })
          .catch((err) => {
            message.error(`无法退出全屏: ${err.message}`);
          });
      }
    }
  };

  // 缩小功能
  const handleZoomOut = () => {
    const currentIndex = zoomLevels.findIndex((level) => Math.abs(level - zoomScale) < 0.01);
    if (currentIndex > 0) {
      setZoomScale(zoomLevels[currentIndex - 1]);
    }
  };

  // 放大功能
  const handleZoomIn = () => {
    const currentIndex = zoomLevels.findIndex((level) => Math.abs(level - zoomScale) < 0.01);
    if (currentIndex < zoomLevels.length - 1) {
      setZoomScale(zoomLevels[currentIndex + 1]);
    }
  };

  // 重置缩放
  const handleResetZoom = () => {
    setZoomScale(1);
  };

  // 监听全屏变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isFullscreenNow = !!document.fullscreenElement;
      setIsFullscreen(isFullscreenNow);

      // 根据全屏状态设置 body 的滚动条
      document.body.style.overflow = isFullscreenNow ? 'hidden' : 'auto';
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);

    return () => {
      // 组件卸载时恢复 body 滚动条
      document.body.style.overflow = 'auto';
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  return {
    // 状态
    isFullscreen,
    zoomScale,

    // 尺寸计算方法
    getScaledSize,
    getScaledFontSize,
    getAreaAvatarHeight,
    getAreaTitleFontSize,
    getChildAvatarSize,
    getChildAvatarInnerSize,
    getChildNameFontSize,
    getPlaceholderSize,
    getAreaChildAvatarSize,
    getAreaChildAvatarInnerSize,
    getAreaChildNameFontSize,
    getDeleteButtonSize,
    getDeleteButtonIconSize,

    // 操作方法
    toggleFullscreen,
    handleZoomOut,
    handleZoomIn,
    handleResetZoom,
  };
};
