import { useState, useEffect } from 'react';
import { AreaData } from './useChildrenInAreaData';

interface UsePaginationAndLayoutProps {
  areas: AreaData[];
  isFullscreen: boolean;
}

export const usePaginationAndLayout = ({ areas, isFullscreen }: UsePaginationAndLayoutProps) => {
  // 区域分页状态
  const [currentAreaPage, setCurrentAreaPage] = useState<number>(1);
  const areasPerPage = 12; // 每页显示12个区域（4行x3列）

  // 响应式布局状态
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 计算响应式列数
  const getGridColumns = () => {
    if (windowWidth < 768) return 2; // 手机：2列
    if (windowWidth < 1024) return 3; // 平板：3列
    if (windowWidth < 1440) return 4; // 小桌面：4列
    return 5; // 大桌面：5列
  };

  // 检测区域排数
  const getAreaRows = () => {
    if (!isFullscreen) return 0; // 只在全屏状态下检测
    const columns = getGridColumns();
    return Math.ceil(areas.length / columns);
  };

  // 动态调整布局比例
  const getChildrenAreaHeightRatio = () => {
    if (!isFullscreen) return 35; // 非全屏状态默认35%

    const rows = getAreaRows();
    if (rows === 1) {
      return 50; // 区域只有1排时，儿童区域占50%
    } else if (rows === 2) {
      return 45; // 区域有2排时，儿童区域占45%
    }
    return 35; // 区域有3排或更多时，默认35%
  };

  const getAreaHeightRatio = () => {
    if (!isFullscreen) return 65; // 非全屏状态默认65%

    const rows = getAreaRows();
    if (rows === 1) {
      return 50; // 区域只有1排时，区域占50%
    } else if (rows === 2) {
      return 55; // 区域有2排时，区域占55%
    }
    return 65; // 区域有3排或更多时，默认65%
  };

  // 获取当前页面要显示的区域
  const getCurrentPageAreas = () => {
    const startIdx = (currentAreaPage - 1) * areasPerPage;
    return areas.slice(startIdx, startIdx + areasPerPage);
  };

  // 切换到上一页区域
  const handlePrevAreaPage = () => {
    if (currentAreaPage > 1) {
      setCurrentAreaPage(currentAreaPage - 1);
    }
  };

  // 切换到下一页区域
  const handleNextAreaPage = () => {
    if (currentAreaPage * areasPerPage < areas.length) {
      setCurrentAreaPage(currentAreaPage + 1);
    }
  };

  return {
    // 状态
    currentAreaPage,
    areasPerPage,
    windowWidth,
    
    // 计算方法
    getGridColumns,
    getAreaRows,
    getChildrenAreaHeightRatio,
    getAreaHeightRatio,
    getCurrentPageAreas,
    
    // 操作方法
    handlePrevAreaPage,
    handleNextAreaPage,
    
    // 设置方法
    setCurrentAreaPage,
  };
};
