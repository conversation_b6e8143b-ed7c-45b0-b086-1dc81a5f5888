import { message, Modal } from 'antd';
import dayjs from 'dayjs';
import { entry, exitArea, entryHistory } from '@/services/childrenInArea';
import { ChildData, AreaData } from './useChildrenInAreaData';

interface UseAreaOperationsProps {
  classId: number | undefined;
  children: ChildData[];
  areas: AreaData[];
  setChildren: React.Dispatch<React.SetStateAction<ChildData[]>>;
  setLoading: React.Dispatch<React.SetStateAction<boolean>>;
  refreshData: () => Promise<void>;
  getAreaChildCount: (areaId: number) => number;
  isHistoryMode?: boolean;
  selectedDate?: string;
}

export const useAreaOperations = ({
  classId,
  children,
  areas,
  setChildren,
  setLoading,
  refreshData,
  getAreaChildCount,
  isHistoryMode = false,
  selectedDate,
}: UseAreaOperationsProps) => {
  // 公共进区逻辑
  const performChildEntry = async (childId: number, areaId: number) => {

    try {
      setLoading(true);

      // 获取儿童当前所在区域
      const child = children.find((c) => c.childId === childId);

      // 如果儿童已在所选区域内，则不执行任何操作
      if (child?.areaId === areaId) {
        message.info('儿童已在该区域内');
        return { success: false, reason: 'already_in_area' };
      }

      // 检查目标区域是否已满
      const targetArea = areas.find((a) => a.areaId === areaId);
      if (targetArea) {
        const currentCount = getAreaChildCount(areaId);

        if (currentCount >= targetArea.limitNum) {
          message.error(
            `${targetArea.areaAlias || targetArea.area}区域已满 (${currentCount}/${
              targetArea.limitNum
            })，无法进入`,
          );
          return { success: false, reason: 'area_full' };
        }
      }

      // 如果儿童已在其他区域，先执行出区操作
      if (child?.areaId) {
        await exitArea({ childId });
      }

      // 执行进区操作 - 根据是否历史模式选择接口
      let res;
      if (isHistoryMode && selectedDate) {
        res = await entryHistory({ childId, areaId, entryDate: selectedDate });
      } else {
        const currentDate = dayjs().format('YYYY-MM-DD');
        res = await entry({
          childId,
          areaId,
          entryDate: currentDate,
        });
      }

      if (res.status === 0) {
        const targetArea = areas.find((a) => a.areaId === areaId);
        const modeText = isHistoryMode ? ' (历史模式)' : '';
        message.success(
          `${child?.title || '儿童'}已进入${targetArea?.areaAlias || targetArea?.area || '区域'}${modeText}`,
        );

        // 在刷新数据前，先在本地更新数据以保持UI一致性
        if (child) {
          setChildren((prevChildren) =>
            prevChildren.map((c) => (c.childId === childId ? { ...c, areaId } : c)),
          );
        }

        // 刷新数据
        await refreshData();

        return { success: true };
      } else {
        message.error(res.message || '进区操作失败，请重试');
        return { success: false, reason: 'api_error' };
      }
    } catch (error: any) {
      console.error('进区操作异常:', error);
      message.error('进区操作失败，请重试');
      return { success: false, reason: 'exception' };
    } finally {
      // 确保loading状态总是被重置
      setLoading(false);
    }
  };

  // 处理单独出区的方法
  const handleChildExitArea = async (childId: number) => {
    // 历史模式下禁用出区操作
    if (isHistoryMode) {
      message.info('历史模式下无法进行出区操作');
      return { success: false, reason: 'history_mode_disabled' };
    }

    try {
      setLoading(true);
      const res = await exitArea({ childId });
      if (res.status === 0) {
        const child = children.find((c) => c.childId === childId);
        message.success(`${child?.title || '儿童'}已出区`);

        // 在刷新数据前，先在本地更新数据以保持UI一致性
        setChildren((prevChildren) =>
          prevChildren.map((c) =>
            c.childId === childId ? { ...c, areaId: null, area: '未进区', areaAlias: '未进区' } : c,
          ),
        );

        // 刷新数据
        refreshData();

        return { success: true };
      } else {
        message.error(res.message || '出区操作失败');
        return { success: false };
      }
    } catch (error: any) {
      message.error(error.message || '出区操作失败');
      return { success: false };
    } finally {
      setLoading(false);
    }
  };

  // 清空所有区域
  const handleClearAllAreas = async () => {
    // 历史模式下禁用清空操作
    if (isHistoryMode) {
      message.info('历史模式下无法进行清空操作');
      return;
    }

    // 计算所有区域中的儿童总数
    const totalChildrenInAreas = areas.reduce((total, area) => {
      return total + getAreaChildCount(area.areaId);
    }, 0);

    if (totalChildrenInAreas === 0) {
      message.info('当前所有区域都是空的');
      return;
    }

    Modal.confirm({
      title: '确认清空所有区域',
      content: `确定要清空所有区域吗？这将使${totalChildrenInAreas}名儿童回到未分配状态。`,
      okText: '确认清空',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          setLoading(true);
          const res = await exitArea({ classId });
          if (res.status === 0) {
            message.success(`已清空所有区域，${totalChildrenInAreas}名儿童已回到未分配状态`);
            // 刷新数据
            refreshData();
          } else {
            message.error(res.message || '清空所有区域失败');
          }
        } catch (error: any) {
          message.error(error.message || '清空所有区域失败');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  return {
    performChildEntry,
    handleChildExitArea,
    handleClearAllAreas,
  };
};
