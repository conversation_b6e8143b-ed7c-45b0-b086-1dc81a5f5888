import React, { useState, useEffect, useMemo } from 'react';
import dayjs from 'dayjs';
import { PageContainer } from '@ant-design/pro-components';
import {
  DndContext,
  rectIntersection,
  useDraggable,
  useDroppable,
  DragOverlay,
} from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import {
  Avatar,
  Typography,
  Spin,
  Select,
  Button,
  Tooltip,
  Popover,
  Pagination,
  DatePicker,
} from 'antd';
import {
  ClearOutlined,
  LeftOutlined,
  RightOutlined,
  FullscreenOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import { useParams, useLocation, history, useModel } from '@umijs/max';
import { AreaSelectDrawer, FullscreenToolbar } from './components';
import {
  useChildrenInAreaData,
  useDragDrop,
  useAreaOperations,
  useSelection,
  useFullscreenAndZoom,
  usePaginationAndLayout,
  type AreaData,
} from './hooks';

// 图片压缩参数 - 提高图片质量
const IMAGE_COMPRESS_PARAMS = '?x-oss-process=image/resize,m_fill,w_500';
const IMAGE_PREVIEW_COMPRESS_PARAMS = '?x-oss-process=image/resize,m_fill,w_500';

// 添加图片压缩的工具函数
const addImageCompress = (url: string | undefined): string => {
  if (!url) return '';
  // 如果URL已经包含查询参数，使用&连接，否则使用?
  return url.includes('?')
    ? `${url}&x-oss-process=image/resize,m_fill,w_500`
    : `${url}${IMAGE_COMPRESS_PARAMS}`;
};

// 添加图片预览压缩的工具函数 - 用于区域头像预览
const addImagePreviewCompress = (url: string | undefined): string => {
  if (!url) return '';
  // 如果URL已经包含查询参数，使用&连接，否则使用?
  return url.includes('?')
    ? `${url}&x-oss-process=image/resize,m_fill,w_500`
    : `${url}${IMAGE_PREVIEW_COMPRESS_PARAMS}`;
};

// 定义默认头像URL常量
const DEFAULT_BOY_AVATAR =
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png' +
  IMAGE_COMPRESS_PARAMS;
const DEFAULT_GIRL_AVATAR =
  'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png' +
  IMAGE_COMPRESS_PARAMS;

// Modified ChildDraggable component to conditionally enable/disable dragging
const ChildDraggable = ({
  id,
  name,
  avatar,
  gender,
  childId,
  onExitArea,
  disableDrag = false,
  isSelected = false,
  onSelect,
  onAvatarClick, // 新增头像点击事件
  containerSize = 72, // 容器尺寸
  avatarSize = 68, // 头像尺寸
  fontSize = 16, // 字体大小
  deleteButtonSize = 22, // 删除按钮尺寸
  deleteIconSize = 11, // 删除按钮图标尺寸
}: any) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id,
    data: {
      childId, // 传递实际的childId
    },
    disabled: disableDrag, // 如果disableDrag为true，则禁用拖动
  });

  // 根据性别选择默认头像
  const defaultAvatar = gender === 'boy' ? DEFAULT_BOY_AVATAR : DEFAULT_GIRL_AVATAR;

  // 当姓名超过4个字符时，只显示前4个字符
  const displayName = name.length > 4 ? name.substring(0, 4) : name;

  const borderColor = gender === 'boy' ? 'rgba(78, 176, 251, 1)' : 'rgba(252, 151, 149, 1)';

  const dragProps = disableDrag ? {} : { ...listeners, ...attributes };

  // 当组件用在区域中且提供了onExitArea回调时，显示退出按钮
  return (
    <div
      ref={setNodeRef}
      {...dragProps}
      data-child-element="true" // 添加标识属性
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: disableDrag ? 'default' : 'grab',
        opacity: 1, // 移除拖拽时的半透明效果
        transform: CSS.Translate.toString(transform),
        // 拖拽时禁用过渡动画以避免残影
        transition: isDragging ? 'none' : 'transform 0.2s, opacity 0.2s',
        flexShrink: 0, // 防止被压缩
        width: `${containerSize + 8}px`, // 设置固定宽度，包含margin
        minWidth: `${containerSize + 8}px`, // 设置最小宽度
      }}
      onClick={(e) => {
        e.stopPropagation();
        if (onSelect) {
          onSelect(childId);
        }
      }}
    >
      {/* 圆形头像容器 */}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: `${containerSize}px`, // 使用动态容器大小
          height: `${containerSize}px`, // 使用动态容器大小
          margin: '4px', // 减少margin
          border: isSelected ? `3px solid #1890ff` : `2px solid ${borderColor}`,
          borderRadius: '50%',
          backgroundColor: isSelected ? 'rgba(24, 144, 255, 0.1)' : 'white',
          boxShadow: isSelected ? '0 0 12px #1890ff' : '0 2px 8px rgba(0,0,0,0.1)',
          position: 'relative',
        }}
      >
        <Avatar
          src={addImageCompress(avatar) || defaultAvatar}
          size={avatarSize} // 使用动态头像大小
          style={{
            flexShrink: 0, // 防止被压缩
            width: `${avatarSize}px`,
            height: `${avatarSize}px`,
            borderRadius: '50%',
            cursor: onAvatarClick ? 'pointer' : 'inherit', // 有点击事件时显示手型光标
          }}
          onClick={(e) => {
            if (onAvatarClick && e) {
              e.stopPropagation(); // 阻止事件冒泡
              onAvatarClick(childId);
            }
          }}
        />

        {/* 出区按钮，只在有onExitArea回调时显示 */}
        {onExitArea && (
          <Tooltip title="点击出区">
            <div
              onClick={(e) => {
                e.stopPropagation(); // 阻止事件冒泡，避免触发拖拽
                onExitArea(childId);
              }}
              style={{
                position: 'absolute' as const,
                top: `${-deleteButtonSize / 2}px`, // 动态定位
                right: `${-deleteButtonSize / 2}px`, // 动态定位
                width: `${deleteButtonSize}px`, // 使用动态尺寸
                height: `${deleteButtonSize}px`, // 使用动态尺寸
                borderRadius: '50%',
                backgroundColor: 'rgba(255, 0, 0, 0.9)', // 保持半透明红色
                color: 'white',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                cursor: 'pointer',
                zIndex: 100, // 适当的z-index
                // 去除边框
                boxShadow: '0 2px 6px rgba(0,0,0,0.3)', // 保持阴影
              }}
            >
              <CloseOutlined style={{ fontSize: `${deleteIconSize}px` }} />
            </div>
          </Tooltip>
        )}
      </div>

      {/* 名字显示在圆形容器外面 */}
      <Tooltip title={name.length > 4 ? name : ''} placement="bottom">
        <div
          style={{
            fontSize: `${fontSize}px`, // 使用动态字体大小
            fontWeight: 'bold',
            textAlign: 'center',
            lineHeight: '1.2',
            color: '#333',
            width: `${containerSize}px`, // 与头像容器宽度一致
            maxWidth: `${containerSize}px`, // 最大宽度与容器一致
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: '2px', // 与头像的间距
          }}
        >
          {displayName}
        </div>
      </Tooltip>
    </div>
  );
};

// Child item without draggable functionality - used for overlay
const ChildItem = ({ name, avatar, gender }: any) => {
  // 根据性别选择默认头像
  const defaultAvatar = gender === 'boy' ? DEFAULT_BOY_AVATAR : DEFAULT_GIRL_AVATAR;

  // 当姓名超过4个字符时，只显示前4个字符
  const displayName = name.length > 4 ? name.substring(0, 4) : name;

  const borderColor = gender === 'boy' ? 'rgba(78, 176, 251, 1)' : 'rgba(252, 151, 149, 1)';

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      {/* 圆形头像容器 */}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '88px', // 调整容器大小以适应头像+边框
          height: '88px', // 调整容器大小以适应头像+边框
          margin: '6px',
          border: `2px solid ${borderColor}`,
          borderRadius: '50%', // 圆形
          backgroundColor: 'white',
          boxShadow: '0 8px 20px rgba(0,0,0,0.4)',
          zIndex: 999,
          overflow: 'hidden', // 确保头像不会超出边框
        }}
      >
        <Avatar
          src={addImageCompress(avatar) || defaultAvatar}
          size={88} // 头像完全填充容器
          style={{
            flexShrink: 0, // 防止被压缩
            width: '88px',
            height: '88px',
            borderRadius: '50%',
          }}
        />
      </div>

      {/* 名字显示在圆形容器外面 */}
      <Tooltip title={name.length > 4 ? name : ''} placement="bottom">
        <div
          style={{
            fontSize: '12px',
            fontWeight: 'bold',
            textAlign: 'center',
            lineHeight: '1.2',
            color: '#333',
            maxWidth: '100px', // 适当增加宽度
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {displayName}
        </div>
      </Tooltip>
    </div>
  );
};

// Droppable area representing an activity zone
const ActivityZone = ({ id, children, areaId, onSelect }: any) => {
  const { setNodeRef } = useDroppable({
    id,
    data: {
      areaId,
      accepts: 'child', // 表示接受儿童拖拽项
      priority: 10, // 高优先级，但需要在拖拽项在区域上方时才生效
    },
  });

  return (
    <div
      ref={setNodeRef}
      data-area-element="true" // 添加标识属性
      className="p-0.5 my-0.5 min-h-[90px] rounded-md border border-gray-200 bg-white transition-all cursor-pointer"
      onClick={(e) => {
        e.stopPropagation();
        if (onSelect) {
          onSelect(areaId);
        }
      }}
    >
      {children}
    </div>
  );
};

// 区域预览卡片组件 - 只显示头像
const AreaPreviewCard = ({ area }: { area: AreaData }) => {
  return (
    <div
      style={{
        width: '300px',
        height: 'auto',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      {area.areaImg && (
        <img
          src={addImagePreviewCompress(area.areaImg)}
          alt={area.areaAlias || area.area}
          style={{
            width: '300px',
            height: 'auto',
            objectFit: 'cover',
            border: '3px solid #f0f0f0',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            borderRadius: '8px', // 轻微圆角，但不是圆形
          }}
        />
      )}
    </div>
  );
};

const ChildrenInArea: React.FC = () => {
  // 添加全局样式禁用文本选择和隐藏Footer
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .children-in-area-container * {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }
      .children-in-area-container *::selection {
        background: transparent;
      }
      .children-in-area-container *::-moz-selection {
        background: transparent;
      }
      /* 隐藏Footer，让main布局占满 */
      // .children-in-area-container ~ .ant-layout-footer,
      // .ant-layout-footer {
      //   display: none !important;
      // }
      /* 确保main区域占满整个高度 */
      .ant-layout-content {
        min-height: 100vh !important;
        height: 100vh !important;
      }
      /* 确保PageContainer占满高度 */
      .ant-pro-page-container {
        min-height: 100vh !important;
        height: 100vh !important;
      }
      /* 全屏状态下的特殊样式 */
      .children-in-area-container.fullscreen-mode {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        z-index: 999 !important;
      }
      .children-in-area-container.fullscreen-mode .ant-pro-page-container-children-content {
        padding: 0 !important;
      }
      /* 设置页面容器的padding */
      .children-in-area-container .ant-pro-page-container-children-container {
        padding-block-end: 20px;
        padding-inline: 20px;
        background: #FFF;
      }
      /* 确保弹窗在全屏状态下正确显示 */
      .ant-modal-root {
        z-index: 10000 !important;
      }
      .ant-modal-mask {
        z-index: 10000 !important;
      }
      .ant-modal-wrap {
        z-index: 10001 !important;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // URL参数和查询参数处理
  const params = useParams<{ classId?: string }>();
  const location = useLocation();
  const query = new URLSearchParams(location.search);
  const classIdFromQuery = query.get('classId');

  // 获取全局状态中的当前班级ID
  const { initialState } = useModel('@@initialState');
  const currentClassIdFromState = initialState?.currentUser?.currentClassId;

  // 当前班级ID - 优先使用URL参数，其次使用全局状态
  const [classId, setClassId] = useState<number | undefined>(
    Number(params.classId || classIdFromQuery) || currentClassIdFromState || undefined,
  );

  // 班级列表（如果需要切换班级）
  const classes = initialState?.userClass || [];

  // 日期选择器状态
  const [selectedDate, setSelectedDate] = useState(dayjs());

  // 稳定日期字符串，避免重复渲染
  const selectedDateString = useMemo(() => {
    return selectedDate?.format('YYYY-MM-DD');
  }, [selectedDate]);

  // 使用自定义 hooks
  const {
    areas,
    children,
    loading,
    boysList,
    girlsList,
    isHistoryMode,
    setChildren,
    setLoading,
    refreshData,
    getAreaChildCount,
    getChildrenInArea,
  } = useChildrenInAreaData(classId, selectedDateString);

  // 使用拖拽 hook
  const dragDropProps = useDragDrop({
    children,
    areas,
    setChildren,
    setLoading,
    refreshData,
    getAreaChildCount,
    isHistoryMode: !!isHistoryMode,
    selectedDate: selectedDateString,
  });

  // 使用区域操作 hook
  const { performChildEntry, handleChildExitArea, handleClearAllAreas } = useAreaOperations({
    classId,
    children,
    areas,
    setChildren,
    setLoading,
    refreshData,
    getAreaChildCount,
    isHistoryMode: !!isHistoryMode,
    selectedDate: selectedDateString,
  });

  // 使用选择状态 hook
  const {
    selectedChildId,
    areaSelectVisible,
    selectedChildForArea,
    setSelectedChildId,
    setAreaSelectVisible,
    setSelectedChildForArea,
    handleSelectChild,
    getSelectedChildGender,
    handleChildAvatarClick,
  } = useSelection({ children, areas, refreshData });

  // 使用全屏和缩放 hook
  const {
    isFullscreen,
    zoomScale,
    getScaledFontSize,
    getAreaAvatarHeight,
    getAreaTitleFontSize,
    getChildAvatarSize,
    getChildAvatarInnerSize,
    getChildNameFontSize,
    getPlaceholderSize,
    getAreaChildAvatarSize,
    getAreaChildAvatarInnerSize,
    getAreaChildNameFontSize,
    getDeleteButtonSize,
    getDeleteButtonIconSize,
    toggleFullscreen,
    handleZoomOut,
    handleZoomIn,
    handleResetZoom,
  } = useFullscreenAndZoom();

  // 使用分页和布局 hook
  const {
    currentAreaPage,
    areasPerPage,
    windowWidth,
    getGridColumns,
    getChildrenAreaHeightRatio,
    getAreaHeightRatio,
    getCurrentPageAreas,
    handlePrevAreaPage,
    handleNextAreaPage,
    setCurrentAreaPage,
  } = usePaginationAndLayout({ areas, isFullscreen });

  // 班级变更处理
  const handleClassChange = (newClassId: number) => {
    setClassId(newClassId);
    // 更新URL，不刷新页面
    history.push(`/ChildrenInArea?classId=${newClassId}`);
  };

  // 日期变更处理
  const handleDateChange = (date: any) => {
    setSelectedDate(date);
  };

  // 从拖拽 hook 中获取相关数据和方法
  const { isDragging, sensors, getActiveChild, handleDragStart, handleDragCancel, handleDragEnd } =
    dragDropProps;

  const activeChild = getActiveChild();

  // 处理区域选择
  const handleAreaSelect = async (areaId: number) => {
    if (!selectedChildForArea) return;

    // 检查目标区域是否已满（弹窗特殊处理：不显示提示，让弹窗组件处理动画）
    const targetArea = areas.find((a) => a.areaId === areaId);
    if (targetArea) {
      const currentCount = getAreaChildCount(areaId);
      const child = children.find((c) => c.childId === selectedChildForArea);

      // 如果儿童不在该区域且区域已满，则不显示提示，让弹窗组件处理动画
      if (child?.areaId !== areaId && currentCount >= targetArea.limitNum) {
        return; // 不关闭弹窗，不清除选中状态，不显示文字提示
      }
    }

    // 使用公共进区逻辑
    const result = await performChildEntry(selectedChildForArea, areaId);

    // 根据结果处理弹窗状态
    if (result.success || result.reason === 'already_in_area') {
      // 成功或已在区域内时关闭弹窗
      setAreaSelectVisible(false);
      setSelectedChildForArea(null);
    } else if (
      result.reason === 'area_full' ||
      result.reason === 'api_error' ||
      result.reason === 'exception'
    ) {
      // 其他错误情况也关闭弹窗
      setAreaSelectVisible(false);
      setSelectedChildForArea(null);
    }
  };

  // 选择区域处理
  const handleSelectArea = async (areaId: number) => {
    // 如果已选择了儿童，执行进区操作
    if (selectedChildId !== null) {
      // 使用公共进区逻辑
      await performChildEntry(selectedChildId, areaId);

      // 操作完成后清除选择状态
      setSelectedChildId(null);
    }
    // 删除了区域选中逻辑，现在只处理儿童进区
  };

  // 处理点击儿童区域（未分配区域）
  const handleUnassignedAreaClick = () => {
    // 如果已选择了儿童，执行出区操作
    if (selectedChildId !== null) {
      // 先从所有儿童中查找（包括区域中的儿童）
      let child = children.find((c) => c.childId === selectedChildId);

      // 如果在children数组中找不到，尝试从区域中查找
      if (!child) {
        for (const area of areas) {
          if (area.childList) {
            child = area.childList.find((c) => c.childId === selectedChildId);
            if (child) break;
          }
        }
      }

      if (child && child.areaId) {
        // 儿童在区域中，执行出区操作
        handleChildExitArea(selectedChildId);
      } else if (child) {
        // 儿童已经在未分配区域，清除选中状态
        setSelectedChildId(null);
      } else {
        setSelectedChildId(null);
      }
    }
  };

  // Container for unassigned children
  const UnassignedDroppable = () => {
    const { setNodeRef } = useDroppable({
      id: 'unassigned-container',
      data: {
        accepts: 'all', // 表示接受所有拖拽项
        priority: 1, // 低优先级，确保区域检测优先
      },
    });

    return (
      <div
        ref={setNodeRef}
        onClick={handleUnassignedAreaClick}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          transition: 'all 0.2s',
          zIndex: 0,
          pointerEvents: 'auto',
          cursor: selectedChildId ? 'pointer' : 'default',
        }}
      />
    );
  };

  if (!classId) {
    return (
      <PageContainer style={{ background: '#fff' }} className="children-in-area-container">
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Typography.Title level={4}>请选择班级</Typography.Title>
          <Select style={{ width: 200 }} placeholder="选择班级" onChange={handleClassChange}>
            {classes.map((cls: any) => (
              <Select.Option key={cls.id} value={cls.id}>
                {cls.title}
              </Select.Option>
            ))}
          </Select>
        </div>
      </PageContainer>
    );
  }

  // 男生待选区 Droppable
  const BoysWaitingArea = ({ children }: any) => {
    const { setNodeRef } = useDroppable({
      id: 'boys-waiting-area',
      data: {
        accepts: 'child',
        type: 'waiting-area',
        gender: 'boy',
        priority: 5, // 设置较高优先级，确保优先于unassigned-container
      },
    });

    return (
      <div ref={setNodeRef} style={{ flex: 1 }}>
        {children}
      </div>
    );
  };

  // 女生待选区 Droppable
  const GirlsWaitingArea = ({ children }: any) => {
    const { setNodeRef } = useDroppable({
      id: 'girls-waiting-area',
      data: {
        accepts: 'child',
        type: 'waiting-area',
        gender: 'girl',
        priority: 5, // 设置较高优先级，确保优先于unassigned-container
      },
    });

    return (
      <div ref={setNodeRef} style={{ flex: 1 }}>
        {children}
      </div>
    );
  };

  return (
    <PageContainer
      style={{
        background: '#fff',
        height: '100vh', // 占满整个视口高度
        padding: 0, // 移除默认padding
        display: 'flex',
        flexDirection: 'column',
      }}
      className={`children-in-area-container ${isFullscreen ? 'fullscreen-mode' : ''}`}
      title={!isFullscreen ? `幼儿进区计划${isHistoryMode ? ' (历史模式)' : ''}` : undefined} // 全屏时隐藏标题
      extra={
        !isFullscreen
          ? [
              <DatePicker
                key="datePicker"
                value={selectedDate}
                onChange={handleDateChange}
                style={{ marginRight: '8px' }}
                placeholder="选择日期"
              />,
              <Button
                key="fullscreen"
                icon={<FullscreenOutlined />}
                onClick={toggleFullscreen}
                style={{ marginRight: '8px' }}
              >
                {isFullscreen ? '退出全屏' : '全屏显示'}
              </Button>,
              // 历史模式下隐藏清空按钮
              !isHistoryMode && (
                <Button
                  key="clear"
                  icon={<ClearOutlined />}
                  onClick={handleClearAllAreas}
                  danger
                  disabled={isDragging}
                >
                  清空所有区域
                </Button>
              ),
            ]
          : undefined
      }
    >
      {/* 全屏时的顶部工具栏 */}
      {isFullscreen && (
        <FullscreenToolbar
          zoomScale={zoomScale}
          isDragging={isDragging}
          isHistoryMode={!!isHistoryMode}
          onZoomOut={handleZoomOut}
          onZoomIn={handleZoomIn}
          onResetZoom={handleResetZoom}
          onClearAllAreas={handleClearAllAreas}
          onToggleFullscreen={toggleFullscreen}
        />
      )}

      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          // height: isFullscreen ? '100vh' : 'calc(100vh - 112px)', // 全屏时使用100vh，否则减去导航栏高度
          // backgroundColor: '#fff',
          overflow: 'hidden', // 防止内容溢出
          flex: 1, // 占满剩余空间
        }}
      >
        <Spin spinning={loading}>
          <DndContext
            sensors={sensors}
            collisionDetection={rectIntersection}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            onDragCancel={handleDragCancel}
          >
            <div
              style={{
                position: 'relative',
                height: '100%', // 使用100%高度，依赖父容器的高度计算
                overflow: 'hidden', // 保持隐藏滚动条
              }}
            >
              <UnassignedDroppable />
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  ...(isFullscreen
                    ? { height: '100vh' } // 全屏状态下使用100vh
                    : { minHeight: '100%' }), // 非全屏状态下使用minHeight: '100%'
                  gap: 4, // 进一步减少gap
                  paddingBottom: '4px', // 进一步减少底部间距
                }}
              >
                {/* 区域部分 - 放在上面 */}
                <div
                  style={{
                    flex: `0 0 ${getAreaHeightRatio()}%`, // 区域占用动态高度比例
                    overflow: 'visible', // 改为visible，允许拖拽元素超出边界显示
                    minHeight: 0, // 允许flex子元素收缩
                    maxHeight: `${getAreaHeightRatio()}%`, // 限制最大高度
                    position: 'relative', // 添加相对定位
                    zIndex: 2, // 设置z-index确保在待选区之上
                  }}
                >
                  <div style={{ position: 'relative', padding: '0 4px' }}>
                    {' '}
                    {/* 进一步减少左右padding */}
                    {/* 左切换按钮 */}
                    {currentAreaPage > 1 && (
                      <Button
                        type="primary"
                        shape="circle"
                        icon={<LeftOutlined />}
                        onClick={handlePrevAreaPage}
                        style={{
                          position: 'absolute',
                          left: '5px',
                          top: '50%',
                          zIndex: 10,
                          transform: 'translateY(-50%)',
                        }}
                      />
                    )}
                    {/* 右切换按钮 */}
                    {currentAreaPage * areasPerPage < areas.length && (
                      <Button
                        type="primary"
                        shape="circle"
                        icon={<RightOutlined />}
                        onClick={handleNextAreaPage}
                        style={{
                          position: 'absolute',
                          right: '5px',
                          top: '50%',
                          zIndex: 10,
                          transform: 'translateY(-50%)',
                        }}
                      />
                    )}
                    <div
                      style={{
                        display: 'grid',
                        gridTemplateColumns: `repeat(${getGridColumns()}, 1fr)`, // 响应式列数
                        gap: windowWidth < 768 ? 2 : 4, // 进一步减小间距
                        width: '100%',
                      }}
                    >
                      {getCurrentPageAreas().map((area) => (
                        <div style={{ minWidth: 0 }} key={area.areaId}>
                          <div
                            style={{
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: 'center',
                              position: 'relative',
                              marginBottom: '2px', // 减少底部间距
                            }}
                          >
                            {/* 区域头像 */}
                            <div style={{ position: 'relative', width: '100%' }}>
                              <Popover
                                content={<AreaPreviewCard area={area} />}
                                title={null}
                                trigger={['hover', 'click']}
                                placement="topLeft"
                                styles={{
                                  body: {
                                    width: '306px', // 300px图片 + 3px边框*2 = 306px
                                    padding: '0',
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                  },
                                }}
                              >
                                <div
                                  className="cursor-pointer rounded-lg"
                                  onClick={() => handleSelectArea(area.areaId)}
                                >
                                  {area.areaImg ? (
                                    <div
                                      className="relative w-full rounded-lg overflow-hidden border-2 border-white/30"
                                      style={{
                                        height: `${getAreaAvatarHeight()}px`,
                                      }}
                                    >
                                      <img
                                        src={addImageCompress(area.areaImg)}
                                        alt={area.areaAlias || area.area}
                                        className="w-full h-full object-contain"
                                      />
                                      {/* 区域标题 - 无背景遮罩 */}
                                      <div className="absolute inset-0 flex items-center justify-center">
                                        <div
                                          className="text-white font-semibold text-center overflow-hidden text-ellipsis whitespace-nowrap max-w-[90%]"
                                          style={{
                                            fontSize: `${getAreaTitleFontSize()}px`,
                                            textShadow: '1px 1px 2px rgba(0,0,0,0.5)', // 加回文字阴影以确保可读性
                                          }}
                                        >
                                          {area.areaAlias || area.area}
                                        </div>
                                      </div>
                                    </div>
                                  ) : (
                                    <div
                                      className="bg-gray-100 text-gray-600 border-2 border-white/30 rounded-lg w-full flex items-center justify-center text-center p-1"
                                      style={{
                                        height: `${getAreaAvatarHeight()}px`,
                                        fontSize: `${getScaledFontSize(10)}px`,
                                      }}
                                    >
                                      <div
                                        className="overflow-hidden font-bold text-center leading-tight break-all"
                                        style={{
                                          display: '-webkit-box',
                                          WebkitLineClamp: 3,
                                          WebkitBoxOrient: 'vertical',
                                          fontSize: `${getScaledFontSize(10)}px`,
                                        }}
                                      >
                                        {area.areaAlias || area.area}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </Popover>
                            </div>
                          </div>
                          <div>
                            <ActivityZone
                              id={`area-${area.areaId}`}
                              areaId={area.areaId}
                              onSelect={handleSelectArea}
                            >
                              <div
                                style={{
                                  display: 'flex',
                                  flexDirection: 'row',
                                  flexWrap: 'wrap',
                                  justifyContent: 'center', // 居中对齐
                                  alignContent: 'center', // 垂直居中
                                  alignItems: 'center', // 项目居中
                                  width: '100%',
                                  height: '100%',
                                  padding: '2px', // 减少padding
                                  gap: '2px', // 减少间距
                                  minHeight: '60px', // 减少最小高度
                                }}
                              >
                                {getChildrenInArea(area.areaId)
                                  .filter((child) => child.childId) // 确保有有效的childId
                                  .map((child) => {
                                    const gender = child.sex === 1 ? 'boy' : 'girl';

                                    // 使区域内的儿童不可拖动，但提供关闭按钮出区
                                    return (
                                      <div
                                        key={`child-${child.childId}`}
                                        style={{
                                          flex: '0 0 auto', // 固定大小，不被压缩
                                          display: 'flex',
                                          justifyContent: 'center',
                                          alignItems: 'center',
                                          margin: '2px', // 统一外边距
                                        }}
                                      >
                                        <ChildDraggable
                                          id={`child-${child.childId}`}
                                          childId={child.childId}
                                          name={child.title}
                                          avatar={child.childAvatar}
                                          gender={gender}
                                          onExitArea={null}
                                          disableDrag={false}
                                          isSelected={selectedChildId === child.childId}
                                          onSelect={handleSelectChild}
                                          containerSize={getAreaChildAvatarSize()}
                                          avatarSize={getAreaChildAvatarInnerSize()}
                                          fontSize={getAreaChildNameFontSize()}
                                          deleteButtonSize={getDeleteButtonSize(
                                            getAreaChildAvatarSize(),
                                          )}
                                          deleteIconSize={getDeleteButtonIconSize(
                                            getDeleteButtonSize(getAreaChildAvatarSize()),
                                          )}
                                        />
                                      </div>
                                    );
                                  })}

                                {/* 空位占位显示 */}
                                {Array(
                                  Math.max(
                                    0,
                                    (area.limitNum || 5) - getChildrenInArea(area.areaId).length,
                                  ),
                                )
                                  .fill(null)
                                  .map((_, index) => (
                                    <div
                                      key={`empty-${index}`}
                                      style={{
                                        flex: '0 0 auto', // 固定大小，不被压缩
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        margin: '2px', // 统一外边距
                                      }}
                                    >
                                      <div
                                        style={{
                                          width: `${getPlaceholderSize()}px`, // 使用动态尺寸
                                          height: `${getPlaceholderSize()}px`,
                                          borderRadius: '50%', // 圆形
                                          background: 'rgba(245, 245, 245, 1)',
                                          border: '2px dashed rgba(201, 201, 201, 1)',
                                          cursor: 'pointer',
                                          transition: 'all 0.2s',
                                          flexShrink: 0, // 防止被压缩
                                        }}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleSelectArea(area.areaId);
                                        }}
                                        onMouseEnter={(e) => {
                                          e.currentTarget.style.background =
                                            'rgba(230, 230, 230, 1)';
                                          e.currentTarget.style.borderColor =
                                            'rgba(180, 180, 180, 1)';
                                        }}
                                        onMouseLeave={(e) => {
                                          e.currentTarget.style.background =
                                            'rgba(245, 245, 245, 1)';
                                          e.currentTarget.style.borderColor =
                                            'rgba(201, 201, 201, 1)';
                                        }}
                                      />
                                    </div>
                                  ))}
                              </div>
                            </ActivityZone>
                          </div>
                        </div>
                      ))}
                      {areas.length === 0 && (
                        <div style={{ gridColumn: '1 / span 4', width: '100%' }}>
                          <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
                            暂无区域数据
                          </div>
                        </div>
                      )}
                    </div>
                    {/* 分页指示器 */}
                    {areas.length > areasPerPage && (
                      <div style={{ textAlign: 'center', marginTop: '4px' }}>
                        {' '}
                        {/* 减少上边距 */}
                        <Pagination
                          simple
                          current={currentAreaPage}
                          pageSize={areasPerPage}
                          total={areas.length}
                          onChange={setCurrentAreaPage}
                          size="small" // 使用小尺寸
                        />
                      </div>
                    )}
                  </div>
                </div>

                {/* 儿童待选区 - 放在下面 */}
                <div
                  style={{
                    flex: `1 1 ${getChildrenAreaHeightRatio()}%`, // 儿童区域占用动态高度比例
                    borderTop: '1px solid rgba(245, 245, 245, 1)', // 减少边框粗细
                    marginTop: '2px', // 进一步减少上边距
                    minHeight: '130px', // 设置最小高度
                    maxHeight: `${getChildrenAreaHeightRatio()}%`, // 限制最大高度
                    overflow: 'hidden', // 确保不超出
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      minHeight: '130px', // 设置最小高度
                      height: '100%', // 使用100%高度，自适应父容器
                      gap: 4, // 进一步减少gap
                      padding: '2px 0', // 进一步减少padding
                    }}
                  >
                    <BoysWaitingArea>
                      <div
                        style={{
                          flex: '0 0 48%',
                          display: 'flex',
                          flexDirection: 'column',
                          minHeight: 0, // 允许flex子元素收缩
                          // 当选中男生时高亮
                          backgroundColor:
                            getSelectedChildGender() === 'boy'
                              ? 'rgba(78, 176, 251, 0.1)'
                              : 'transparent',
                          border:
                            getSelectedChildGender() === 'boy'
                              ? '2px solid rgba(78, 176, 251, 0.5)'
                              : '2px solid transparent',
                          borderRadius: '8px',
                          padding: getSelectedChildGender() === 'boy' ? '6px' : '8px',
                          transition: 'all 0.3s ease',
                          boxShadow:
                            getSelectedChildGender() === 'boy'
                              ? '0 0 12px rgba(78, 176, 251, 0.3)'
                              : 'none',
                          height: '100%', // 确保高度填满
                        }}
                      >
                        <div
                          style={{
                            fontSize: `${getScaledFontSize(14)}px`, // 使用动态字体大小
                            fontWeight: 'bold',
                            color: 'rgba(78, 176, 251, 1)',
                            marginBottom: '4px', // 减少底部间距
                            flex: '0 0 auto', // 标题固定高度
                          }}
                        >
                          男生区 (待选)
                        </div>
                        <div
                          style={{
                            display: 'flex',
                            flexWrap: 'wrap',
                            justifyContent: 'start',
                            alignContent: 'flex-start',
                            overflow: 'hidden', // 移除滚动条
                            flex: '1', // 使用flex自适应剩余高度
                            paddingRight: '4px', // 减少右侧padding
                            // 添加触摸滚动支持
                            WebkitOverflowScrolling: 'touch',
                            touchAction: 'pan-y', // 允许垂直滚动
                          }}
                        >
                          {boysList.map((boy) => (
                            <div
                              key={`child-${boy.childId}`}
                              style={{
                                margin: '2px', // 统一间距
                              }}
                            >
                              <ChildDraggable
                                id={`child-${boy.childId}`}
                                childId={boy.childId}
                                name={boy.title}
                                avatar={boy.childAvatar}
                                gender="boy"
                                isSelected={selectedChildId === boy.childId}
                                onSelect={handleSelectChild}
                                onAvatarClick={handleChildAvatarClick} // 添加头像点击事件
                                containerSize={getChildAvatarSize()}
                                avatarSize={getChildAvatarInnerSize()}
                                fontSize={getChildNameFontSize()}
                                deleteButtonSize={getDeleteButtonSize(getChildAvatarSize())}
                                deleteIconSize={getDeleteButtonIconSize(
                                  getDeleteButtonSize(getChildAvatarSize()),
                                )}
                              />
                            </div>
                          ))}
                          {boysList.length === 0 && (
                            <div style={{ padding: '10px', color: '#999' }}>暂无数据</div>
                          )}
                        </div>
                      </div>
                    </BoysWaitingArea>

                    <GirlsWaitingArea>
                      <div
                        style={{
                          flex: '0 0 48%',
                          display: 'flex',
                          flexDirection: 'column',
                          minHeight: 0, // 允许flex子元素收缩
                          // 当选中女生时高亮
                          backgroundColor:
                            getSelectedChildGender() === 'girl'
                              ? 'rgba(252, 151, 149, 0.1)'
                              : 'transparent',
                          border:
                            getSelectedChildGender() === 'girl'
                              ? '2px solid rgba(252, 151, 149, 0.5)'
                              : '2px solid transparent',
                          borderRadius: '8px',
                          padding: getSelectedChildGender() === 'girl' ? '6px' : '8px',
                          transition: 'all 0.3s ease',
                          boxShadow:
                            getSelectedChildGender() === 'girl'
                              ? '0 0 12px rgba(252, 151, 149, 0.3)'
                              : 'none',
                          height: '100%', // 确保高度填满
                        }}
                      >
                        <div
                          style={{
                            fontSize: `${getScaledFontSize(14)}px`, // 使用动态字体大小
                            fontWeight: 'bold',
                            color: 'rgba(252, 151, 149, 1)',
                            marginBottom: '4px', // 减少底部间距
                            flex: '0 0 auto', // 标题固定高度
                          }}
                        >
                          女生区 (待选)
                        </div>
                        <div
                          style={{
                            display: 'flex',
                            flexWrap: 'wrap',
                            justifyContent: 'start',
                            alignContent: 'flex-start',
                            overflow: 'hidden', // 移除滚动条
                            flex: '1', // 使用flex自适应剩余高度
                            paddingRight: '4px', // 减少右侧padding
                            // 添加触摸滚动支持
                            WebkitOverflowScrolling: 'touch',
                            touchAction: 'pan-y', // 允许垂直滚动
                          }}
                        >
                          {girlsList.map((girl) => (
                            <div
                              key={`child-${girl.childId}`}
                              style={{
                                margin: '2px', // 统一间距
                              }}
                            >
                              <ChildDraggable
                                id={`child-${girl.childId}`}
                                childId={girl.childId}
                                name={girl.title}
                                avatar={girl.childAvatar}
                                gender="girl"
                                isSelected={selectedChildId === girl.childId}
                                onSelect={handleSelectChild}
                                onAvatarClick={handleChildAvatarClick} // 添加头像点击事件
                                containerSize={getChildAvatarSize()}
                                avatarSize={getChildAvatarInnerSize()}
                                fontSize={getChildNameFontSize()}
                                deleteButtonSize={getDeleteButtonSize(getChildAvatarSize())}
                                deleteIconSize={getDeleteButtonIconSize(
                                  getDeleteButtonSize(getChildAvatarSize()),
                                )}
                              />
                            </div>
                          ))}
                          {girlsList.length === 0 && (
                            <div style={{ padding: '10px', color: '#999' }}>暂无数据</div>
                          )}
                        </div>
                      </div>
                    </GirlsWaitingArea>
                  </div>
                </div>
              </div>

              {/* Drag Overlay - shows the dragged item on top of everything */}
              <DragOverlay zIndex={9999}>
                {activeChild ? (
                  <ChildItem
                    name={activeChild.title}
                    avatar={activeChild.childAvatar}
                    gender={activeChild.sex === 1 ? 'boy' : 'girl'}
                  />
                ) : null}
              </DragOverlay>
            </div>
          </DndContext>
        </Spin>

        {/* 区域选择抽屉 */}
        <AreaSelectDrawer
          visible={areaSelectVisible}
          onClose={() => {
            setAreaSelectVisible(false);
            setSelectedChildForArea(null);
          }}
          selectedChildId={selectedChildForArea}
          childrenData={children}
          areas={areas}
          onAreaSelect={handleAreaSelect}
          addImageCompress={addImageCompress}
          defaultBoyAvatar={DEFAULT_BOY_AVATAR}
          defaultGirlAvatar={DEFAULT_GIRL_AVATAR}
          getAreaChildCount={getAreaChildCount}
        />
      </div>
    </PageContainer>
  );
};

export default ChildrenInArea;
