import React, { useState, useEffect } from 'react';
import { Drawer, Avatar } from 'antd';

// 接口类型定义
interface ChildData {
  childId: number;
  areaId?: number | null;
  title: string;
  area: string;
  areaAlias: string;
  childCount?: number;
  childAvatar: string;
  limitNum?: number;
  sex: number; // API返回的性别: 1为男，2为女
  gender?: 'boy' | 'girl'; // 前端自定义字段，用于区分性别
  status?: number;
}

interface AreaData {
  areaId: number;
  area: string;
  areaAlias: string;
  childCount: number;
  limitNum: number;
  areaImg?: string;
  childList?: ChildData[];
  status?: number;
  backgroundColor?: string;
}

interface AreaSelectDrawerProps {
  visible: boolean;
  onClose: () => void;
  selectedChildId: number | null;
  childrenData: ChildData[];
  areas: AreaData[];
  onAreaSelect: (areaId: number) => void;
  addImageCompress: (url: string | undefined) => string;
  defaultBoyAvatar: string;
  defaultGirlAvatar: string;
  getAreaChildCount: (areaId: number) => number; // 新增统一的人数计算函数
}

const AreaSelectDrawer: React.FC<AreaSelectDrawerProps> = ({
  visible,
  onClose,
  selectedChildId,
  childrenData,
  areas,
  onAreaSelect,
  addImageCompress,
  defaultBoyAvatar,
  defaultGirlAvatar,
  getAreaChildCount,
}) => {
  // 获取选中的儿童信息
  const selectedChild = selectedChildId
    ? childrenData.find((child) => child.childId === selectedChildId)
    : null;
  const childGender = selectedChild?.gender || (selectedChild?.sex === 1 ? 'boy' : 'girl');
  const defaultAvatar = childGender === 'boy' ? defaultBoyAvatar : defaultGirlAvatar;

  // 管理区域满员状态的动画
  const [fullAreaId, setFullAreaId] = useState<number | null>(null);

  // 添加CSS动画样式
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.5); }
        70% { box-shadow: 0 0 0 6px rgba(255, 77, 79, 0); }
        100% { box-shadow: 0 0 0 0 rgba(255, 77, 79, 0); }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // 获取特定区域的儿童列表
  const getChildrenInArea = (areaId: number) => {
    // 先查找对应的区域
    const area = areas.find((a) => a.areaId === areaId);

    // 如果区域存在且有childList属性，则使用childList
    if (area && area.childList) {
      return area.childList.map((child) => ({
        ...child,
        gender: child.sex === 1 ? 'boy' : 'girl',
      }));
    }

    // 回退到从childrenData中筛选
    return childrenData
      .filter((child) => child.areaId === areaId)
      .map((child) => ({
        ...child,
        gender: child.sex === 1 ? 'boy' : 'girl',
      }));
  };

  // 处理区域选择，包括满员检查
  const handleAreaClick = (areaId: number) => {
    // 使用统一的区域人数计算函数
    const currentCount = getAreaChildCount(areaId);
    const targetArea = areas.find((a) => a.areaId === areaId);

    if (targetArea && currentCount >= targetArea.limitNum) {
      // 区域已满，显示红色边框动画
      setFullAreaId(areaId);
      // 1.2秒后清除动画状态
      setTimeout(() => {
        setFullAreaId(null);
      }, 1200);
      return;
    }

    // 区域未满，执行正常的选择逻辑
    onAreaSelect(areaId);
  };

  return (
    <Drawer
      title="选择区域"
      open={visible}
      onClose={onClose}
      placement="bottom"
      height="650px"
      zIndex={10000} // 设置更高的z-index确保在全屏状态下也能显示
      styles={{
        body: {
          overflow: 'hidden', // 移除滚动条
          display: 'flex',
          flexDirection: 'column',
          padding: '16px',
        },
      }}
    >
      {/* 儿童头像展示区域 */}
      {selectedChild && (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            marginBottom: '10px',
            // padding: '20px',
          }}
        >
          <Avatar
            src={
              selectedChild.childAvatar
                ? addImageCompress(selectedChild.childAvatar)
                : defaultAvatar
            }
            size={160} // 大头像
            style={{
              border: '4px solid #fff',
              boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
              marginBottom: '12px',
            }}
          />
          <div
            style={{
              fontSize: '18px',
              fontWeight: 'bold',
              color: '#333',
              textAlign: 'center',
            }}
          >
            {selectedChild.title || ''}
          </div>
          {/* <div
            style={{
              fontSize: '14px',
              color: '#666',
              marginTop: '4px',
            }}
          >
            请为该儿童选择区域
          </div> */}
        </div>
      )}

      {/* 区域列表 */}
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center', // 居中对齐
          alignItems: 'center',
          gap: 8, // 减少区域之间的间距以适应更多区域
          padding: '8px 0', // 减少上下padding
          flex: '1', // 使用flex自适应剩余空间
          overflow: 'hidden', // 确保不出现滚动条
          flexWrap: 'nowrap', // 不换行，保持一行显示
        }}
      >
        {areas.map((area) => {
          const isAreaFull = fullAreaId === area.areaId;
          return (
            <div
              key={area.areaId}
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                padding: '8px', // 减少内边距以适应更多区域
                border: isAreaFull ? '3px solid #ff4d4f' : '2px solid #eee', // 满员时红色边框
                borderRadius: '12px', // 增加圆角
                cursor: 'pointer',
                transition: 'all 0.3s ease', // 增加过渡时间
                backgroundColor: 'white',
                minHeight: '240px', // 增加最小高度以适应更多儿童头像和区域图片
                maxHeight: '260px', // 增加最大高度，给儿童头像区域更多空间
                justifyContent: 'flex-start', // 改为顶部对齐，避免内容被挤压
                flex: '1 1 0', // 自适应宽度，平均分配空间
                minWidth: '100px', // 设置最小宽度，确保不会太小
                maxWidth: 'none', // 移除最大宽度限制，允许自适应
                // 满员时的动画效果
                animation: isAreaFull ? 'pulse 1s ease-in-out' : 'none',
                boxShadow: isAreaFull ? '0 0 12px rgba(255, 77, 79, 0.6)' : 'none',
                transform: isAreaFull ? 'scale(1.02)' : 'scale(1)',
              }}
              onClick={() => handleAreaClick(area.areaId)}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = '#1890ff';
                e.currentTarget.style.boxShadow = '0 4px 16px rgba(24, 144, 255, 0.3)'; // 增强阴影
                e.currentTarget.style.transform = 'translateY(-2px)'; // 添加悬浮效果
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = '#eee';
                e.currentTarget.style.boxShadow = 'none';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              <Avatar
                src={area.areaImg ? addImageCompress(area.areaImg) : undefined}
                size={130}
                style={{
                  marginBottom: '8px',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                  backgroundColor: area.areaImg ? 'transparent' : '#f0f0f0',
                  color: area.areaImg ? 'transparent' : '#666',
                  width: '100%', // 填满父级容器宽度
                  height: '130px', // 增大高度以匹配size
                  flexShrink: 0, // 防止被压缩
                  borderRadius: '8px', // 修改为长方形
                  objectFit: 'contain', // 修改为contain样式，完整显示图片
                }}
              >
                {!area.areaImg && (
                  <div
                    style={{
                      overflow: 'hidden',
                      display: '-webkit-box',
                      WebkitLineClamp: 3, // 最多显示3行
                      WebkitBoxOrient: 'vertical',
                      wordBreak: 'break-all', // 允许单词内换行
                      fontSize: '12px',
                      fontWeight: 'bold',
                      textAlign: 'center',
                      lineHeight: '1.1',
                    }}
                  >
                    {area.areaAlias || area.area}
                  </div>
                )}
              </Avatar>
              <div
                style={{
                  fontSize: '14px', // 适当减小字体以适应多行
                  fontWeight: 'bold',
                  textAlign: 'center',
                  color: '#333',
                  marginBottom: '6px', // 增加间距
                  lineHeight: '1.3',
                  wordBreak: 'break-word', // 允许单词内换行
                  maxWidth: '100%', // 限制最大宽度
                  minHeight: '20px', // 设置最小高度
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                {area.areaAlias || area.area}({getAreaChildCount(area.areaId)}/{area.limitNum})
              </div>
              {/* 显示区域中的儿童头像和占位符 */}
              <div
                style={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  justifyContent: 'center',
                  alignItems: 'center',
                  gap: '2px',
                  padding: '4px',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '8px',
                  minHeight: '32px',
                  maxWidth: '100%',
                }}
              >
                {/* 显示已进区的儿童头像 */}
                {getChildrenInArea(area.areaId).map((child) => {
                  const childGender = child.gender || (child.sex === 1 ? 'boy' : 'girl');
                  const defaultAvatar =
                    childGender === 'boy' ? defaultBoyAvatar : defaultGirlAvatar;

                  return (
                    <Avatar
                      key={child.childId}
                      src={child.childAvatar ? addImageCompress(child.childAvatar) : defaultAvatar}
                      size={20}
                      style={{
                        border: '1px solid #ddd',
                        flexShrink: 0,
                      }}
                    />
                  );
                })}

                {/* 显示剩余位置的占位符 */}
                {Array.from({
                  length: Math.max(0, (area.limitNum || 0) - getAreaChildCount(area.areaId)),
                }).map((_, index) => (
                  <div
                    key={`placeholder-${index}`}
                    style={{
                      width: '20px',
                      height: '20px',
                      borderRadius: '50%',
                      border: '1px dashed #ccc',
                      backgroundColor: '#f5f5f5',
                      flexShrink: 0,
                    }}
                  />
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </Drawer>
  );
};

export default AreaSelectDrawer;
