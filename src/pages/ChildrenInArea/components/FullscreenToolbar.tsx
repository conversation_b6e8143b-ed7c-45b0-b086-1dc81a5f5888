import React, { useState, useEffect } from 'react';
import { Button } from 'antd';
import {
  ZoomOutOutlined,
  ZoomInOutlined,
  ReloadOutlined,
  FullscreenOutlined,
} from '@ant-design/icons';

interface FullscreenToolbarProps {
  zoomScale: number;
  isDragging: boolean;
  isHistoryMode?: boolean;
  onZoomOut: () => void;
  onZoomIn: () => void;
  onResetZoom: () => void;
  onClearAllAreas: () => void;
  onToggleFullscreen: () => void;
}

const FullscreenToolbar: React.FC<FullscreenToolbarProps> = ({
  zoomScale,
  isHistoryMode = false,
  onZoomOut,
  onZoomIn,
  onResetZoom,
  onToggleFullscreen,
}) => {
  // 工具栏拖动状态 - 修复初始位置，确保完全可见
  const [toolbarPosition, setToolbarPosition] = useState<{ x: number; y: number }>({
    x: Math.max(10, window.innerWidth - 240), // 增加右边距，确保完全可见
    y: 10,
  });
  const [isDraggingToolbar, setIsDraggingToolbar] = useState<boolean>(false);
  const [dragStart, setDragStart] = useState<{
    x: number;
    y: number;
    startX: number;
    startY: number;
  }>({ x: 0, y: 0, startX: 0, startY: 0 });

  // 工具栏拖动处理函数
  const handleToolbarMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDraggingToolbar(true);
    setDragStart({
      x: e.clientX,
      y: e.clientY,
      startX: toolbarPosition.x,
      startY: toolbarPosition.y,
    });
  };

  const handleToolbarTouchStart = (e: React.TouchEvent) => {
    e.preventDefault();
    const touch = e.touches[0];
    setIsDraggingToolbar(true);
    setDragStart({
      x: touch.clientX,
      y: touch.clientY,
      startX: toolbarPosition.x,
      startY: toolbarPosition.y,
    });
  };

  const handleToolbarMove = (clientX: number, clientY: number) => {
    if (!isDraggingToolbar) return;

    const deltaX = clientX - dragStart.x;
    const deltaY = clientY - dragStart.y;

    // 计算新位置，限制在屏幕范围内，增加右边距确保完全可见
    const toolbarWidth = 240; // 工具栏大概宽度
    const newX = Math.max(
      10,
      Math.min(window.innerWidth - toolbarWidth, dragStart.startX + deltaX),
    );
    const newY = Math.max(10, Math.min(window.innerHeight - 60, dragStart.startY + deltaY));

    setToolbarPosition({ x: newX, y: newY });
  };

  const handleToolbarMouseMove = (e: MouseEvent) => {
    handleToolbarMove(e.clientX, e.clientY);
  };

  const handleToolbarTouchMove = (e: TouchEvent) => {
    e.preventDefault();
    const touch = e.touches[0];
    handleToolbarMove(touch.clientX, touch.clientY);
  };

  const handleToolbarEnd = () => {
    setIsDraggingToolbar(false);
  };

  // 监听工具栏拖动事件
  useEffect(() => {
    if (!isDraggingToolbar) return;

    document.addEventListener('mousemove', handleToolbarMouseMove);
    document.addEventListener('mouseup', handleToolbarEnd);
    document.addEventListener('touchmove', handleToolbarTouchMove, { passive: false });
    document.addEventListener('touchend', handleToolbarEnd);

    return () => {
      document.removeEventListener('mousemove', handleToolbarMouseMove);
      document.removeEventListener('mouseup', handleToolbarEnd);
      document.removeEventListener('touchmove', handleToolbarTouchMove);
      document.removeEventListener('touchend', handleToolbarEnd);
    };
  }, [isDraggingToolbar, dragStart]);

  // 监听窗口大小变化，调整工具栏位置
  useEffect(() => {
    const handleResize = () => {
      const toolbarWidth = 240;
      setToolbarPosition((prev) => ({
        x: Math.max(10, Math.min(window.innerWidth - toolbarWidth, prev.x)),
        y: Math.max(10, Math.min(window.innerHeight - 60, prev.y)),
      }));
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div
      style={{
        position: 'fixed',
        top: `${toolbarPosition.y}px`,
        left: `${toolbarPosition.x}px`,
        zIndex: 9999,
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: '10px 12px',
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        userSelect: 'none',
        transition: isDraggingToolbar ? 'none' : 'all 0.2s ease',
        border: '1px solid rgba(0, 0, 0, 0.1)', // 增加边框提升可见性
      }}
    >
      {/* 拖动手柄区域 - 增大触摸区域 */}
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '2px',
          cursor: isDraggingToolbar ? 'grabbing' : 'grab',
          padding: '8px 6px', // 增大触摸区域
          borderRadius: '4px',
          transition: 'background-color 0.2s',
          minWidth: '16px', // 确保最小触摸区域
          minHeight: '32px', // 增加高度便于触摸
          justifyContent: 'center',
          alignItems: 'center',
        }}
        onMouseDown={handleToolbarMouseDown}
        onTouchStart={handleToolbarTouchStart}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = 'transparent';
        }}
        title="拖动工具栏"
      >
        {[...Array(3)].map((_, i) => (
          <div
            key={i}
            style={{
              width: '5px', // 稍微增大圆点
              height: '5px',
              backgroundColor: '#999',
              borderRadius: '50%',
            }}
          />
        ))}
      </div>

      {/* 历史模式标识 */}
      {isHistoryMode && (
        <div
          style={{
            fontSize: '12px',
            color: '#1890ff',
            fontWeight: 'bold',
            padding: '2px 8px',
            backgroundColor: 'rgba(24, 144, 255, 0.1)',
            borderRadius: '4px',
            border: '1px solid rgba(24, 144, 255, 0.3)',
            marginRight: '8px',
          }}
        >
          历史模式
        </div>
      )}

      {/* 缩放控制组 */}
      <div
        style={{
          display: 'flex',
          gap: '6px',
          borderRight: '1px solid #f0f0f0',
          paddingRight: '10px',
        }}
        onMouseDown={(e) => e.stopPropagation()}
        onTouchStart={(e) => e.stopPropagation()}
      >
        <Button
          icon={<ZoomOutOutlined style={{ fontSize: '16px' }} />}
          onClick={(e) => {
            e.stopPropagation();
            onZoomOut();
          }}
          disabled={zoomScale <= 0.5}
          title="缩小视图"
          size="middle"
          style={{ minWidth: '36px', height: '36px' }}
        />
        <Button
          icon={<ZoomInOutlined style={{ fontSize: '16px' }} />}
          onClick={(e) => {
            e.stopPropagation();
            onZoomIn();
          }}
          disabled={zoomScale >= 1.2}
          title="放大视图"
          size="middle"
          style={{ minWidth: '36px', height: '36px' }}
        />
        <Button
          icon={<ReloadOutlined style={{ fontSize: '16px' }} />}
          onClick={(e) => {
            e.stopPropagation();
            onResetZoom();
          }}
          disabled={zoomScale === 1}
          title={`重置缩放 (当前: ${Math.round(zoomScale * 100)}%)`}
          size="middle"
          style={{ minWidth: '36px', height: '36px' }}
        />
      </div>

      {/* 操作控制组 */}
      <div
        style={{ display: 'flex', gap: '12px' }}
        onMouseDown={(e) => e.stopPropagation()}
        onTouchStart={(e) => e.stopPropagation()}
      >
        {/* 隐藏清空所有区域按钮 */}
        {/* <Button
          icon={<ClearOutlined style={{ fontSize: '16px' }} />}
          onClick={(e) => {
            e.stopPropagation();
            onClearAllAreas();
          }}
          danger
          disabled={isDragging}
          size="middle"
          title="清空所有区域"
          style={{ minWidth: '36px', height: '36px' }}
        /> */}
        <Button
          icon={<FullscreenOutlined style={{ fontSize: '16px' }} />}
          onClick={(e) => {
            e.stopPropagation();
            onToggleFullscreen();
          }}
          type="primary"
          size="middle"
          title="退出全屏"
          style={{ minWidth: '36px', height: '36px' }}
        />
      </div>
    </div>
  );
};

export default FullscreenToolbar;
