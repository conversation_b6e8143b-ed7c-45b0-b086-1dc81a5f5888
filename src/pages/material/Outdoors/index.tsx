import {
  getMaterialOutdoorsList,
  fetchOutList,
  queryOutProductList,
  saveMaterialInfo,
  generatePlayList,
  exportMaterialInfo,
  exportMaterialDetail,
} from '@/services/material';
import { deleteAreaProduct } from '@/services/classAreaProduct';

// import { getMaterialClassList } from '@/services/api';
import { DictionaryCategory } from '@/services/constants';
import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProFormInstance,
  ProTable,
  ModalForm,
  ProFormText,
  ProFormSelect,
  ProFormRadio,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Button, Image, message, Tag, Popconfirm, Form } from 'antd';
import { useEffect, useRef, useState } from 'react';
import UploadImageComponent from '@/components/UploadImage';
import { fetchUserClasses } from '@/services/apis';
import { useModal } from '@/components/UseModal';
import TemplateList from '../components/TemplateList';

const OutdoorsMaterial = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const { initialState } = useModel('@@initialState');
  const classOptions = useRef<any[]>([]);

  const [areaOptions, setAreaOptions] = useState<any[]>([]);
  const [form] = Form.useForm();
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<any>({});
  const [isToy, setIsToy] = useState<boolean>(true);
  const [imageList, setImageList] = useState<any[]>([]);
  const [aSelOptions, setASelOptions] = useState<any[]>([]);
  const [tableData, setTableData] = useState<any[]>([]); // 存储表格数据
  let { currentClassId, currentSchoolId } = initialState?.currentUser || {};
  const [client, setClient] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [AntModal, showModal, hideModal] = useModal();
  const [templateOptions, setTemplateOptions] = useState<any>(null); // 选中的值
  const [tid, setTid] = useState<any>(null);
  const templateRef = useRef<any>(null);
  const [actItem, setActItem] = useState<any>({});
  // 保留原有的字典区域选项作为备用
  const dictionaryAreaOptions = initialState?.dictionaryList?.filter(
    (f) => f.category === DictionaryCategory.Area,
  );

  // 获取区域列表的函数
  const loadAreaOptions = async (classId?: number, schoolId?: number) => {
    console.log('loadAreaOptions 调用参数:', { classId, schoolId });

    if (classId && schoolId) {
      try {
        console.log('调用 fetchAreaList 接口:', { classId, schoolId });
        const res = await fetchOutList({ schoolId });
        console.log('fetchAreaList 返回结果:', res);

        if (res.data) {
          const options = res.data.map((item: any) => ({
            label: item.areaAlias || item.area,
            value: item.id,
          }));
          setAreaOptions(options);
          let options1 = res.data.map((item: any) => ({
            label: item.areaAlias ? `${item.area}（${item.areaAlias}）` : item.area,
            value: item.id,
          }));
          options1 = options1.filter((item: any) => item.label !== '全部');
          setASelOptions(options1);
        }
      } catch (error) {
        console.error('获取区域列表失败:', error);
        // 如果接口失败，使用字典数据作为备用
        const backupOptions =
          dictionaryAreaOptions?.map((item) => ({
            label: item.label,
            value: item.id,
          })) || [];
        setAreaOptions([{ label: '全部', value: -1 }, ...backupOptions]);
      }
    } else {
      console.log('使用字典数据作为区域选项');
      // 如果没有classId或schoolId，使用字典数据
      const defaultOptions =
        dictionaryAreaOptions?.map((item) => ({
          label: item.label,
          value: item.id,
        })) || [];
      setAreaOptions([{ label: '全部', value: -1 }, ...defaultOptions]);
    }
  };
  // 导出
  const exportMaterial = async (params, type) => {
    setLoading(true);
    let fn = type === 'all' ? exportMaterialInfo : exportMaterialDetail;
    let res = await fn(params);
    if (res.status === 0) {
      message.success('导出成功, 请到下载文件查看（ctrl + J）');
      window.open(res.data);
      hideModal();
      setLoading(false);
      return;
    }
    message.error('导出失败:' + res.message);
  };

  // 导出单个文件
  const exportFile = async () => {
    console.log(initialState?.currentUser);
    let { id } = actItem;
    let params = {
      id,
      url: templateRef.current?.templateId,
      className: initialState?.userClass?.find((item: any) => item.id === currentClassId)?.title,
    };
    exportMaterial(params, 'item');
  };

  useEffect(() => {
    console.log('组件初始化，当前用户信息:', initialState?.currentUser);
    let classId = initialState?.currentUser?.currentClassId;
    let schoolId = initialState?.currentUser?.currentSchoolId;
    fetchUserClasses().then((res) => {
      console.log('获取班级列表结果:', res);
      classOptions.current = [
        { label: '全部', value: 0 },
        ...res.data.map((d: any) => ({ label: d.title, value: d.id })),
      ];
    });

    // 初始化时加载区域选项
    console.log('初始化加载区域选项');
    loadAreaOptions(classId, schoolId);
  }, []);

  const handleDelete = async (record: any) => {
    try {
      const deleteParams = {
        id: record.id,
        classId: record.classId || initialState?.currentUser?.currentClassId,
        sourceType: record.sourceType,
      };
      console.log('删除参数:', deleteParams);
      console.log('记录数据:', record);

      await deleteAreaProduct(deleteParams);
      message.success('删除成功');
      actionRef.current?.reload();
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败，请重试');
    }
  };

  const baseColumn: ProColumns<any, 'text'>[] = [
    {
      title: '操作',
      ellipsis: true,
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: '6em',
      search: false,
      fixed: 'right',
      render: (_, record) => [
        <Button
          size="small"
          type="primary"
          key="edit"
          onClick={() => {
            // window.open(`/ClassAreaProduct/edit/${record?.id}`, '_blank');
            window.open(`/GameManager/material/edit?id=${record.id}&sourceType=${record.sourceType}`, '_blank');
          }}
        >
          详情
        </Button>,
        <Popconfirm
          key="delete"
          title="确认删除"
          description={`确定要删除材料"${record.name}"吗？此操作不可撤销。`}
          okText="是"
          cancelText="否"
          onConfirm={async () => {
            await handleDelete(record);
          }}
        >
          <Button size="small" type="primary" danger>
            删除
          </Button>
        </Popconfirm>,
        <Button
          size="small"
          type="link"
          key="edit"
          onClick={() => {
            console.log(record);
            let list = record.materialDetailTemplate.map((item: any) => {
              return {
                label: item.name,
                value: item.url,
              };
            });
            setActItem(record);
            showModal();
            setTemplateOptions(list);
            setTid(list[0].value);
          }}
        >
          导出
        </Button>,
        // <Button
        //   size="small"
        //   type="link"
        //   key="detail"
        //   href={`/ClassAreaProduct/detail/${record?.id}`}
        //   target="_blank"
        // >
        //   查看详情
        // </Button>,
      ],
    },

    {
      title: '区域',
      width: 100,
      dataIndex: 'areaId',
      valueType: 'select',
      fieldProps: {
        options: areaOptions,
        placeholder: '请选择区域',
      },
      renderText(_, record) {
        // 优先显示 area 字段，如果没有则通过 areaId 匹配
        if (record.area) {
          return record.area;
        }
        const t = areaOptions?.find((f) => f.value === record.areaId);
        return t ? t.label : _;
      },
    },
    {
      title: '材料名称',
      dataIndex: 'name',
      width: 160,
    },
    {
      title: '玩法数量',
      dataIndex: 'playCount',
      width: 80,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'playStatus',
      width: 100,
      valueType: 'select',
      valueEnum: {
        '-1': '未生成',
        '0': '生成中',
        '99': '生成异常',
      },
      search: false, // 隐藏筛选表单中的状态筛选
      render(_, record) {
        if (record.playStatus < 0) {
          return <Tag color="default">未生成</Tag>;
        } else if (record.playStatus === 0) {
          return <Tag color="processing">生成中</Tag>;
        } else if (record.playStatus === 99) {
          return <Tag color="error">生成异常</Tag>;
        }
        return <Tag color="success">生成完成</Tag>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
      search: false,
      sorter: (a, b) => {
        return new Date(a.createTime).getTime() - new Date(b.createTime).getTime();
      },
    },
    {
      title: '预览图',
      dataIndex: 'displayedImage',
      width: 120,
      search: false,
      render(_, record) {
        return record.displayedImage ? (
          <Image width={'auto'} height={40} src={record.displayedImage} />
        ) : null;
      },
    },
  ];

  // 处理保存
  const handleSave = async (values: any) => {
    console.log(aSelOptions.find((f) => f.value === values.areaId));
    let params = {
      area: aSelOptions.find((f) => f.value === values.areaId)?.label,
      schoolId: currentSchoolId,
      selectedImg: imageList[0]?.uri || imageList[0]?.url,
      baseMaterialId: null,
      brand: null,
      combinedMaterialComponentId: null,
      combinedMaterialId: null,
      name: values.bookName || values.taobaoName,
      trashImg: '',
      ...values,
    };
    if (!isToy) params.taobaoLink = '';
    if (isToy && !params.taobaoLink) params.taobaoLink = '';
    console.log(params);
    // 分两种情况 toy 有ai book 正常
    try {
      const response = await saveMaterialInfo(params);
      if (response?.status === 0) {
        message.success('新增成功');
        if (isToy) {
          await generatePlayList({
            combinedId: response.data.combinedId,
          });
        }
        setModalOpen(false);
        // 刷新页面
        actionRef.current?.reload();
        return true;
      } else {
        message.error(response?.message || '操作失败');
        return false;
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('操作失败');
      return false;
    }
  };

  return (
    <PageContainer style={{ background: '#fff' }}>
      <ProTable<any, API.PageParams>
        actionRef={actionRef}
        formRef={formRef}
        rowKey="id"
        scroll={{ y: 500, x: 'max-content' }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 10,
        }}
        expandable={{ showExpandColumn: false }}
        form={{
          initialValues: {
            classId: initialState?.currentUser?.currentClassId || undefined,
            areaId: -1,
          },
        }}
        toolBarRender={() => [
          <Button
            size="small"
            type="link"
            key="detail"
            // href={`/ClassAreaProduct/edit/add`}
            onClick={() => setModalOpen(true)}
            target="_blank"
          >
            <PlusOutlined /> 新建
          </Button>,
          <Button
            size="small"
            type="link"
            key="export"
            target="_blank"
            onClick={async () => {
              let list = [];
              list = tableData?.map((item) => {
                return {
                  combinedId: item.id,
                  sourceType: item.sourceType,
                };
              });
              let params = { fields: [], list };

              exportMaterial(params, 'all');
            }}
          >
            导出清单
          </Button>,
        ]}
        request={(params) => {
          console.log('ProTable request 原始参数:', params);
          const p: any = { ...params };

          // 确保传递 schoolId 参数
          if (!p.schoolId) {
            p.schoolId = initialState?.currentUser?.currentSchoolId;
          }

          // areaId 默认传 -1（全部）
          if (!p.areaId) {
            p.areaId = -1;
          }

          // 处理 classId 参数
          if (!p.classId || p.classId === 0) {
            // 如果没有classId或者是0（全部），使用当前用户的班级ID
            p.classId = initialState?.currentUser?.currentClassId;
          }

          console.log('处理后的参数:', p);
          console.log('当前用户信息:', initialState?.currentUser);

          // 检查必需参数 - 使用更严格的验证
          if (
            !p.classId ||
            !p.areaId ||
            !p.schoolId ||
            p.classId === undefined ||
            p.areaId === undefined ||
            p.schoolId === undefined
          ) {
            console.log('参数验证失败，缺少必需参数:', {
              classId: p.classId,
              areaId: p.areaId,
              schoolId: p.schoolId,
            });
            // 如果缺少必需参数，返回空结果
            return Promise.resolve({
              data: [],
              total: 0,
              success: true,
            });
          }

          // 判断是否有筛选条件（区域或材料名称）
          // const hasFilter = (p.areaId && p.areaId !== -1) || p.name;
          const hasFilter = p.name ? true : false;

          if (hasFilter) {
            // 有筛选条件时使用 queryAreaProductList
            const queryParams = {
              classId: p.classId,
              areaId: p.areaId,
              schoolId: p.schoolId,
              materialName: p.name, // 将name参数转换为materialName
            };
            console.log('调用 queryAreaProductList 接口，参数:', queryParams);
            return queryOutProductList(queryParams)
              .then((res) => {
                console.log('queryAreaProductList 返回结果:', res);
                // 展平嵌套的数据结构
                const flatData: any[] = [];
                const seenIds = new Set(); // 用于去重

                if (res.data && Array.isArray(res.data)) {
                  res.data.forEach((areaItem: any) => {
                    if (
                      areaItem.teacherCombinedMaterialVOList &&
                      Array.isArray(areaItem.teacherCombinedMaterialVOList)
                    ) {
                      areaItem.teacherCombinedMaterialVOList.forEach((material: any) => {
                        // 根据材料ID去重
                        if (!seenIds.has(material.id)) {
                          seenIds.add(material.id);
                          flatData.push(material);
                        }
                      });
                    }
                  });
                }
                setTableData(flatData);
                console.log('展平并去重后的数据:', flatData);
                return {
                  data: flatData,
                  total: flatData.length,
                  success: true,
                };
              })
              .catch((error) => {
                console.error('queryAreaProductList 调用失败:', error);
                throw error;
              });
          } else {
            // 无筛选条件时使用 fetchAreaProductList
            console.log('调用 fetchAreaProductList 接口:', p);
            return getMaterialOutdoorsList(p)
              .then((res) => {
                console.log('fetchAreaProductList 返回结果:', res);
                setTableData(res.data);
                return res;
              })
              .catch((error) => {
                console.error('fetchAreaProductList 调用失败:', error);
                throw error;
              });
          }
        }}
        columns={baseColumn}
      />
      <ModalForm
        title={'新建材料'}
        width="600px"
        form={form}
        open={modalOpen}
        modalProps={{
          maskClosable: false,
          destroyOnClose: true,
        }}
        submitter={{
          searchConfig: {
            submitText: isToy ? 'AI分析' : '提交',
          },
        }}
        onOpenChange={(open) => {
          setModalOpen(open);
          if (!open) {
            setCurrentRow({});
            setImageList([]);
            form.resetFields();
          }
        }}
        onFinish={handleSave}
      >
        <ProFormRadio.Group
          name="sourceType"
          label="材料类型"
          onChange={(e: any) => {
            e.target.value === 'toy' ? setIsToy(true) : setIsToy(false);
          }}
          options={[
            {
              label: '玩具类',
              value: 'toy',
            },
            {
              label: '图书类',
              value: 'book',
            },
          ]}
          // request={() => {}}
          initialValue={'toy'}
        />
        <ProFormSelect
          name="areaId"
          label="选择区域"
          rules={[{ required: true }]}
          //   options={[{ label: '自定义心情', value: 5 }]}
          options={aSelOptions}
          // request={() => {}}
          //   initialValue={5}
        />

        {isToy ? (
          <>
            <ProFormText name="taobaoName" label="材料名称" rules={[{ required: true }]} />
            <ProFormText name="brand" label="材料品牌" />
            <ProFormText name="taobaoLink" label="淘宝链接" />
          </>
        ) : (
          <ProFormText name="bookName" label="书籍名称" rules={[{ required: true }]} />
        )}

        <UploadImageComponent
          key="upload"
          fileName="imageId"
          rules={[{ required: true }]}
          label={isToy ? '材料图片' : '书籍图片'}
          max={1}
          client={client}
          fileList={imageList}
          setClient={setClient}
          setFileList={setImageList}
          accept=".png,.jpg,.jpeg,.gif"
        />
      </ModalForm>
      <AntModal onOk={exportFile} title="选择模版" loading={loading}>
        <TemplateList options={templateOptions} values={tid} ref={templateRef} />
      </AntModal>
    </PageContainer>
  );
};

export default OutdoorsMaterial;
