// import MatrixSelect from '@/components/MatrixSelect';
import AntForms, { FormFields, FormTypes } from '@/components/UseForms';
// import { useCategorySelect } from '@/components/UseForms/categorySelect';
import { useModal } from '@/components/UseModal';
// import { addDictionary, fetchUserClasses } from '@/services/apis';
import {
  getMaterialInfoDetails,
  getMaterialAnalysis,
  getMaterialPlayList,
  getDictList,
  editMaterialInfo,
  editMaterialAnalysis,
  getAIPlayDetail,
  UseAIPlay,
  deletePlay,
  getCoreExperience
} from '@/services/material';
import { fetchAreaList } from '@/services/classAreaProduct';
import { getMatrix, getTarget } from '@/services/class';
// import { DictionaryCategory } from '@/services/constants';
// import { useGetEnumOptionsByKey } from '@/services/enums';
import { DeleteOutlined } from '@ant-design/icons';
import {
  PageContainer,
  ProForm,
  ProFormText,
  ProFormSelect,
  ProFormTextArea,
  ProFormList,
  ProFormGroup,
} from '@ant-design/pro-components';
import { history, useModel, useParams, useLocation } from '@umijs/max';
import { Button, message, Row, List, Rate } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useCallback, useEffect, useRef, useState } from 'react';
export const getLists = async (params: any) => {
  try {
    const res = await getCoreExperience(params);
    return res.data?.map((item) => ({ label: item.title, value: item.id }));
  } catch (e) {
    return [];
  }
};
const getTargetList = async (params: any) => {
  try {
    const res = await getTarget(params);
    return res.data?.map((item) => ({ label: item.title, value: item.id }));
  } catch (e) {
    return [];
  }
};
const MaterialEdit = () => {
  const [form] = useForm();
  // const [matrixForm] = useForm();
  const [modalForm] = useForm();
  // const params = useParams();
  const { initialState } = useModel('@@initialState');
  const { currentClassId, currentSchoolId } = initialState?.currentUser || {};
  const [isEdit, setIsEdit] = useState(false);
  const [AntModal, showModal, hideModal] = useModal();
  const [isLoading, setIsLoading] = useState(false);
  const location = useLocation();
  const query = new URLSearchParams(location.search);
  const [currentPlay, setCurrentPlay] = useState<any>({});

  const [value, setValue] = useState(0); // 确认玩法的评分

  const regionOptions = useRef<any[]>([]);
  const [playList, setPlayList] = useState([]);
  const [allOptions, setAllOptions] = useState<any>({
    productionMethod: [],
    qualitylevel: [],
    safetyLevel: [],
    gameRating: [],
  });

  const onSave = useCallback(async () => {
    // 获取form表单数据中的id
    let {
      name,
      materialType,
      qualityLevel,
      safetyLevel,
      weightGram,
      productionMethod,
      areaId,
      headers,
    } = form.getFieldsValue();
    // 材料基本信息收集
    let basicInfo = {
      id: form.getFieldValue('id'),
      name,
      materialtype: materialType,
      qualitylevel: qualityLevel,
      safetylevel: safetyLevel,
      weightgram: weightGram,
      selectedImg: headers.map((item: any) => item.uri).join(','),
      productionmethod: productionMethod,
      area: regionOptions.current.find((item: any) => item.value === areaId)?.label,
      areaId: areaId,
      classId: currentClassId,
      materialAnalysisId: Number(form.getFieldValue('materialAnalysisId')),
      combinedId: query.get('id'),
      taobaoId: form.getFieldValue('taobaoId'), // 淘宝ID
    };
    // 材料分析信息收集
    let analysisInfo = {
      id: Number(form.getFieldValue('analId')),
      materialSummary: form.getFieldValue('materialSummary'),
      purpose: form.getFieldValue('purpose'),
      productionMethod: form.getFieldValue('productionMethod'),
      minAge: Number(form.getFieldValue('minAge')),
      maxAge: Number(form.getFieldValue('maxAge')),
      suitablePeopleMin: Number(form.getFieldValue('suitablePeopleMin')) || 0,
      suitablePeopleMax: Number(form.getFieldValue('suitablePeopleMax')) || 0,
    };

    let mRes = await editMaterialInfo(basicInfo);
    let aRes = await editMaterialAnalysis(analysisInfo);
    if (mRes.status === 0 && aRes.status === 0) {
      message.success('修改成功');
      // history.push('/material/list');
    }
  }, [form]);

  const onAddCategory = useCallback(() => {
    // const fields = modalForm.getFieldsValue();
    // if (fields.value) {
    //   addDictionary({
    //     value: fields.value,
    //     category: modalName,
    //     sort: 0,
    //   }).then((r) => {
    //     const objVal = { ...r, label: r.data.value, value: r.data.id };
    //     const fn: any =
    //       modalName === DictionaryCategory.Brand
    //         ? unshiftBrandSelect
    //         : modalName === DictionaryCategory.Tag
    //         ? unshiftTagSelect
    //         : unshiftMaterialSelect;
    //     fn([objVal]);
    //     switch (modalName) {
    //       case DictionaryCategory.Brand:
    //         form.setFieldValue('brandId', objVal.value);
    //         break;
    //       case DictionaryCategory.Material:
    //         form.setFieldValue('material', objVal.value);
    //       default:
    //       case DictionaryCategory.Tag:
    //         form.setFieldValue(
    //           'tagIds',
    //           Array.from(new Set([objVal.value, ...form.getFieldsValue().tagIds])),
    //         );
    //         break;
    //     }
    //     message.success(r.message || '保存成功');
    //     hideModal();
    //   });
    // } else {
    //   message.warning('请输入名称');
    // }
  }, []);

  /***
   * @param n  1：采用 -1：不采用
   * @param type ai：AI玩法推荐 delete：删除玩法
   * **/
  const onUseOrNot = async (n: number | string, type: string = 'ai') => {
    let fun = UseAIPlay;

    const { dataType, combinedId, id, schoolId, classId } = currentPlay;
    let p = {
      playId: id,
      isAdopt: n,
      dataType,
      combinedId,
      schoolId,
      classId,
    };
    if (type === 'delete') {
      fun = deletePlay;
      delete p.isAdopt;
    }

    const r = await fun(p);
    if (r.status === 0) {
      message.success('操作成功');
      hideModal();
      // 刷新表单
      getMaterialPlayList({ combinedId: Number(query.get('id')) }).then((res) =>
        setPlayList(res.data),
      );
      // history.push('/play/list');
    }
    console.log('采用参数：', p);
  };

  const [childFormData] = useState({});

  const optionsFn = async (code: any, key: any) => {
    const r = await getDictList({ code });
    let list = r.data.map((item: any) => {
      return {
        label: item.dictItemName,
        value: item.dictItemCode,
      };
    });
    setAllOptions((prev: any) => ({
      ...prev,
      [key]: list,
    }));
  };

  const childForms: FormFields = [
    {
      label: '区域',
      name: 'areaId',
      formType: FormTypes.select,
      options: regionOptions.current,
      disabled: isEdit,
    },
    {
      label: '名称',
      name: 'name',
      formType: FormTypes.input,
      rules: [{ required: true }],
      placeholder: '请输入名称',
      disabled: isEdit,
    },
    {
      label: '制作方式',
      name: 'productionMethod',
      formType: FormTypes.select,
      options: allOptions.productionMethod,
      placeholder: '请选择制作方式',
      disabled: isEdit,
    },
    {
      label: '材质',
      name: 'materialType',
      formType: FormTypes.input,
      placeholder: '请输入材质',
      disabled: isEdit,
    },
    {
      label: '品质',
      name: 'qualityLevel',
      formType: FormTypes.select,
      options: allOptions.qualitylevel,
      placeholder: '请选择品质',
      disabled: isEdit,
    },
    {
      label: '安全性',
      name: 'safetyLevel',
      formType: FormTypes.select,
      options: allOptions.safetyLevel,
      placeholder: '请选择安全性',
      disabled: isEdit,
    },
    {
      label: '重量（克）',
      name: 'weightGram',
      formType: FormTypes.input,
      placeholder: '请输入重量',
      disabled: isEdit,
    },
    {
      formType: FormTypes.file,
      label: '图片管理',
      multiple: true,
      name: 'headers',
      fileKey: 'headers',
      disabled: isEdit,
    },
  ];
  // 材料分析
  const matForms: FormFields = [
    {
      label: '材料概述',
      name: 'materialSummary',
      placeholder: `请输入材料概述，可以参考如下内容：材料由什么部分构成（这份材料包含什么部分，比如“1. 彩色积木块20块；2. 名字卡片；3. 筛子；4. 笔；5. 记录单”）？幼儿会怎么操作这份材料？材料和操作单是否易于幼儿理解和操作？这份材料有什么难点？玩的时候可能会出现什么情况？这份材料是否可以达成其他区域或领域的目标？`,
      formType: FormTypes.textarea,
      style: { height: '155px' },
      disabled: isEdit,
    },

    {
      label: '投放目的',
      name: 'purpose',
      placeholder:
        '请输入投放目的，可以参考如下内容：为什么要投放这份材料？材料的目标是什么？是否是幼儿的需求？幼儿是否有兴趣？是否符合幼儿生活经验？是否吸引幼儿？',
      formType: FormTypes.textarea,
      style: { height: '155px' },
      disabled: isEdit,
    },
    [
      {
        label: '最小年龄',
        name: 'minAge',
        formType: FormTypes.input,
        type: 'number',
        disabled: isEdit,
      },
      {
        label: '最大年龄',
        name: 'maxAge',
        formType: FormTypes.input,
        type: 'number',
        disabled: isEdit,
      },
    ],
    [
      {
        label: '最小人数',
        name: 'suitablePeopleMin',
        formType: FormTypes.input,
        type: 'number',
        disabled: isEdit,
      },
      {
        label: '最大人数',
        name: 'suitablePeopleMax',
        formType: FormTypes.input,
        type: 'number',
        disabled: isEdit,
      },
    ],
  ];

  // 请求区域数据
  const getRegionData = async () => {
    const res = await fetchAreaList({
      classId: Number(currentClassId),
      schoolId: Number(currentSchoolId),
    });
    if (res.status === 0) {
      let list = res.data.map((item: any) => {
        return {
          label: item.area,
          value: item.id,
        };
      });

      regionOptions.current = list.filter((item: any) => item.label !== '全部');
      return;
    }
    message.error(res?.message || '获取区域数据失败');
  };

  const filterData = (data: any[], i: number) => data.filter((item: any) => item.playAdopt === i);

  const [matrix, setMatrix] = useState([]);

  // 获取领域
  const fetchList = async () => {
    const list = await getLists({ parentId: 0, depth: 1 });
    setMatrix(list);
  };

  useEffect(() => {
    console.log('是否是编辑状态', query.get('isEdit'));

    if (query.get('isEdit')) setIsEdit(true);
    getRegionData();
    let id = Number(query.get('id'));
    let sourceType = query.get('sourceType');
    let isEdit = query.get('isEdit');
    console.log(id, sourceType, isEdit, 'id');
    getMaterialInfoDetails({ combinedId: id, sourceType }).then((res) => {
      form.setFieldsValue(res.data);
      let imgList = res.data.combinedSelectedImg.split(',');
      form.setFieldsValue({
        headers: imgList.map((item: any) => {
          return { uri: item, hash: item, filename: item };
        }),
      });
      console.log(imgList, 'imgList');
    });
    getMaterialAnalysis({ combinedId: id }).then((res) => {
      form.setFieldsValue(res.data);
      form.setFieldValue('analId', res.data.id);
    });
    getMaterialPlayList({ combinedId: id }).then((res) => setPlayList(res.data));
    console.log();
    optionsFn('productionMethod', 'productionMethod');
    setTimeout(() => {
      optionsFn('materialQuality', 'qualitylevel');
    }, 100);
    setTimeout(() => {
      optionsFn('materialSafety', 'safetyLevel');
    }, 200);
    getDictList({ code: 'childInterest' }).then((res) => {
      setAllOptions((prev: any) => ({
        ...prev,
        gameRating: res.data.map((item: any) => {
          return item.dictItemName;
        }),
      }));
    });
    fetchList();
  }, []);

  return (
    <PageContainer style={{ background: '#fff' }}>
      <h3>材料基本信息</h3>
      <Row>
        <AntForms
          style={{ width: '100%' }}
          form={form}
          formProps={{
            disabled: true,
            initialValues: childFormData,
          }}
          forms={childForms}
        />
      </Row>

      <h3>材料分析</h3>
      <Row>
        <AntForms
          form={form}
          formProps={{
            disabled: true,
            initialValues: childFormData,
          }}
          forms={matForms}
        />
      </Row>

      <h3
        style={{
          padding: '12px 0',
        }}
      >
        已采纳玩法
      </h3>
      <List
        bordered
        dataSource={filterData(playList, 1)}
        itemLayout="vertical"
        renderItem={(item) => (
          <List.Item
            style={{ cursor: 'pointer' }}
            key={item?.id}
            onClick={() => {
              showModal();
              setCurrentPlay(item);
            }}
          >
            <div style={{ fontWeight: 'bold' }}>{item?.playName}</div>
            <div style={{ color: '#999' }}>{item?.playIntro}</div>
          </List.Item>
        )}
      />
      <h3 style={{ padding: '12px 0' }}>AI推荐玩法</h3>
      <List
        bordered
        dataSource={filterData(playList, 0)}
        itemLayout="vertical"
        renderItem={(item) => (
          <List.Item
            style={{ cursor: 'pointer' }}
            key={item?.id}
            onClick={() => {
              showModal();
              setCurrentPlay(item);
            }}
          >
            <div style={{ fontWeight: 'bold' }}>{item?.playName}</div>
            <div style={{ color: '#999' }}>{item?.playIntro}</div>
          </List.Item>
        )}
      />
      <Button
        loading={isLoading}
        style={{
          position: 'fixed',
          right: 60,
          top: 75,
        }}
        onClick={onSave}
        type="primary"
      >
        保存
      </Button>

      <AntModal
        width="80%"
        maskClosable={false}
        style={{ top: 20 }}
        onOk={onAddCategory}
        footer={
          currentPlay?.playAdopt === 1
            ? [
                <Button key="back">取消</Button>,
                <Button
                  key="submit"
                  type="primary"
                  onClick={() => {
                    console.log(modalForm.getFieldsValue());
                  }}
                >
                  保存
                </Button>,
              ]
            : null
        }
      >
        <ProForm
          disabled={currentPlay?.playAdopt !== 1}
          submitter={{
            render: (props, doms) => {
              console.log(currentPlay);
              return (
                <>
                  {currentPlay?.playAdopt === 0
                    ? [
                        <Button
                          htmlType="button"
                          type="primary"
                          onClick={() => onUseOrNot(1)}
                          key="ok"
                          style={{ marginRight: 12 }}
                          disabled={currentPlay?.playAdopt === 1}
                        >
                          采用
                        </Button>,
                        <Button
                          htmlType="button"
                          danger
                          type="primary"
                          onClick={() => onUseOrNot(-1)}
                          key="no"
                          disabled={currentPlay?.playAdopt === 1}
                        >
                          不采用
                        </Button>,
                      ]
                    : [
                        <span key="select" style={{ marginRight: 12 }}>
                          兴趣评定：
                          <Rate
                            tooltips={allOptions.gameRating}
                            onChange={setValue}
                            value={value}
                          />
                          {value ? (
                            <span className="ant-rate-text">
                              {allOptions.gameRating[value - 1]}
                            </span>
                          ) : (
                            ''
                          )}
                        </span>,
                        <Button
                          htmlType="button"
                          danger
                          type="primary"
                          onClick={() => onUseOrNot(0, 'delete')}
                          key="no"
                        >
                          删除
                        </Button>,
                      ]}
                </>
              );
            },
          }}
          form={modalForm}
          request={async () => {
            const { dataType, id } = currentPlay;
            let v = { dataType, playId: Number(id) };
            let res = await getAIPlayDetail(v);
            return res.data;
          }}
        >
          <ProFormText
            required
            rules={[{ required: true }]}
            placeholder={`请输入玩法名称`}
            name="playName"
            label={'玩法名称'}
            // disabled={true}
          />
          <ProForm.Group>
            <ProFormText
              required
              rules={[{ required: true }]}
              placeholder={`请输入最小年龄`}
              name="ageMin"
              // initialValue={}
              label="最小年龄"
            />
            <ProFormText
              required
              rules={[{ required: true }]}
              placeholder={`请输入最大年龄`}
              name="ageMax"
              // initialValue={}
              label="最大年龄"
            />
            <ProFormText
              required
              rules={[{ required: true }]}
              placeholder={`请输入最小人数`}
              name="suitablePeopleMin"
              label="最小人数"
            />
            <ProFormText
              required
              rules={[{ required: true }]}
              placeholder={`请输入最大人数`}
              name="suitablePeopleMax"
              // initialValue={}
              label="最大人数"
            />
          </ProForm.Group>
          <ProFormSelect
            required
            rules={[{ required: true }]}
            placeholder={`请输入投放区域`}
            name="area"
            label="投放区域"
            options={regionOptions.current}
          />
          <ProFormText
            required
            rules={[{ required: true }]}
            placeholder={`请输入结构化`}
            name="structured"
            label="结构化"
          />
          <ProFormTextArea
            required
            rules={[{ required: true }]}
            placeholder={`请输入玩法介绍`}
            name="playIntro"
            label="玩法介绍"
            // styles={{ minHeight: "300px" }}
          />
          {/* { currentPlay?.coreExperience } */}
          <ProForm.Item label="核心经验" name="coreExperience">
            <List
              bordered
              dataSource={JSON.parse(currentPlay?.coreExperience || '[]')}
              itemLayout="horizontal"
              renderItem={(item, index) => (
                <List.Item
                  onClick={() => {
                    // showModal();
                    // setCurrentPlay(item);
                  }}
                  actions={[
                    <Button key={index} danger icon={<DeleteOutlined />} style={{ border: '0' }} />,
                  ]}
                >
                  <span style={{width: 'fit-content'}}>经验{index + 1}：</span>
                  <div style={{ margin: '0 auto 0 12px' }}>{item}</div>
                </List.Item>
              )}
            />
            <ProFormList
              name="matrices"
              copyIconProps={false}
              rules={[{ required: true, message: '请选择核心经验'}]}
              alwaysShowItemLabel
              creatorButtonProps={{
                creatorButtonText: '新增核心经验',
              }}
              style={{ marginTop: 24 }}
            >
              <ProFormGroup key="matrices">
                <ProFormSelect
                  rules={[
                    {
                      required: true,
                      message: '请选择领域',
                    },
                  ]}
                  width={'sm'}
                  name="matrix1Id"
                  label="领域"
                  placeholder="请选择领域"
                  showSearch

                  options={matrix}
                />
                <ProFormSelect
                  dependencies={['matrix1Id']}
                  width={'sm'}
                  rules={[
                    {
                      required: true,
                      message: '请选择维度',
                    },
                  ]}
                  placeholder={'请选择维度'}
                  showSearch
                  label="维度"
                  name="matrix2Id"
                  request={async (params) => {
                    if (!params?.matrix1Id) {
                      return [];
                    }
                    return await getLists({ parentId: params.matrix1Id, depth: 2 });
                  }}
                />
                <ProFormSelect
                  dependencies={['matrix2Id']}
                  showSearch
                  rules={[
                    {
                      required: true,
                      message: '请选择子维度',
                    },
                  ]}
                  label="子维度"
                  width={'sm'}
                  placeholder="请选择子维度"
                  name="matrix3Id"
                  request={async (params) => {
                    if (!params?.matrix2Id) {
                      return [];
                    }
                    return await getLists({ parentId: params.matrix2Id, depth: 3 });
                  }}
                />
                <ProFormSelect
                  showSearch
                  dependencies={['matrix3Id']}
                  rules={[
                    {
                      required: true,
                      message: '请选择指标',
                    },
                  ]}
                  width={'lg'}
                  placeholder="请选择指标"
                  label="请选择指标"
                  name="targetId"
                  request={async (params) => {
                    if (!params?.matrix3Id) {
                      return [];
                    }
                    const res = await getLists({ parentId: params.matrix3Id, depth: 4 });

                    return res;
                  }}
                />
              </ProFormGroup>
            </ProFormList>
          </ProForm.Item>
          <ProFormTextArea
            required
            rules={[{ required: true }]}
            placeholder={`请输入玩法名称`}
            name="teacherSupport"
            label="教师支持"
          />
        </ProForm>
      </AntModal>
    </PageContainer>
  );
};

export default MaterialEdit;
