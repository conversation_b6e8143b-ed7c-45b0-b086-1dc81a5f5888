import { getTemplateList } from '@/services/apis';
import { useGetEnumsByKey } from '@/services/enums';
import { useModel } from '@umijs/max';
import { Select } from 'antd';

import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

/** 状态 */
export enum StateStatus {
  // 停用
  Off = 0,
  // 启用
  On = 1,
}

const TemplateList = forwardRef((props: any, ref: any) => {
  const { options, values } = props;
  const [templateId, setTemplateId] = useState();
  useImperativeHandle(ref, () => ({
    templateId,
  }));

  useEffect(() => {
    if (values) {
      setTemplateId(values);
    }
  }, []);

  return (
    <div>
      <Select options={options} value={templateId} onChange={setTemplateId} />
    </div>
  );
});

export default TemplateList;
