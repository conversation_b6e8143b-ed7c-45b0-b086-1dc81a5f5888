import { getFeedbackDetail } from '@/services/feedback';
import { PageContainer } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Image } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useState } from 'react';
import { FormItem } from '../Class/ActivityDetail';

const ChildDetail: React.FC = () => {
  const params = useParams();
  const { id } = params || {};
  const [detail, setDetail] = useState({});
  const [form] = useForm();

  useEffect(() => {
    if (id) {
      // 获取详情
      getFeedbackDetail({ id }).then((res) => {
        setDetail(res.data || {});
        form.setFieldsValue({
          ...res.data,
        });
      });
    }
  }, []);

  return (
    <PageContainer style={{ background: '#fff', minHeight: '400px' }}>
      <FormItem
        data={detail}
        lists={[
          {
            dataIndex: 'title',
            label: '标题',
            span: 4,
          },
          {
            dataIndex: 'content',
            label: '内容',
            span: 4,
          },
          {
            dataIndex: 'link',
            label: '链接',
            span: 4,
          },
          {
            dataIndex: 'images',
            label: '图片',
            span: 4,
            type: 'file'
          },
        ]}
      />
    </PageContainer>
  );
};

export default ChildDetail;
