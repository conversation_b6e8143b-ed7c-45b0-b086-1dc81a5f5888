/* eslint-disable guard-for-in */

import { getFeedbackList } from '@/services/api';
import { useGetEnumsByKey } from '@/services/enums';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button } from 'antd';
import React, { useRef } from 'react';

const Feedback: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [FeedbackStateEnum] = useGetEnumsByKey(['FeedbackStateEnum']);
  // 新增配置项
  const options = Object.keys(FeedbackStateEnum || {}).reduce((total, next) => {
    total[next] = {
      value: next,
      label: FeedbackStateEnum[next]?.adminDesc,
      text: FeedbackStateEnum[next]?.adminDesc,
    };
    return total;
  }, {});

  const columns: ProColumns<API.RuleListItem>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '标题',
      dataIndex: 'title',
      valueType: 'text',
      width: 80,
    },
    {
      title: '内容',
      dataIndex: 'content',
      valueType: 'text',
      width: 320,
      search: false,
    },
    {
      title: '链接',
      dataIndex: 'link',
      valueType: 'text',
      width: 320,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'state',
      valueType: 'select',
      valueEnum: options,
      width: 80,
      search: false,
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        toolBarRender={() => [
          <Button size="small" type="link" key="detail" href={`/feedback/add`} target="_blank">
            <PlusOutlined /> 新建
          </Button>,
        ]}
        request={getFeedbackList}
        columns={columns}
      />
    </PageContainer>
  );
};

export default Feedback;
