import AntForms, { FormFields, FormTypes } from '@/components/UseForms';
import { addFeedback, getFeedbackDetail, updateFeedback } from '@/services/feedback';
import { filterEmptyObject } from '@/services/utils';
import { PageContainer } from '@ant-design/pro-components';
import { history, useParams } from '@umijs/max';
import { Button, message, Row } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useCallback, useEffect, useState } from 'react';

const ChildDetail: React.FC = () => {
  const params = useParams();
  const { id } = params || {};
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, setDetail] = useState({});
  const [form] = useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (id) {
      // 获取详情
      getFeedbackDetail({ id }).then((res) => {
        setDetail(res.data || {});
        form.setFieldsValue({
          ...res.data,
        });
      });
    }
  }, []);

  const childForms: FormFields = [
    {
      label: '标题',
      name: 'title',
      formType: FormTypes.input,
      style: { width: '50%' },
      rules: [
        {
          message: '请输入反馈标题',
          required: true,
        },
      ],
    },
    {
      label: '反馈内容',
      name: 'content',
      style: { width: '50%' },
      rules: [
        {
          message: '请输入反馈内容',
          required: true,
        },
      ],
      formType: FormTypes.textarea,
    },
    {
      label: '链接',
      name: 'link',
      formType: FormTypes.input,
      style: { width: '50%' },
      rules: [
        {
          message: '请输入反馈链接',
          required: true,
        },
      ],
    },
    {
      label: '照片',
      name: 'imageIds',
      fileKey: 'imageIds',
      formType: FormTypes.file,
    },
  ];

  const onUpdate = useCallback(
    (values: any, type?: string) => {
      const fn = id ? updateFeedback : addFeedback;
      setLoading(true);
      const { title, content, imageIds, link } = values;
      fn(
        filterEmptyObject({
          title,
          content,
          imageIds: imageIds?.map((item) => item.id),
          id,
          link,
          note: navigator.userAgent
        }),
      )
        .then((r) => {
          if (r.status === 0) {
            message.success(id ? '更新成功' : '添加成功');
            if (r?.data?.id) {
              if(type) {
                setTimeout(() => {
                  location.reload();
                }, 1000)
              } else {
                history.replace('/feedback/detail/' + r.data.id);
              }
            }
            
          }
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [id],
  );

  return (
    <PageContainer style={{ background: '#fff', height: '100%' }}>
      <AntForms
        form={form}
        formProps={{
          onFinish: onUpdate,
        }}
        forms={childForms}
      >
        <Row justify="end">
          <Button loading={loading} style={{ marginRight: 16 }} htmlType="submit" type="primary">
            确定
          </Button>
           <Button loading={loading} style={{ marginRight: 16 }} onClick={() => {
              const value = form.getFieldsValue();
              onUpdate(value, 'continue');
           }} type="primary">
            保存并继续
          </Button>
        </Row>
      </AntForms>
    </PageContainer>
  );
};

export default ChildDetail;
