// 草稿箱
import { addSubject, getJavaClassList, updateSubject } from '@/services/api';
import { ActionType, PageContainer, ProList } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, message } from 'antd';
import { useEffect, useRef, useState } from 'react';

import { DictionaryCategory } from '@/services/constants';
import AddModal from './AddModal';
import './index.less';
import { arrayToObject, fetchCategory, getTerm } from './util';
import { useModel } from '@umijs/max';
const getClassText = (item: any) => {
  if (item.schoolClass && item.schoolClass.title) {
    return item.schoolClass.title;
  }
  return '-';
};

const Draft = () => {
  const [category, setCategory] = useState<{ value: string; label: string }[]>([]);
  const [status, setStatus] = useState('loading');
  const [terms, setTerms] = useState<{ value: string | number; label: string }[]>([]);
  const actionRef = useRef<ActionType>();
  const { initialState } = useModel('@@initialState');
  let { currentSchoolId, currentClassId } = initialState?.currentUser || {};

  useEffect(() => {
    Promise.all([
      fetchCategory(DictionaryCategory.Category),
      fetchCategory(DictionaryCategory.Tag),
      getTerm(),
    ]).then((res) => {
      setStatus('success');
    });
  }, []);

  const onSubmitData = async (data: Record<string, any>) => {
    try {
      let res;
      if (data.id) {
        res = await updateSubject({
          ...data,
        });
      } else {
        res = await addSubject({
          ...data,
        });
      }

      if (res?.status === 0) {
        message.success('更新成功');
        if (actionRef.current) {
          actionRef.current.reload();
        }
        return true;
      } else {
        message.success('更新失败');
      }
    } catch (error: any) {
      message.error(error?.message || '更新失败，请重试');
    }
  };

  // const handleSwitchChange = async (record: Record<string, any>, checked: boolean) => {
  //   try {
  //     const res = await updateSubjectState({
  //       id: record?.id,
  //       state: checked ? 1 : 0,
  //     });
  //     if (res?.status === 0) {
  //       message.success('更新成功');
  //       if (actionRef.current) {
  //         actionRef.current.reload();
  //       }
  //     } else {
  //       message.success('更新失败');
  //     }
  //   } catch (error) {
  //     message.error(error?.message || '更新失败');
  //   }
  // };


  if (status === 'loading') return null;

  return (
    <PageContainer style={{ background: '#fff' }}>
      {/* <div style={{ marginBottom: 15, fontWeight: 600, fontSize: 16 }}>课程资源库</div> */}
      <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 15 }}>
        <AddModal
          key={'add'}
          onSubmit={(data) =>
            onSubmitData({
              ...data,
            })
          }
        />
      </div>
      {/* <div style={{ marginBottom: 15 }}>
        <span style={{ display: 'inline-block', width: 80 }}>课程类别：</span>
        {[{ label: '全部', value: 'all' }, ...(category || [])]?.map((item) => {
          const isActive = activeCategory && activeCategory === item.value;
          return (
            <Tag
              {...(isActive && {
                color: '#55acee',
              })}
              style={{
                padding: '5px 10px',
                cursor: 'pointer',
              }}
              key={item.value}
              onClick={() => {
                setActiveCategory(item.value);
                if (actionRef.current) {
                  actionRef.current.reload();
                }
              }}
            >
              {item.label}
            </Tag>
          );
        })}
      </div> */}
      {/* <div style={{ marginBottom: 15 }}>
        <span style={{ display: 'inline-block', width: 80 }}>学期：</span>
        {[{ label: '全部', value: 'all' }, ...(terms || [])]?.map((item) => {
          const isActive = activeterm && activeterm === item.value;
          return (
            <Tag
              {...(isActive && {
                color: '#3b5999',
              })}
              style={{
                marginBottom: 10,
                padding: '5px 10px',
                cursor: 'pointer',
              }}
              key={item.value}
              onClick={() => {
                setActiveTerm(item.value);
                if (actionRef.current) {
                  actionRef.current.reload();
                }
              }}
            >
              {item.label}
            </Tag>
          );
        })}
      </div> */}
      <ProList<any>
        actionRef={actionRef}
        search={{
          filterType: 'light',
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: false,
        }}
        className="classList"
        showActions="hover"
        rowSelection={false}
        grid={{ gutter: 16, column: 4 }}
        onItem={(record: any) => {
          console.log('%c Line:162 🌰 record', 'color:#3f7cff', record);
          return {
            // onMouseEnter: () => {
            //   console.log(record);
            // },
            onClick: () => {
              console.log(record);
              history.push(`/class/detail/${record?.id}?gradeId=${record.gradeId}`);
            },
          };
        }}
        request={async (params) => {
          let res = await getJavaClassList({
            currentPage: params.current,
            pageSize: params.pageSize,
            pageModel: {
              classId: Number(currentClassId),
              schoolId: Number(currentSchoolId),
              statusList: [2, 4],
            },
          });
          return { ...res, total: res?.metadata?.count };
        }}
        metas={{
          title: {
            dataIndex: 'title',
            search: false,
            title: '标题',
            render: (text) => {
              return `标题： ${text}`;
            },
          },
          content: {
            search: false,
            render: (_, item) => {
              return (
                <div
                  style={{
                    width: '100%',
                  }}
                >
                  {/* <div>{arrayToObject(category)[item.categoryId]?.label}</div> */}
                  <div>创作人：{item.creator || '-'}</div>
                  <div>班级： {getClassText(item)}</div>
                  <div>学期：{arrayToObject(terms)?.[item.term]?.label || '-'}</div>
                  <div>实际实施月份： {item.effect || '-'}</div>
                  <div>课程类别：{arrayToObject(category)[item.categoryId]?.label}</div>
                  <div>
                    课程类别：{item.state === 4 ? <span style={{ color: 'red' }}>草稿</span> : ' '}
                  </div>
                </div>
              );
            },
          },
          actions: {
            cardActionProps: 'actions',
            search: false,
            render: (_, item) => {
              return [
                <AddModal
                  key={'add'}
                  id={item.id}
                  data={{
                    ...item,
                    startTimer: (item.startAt && item.endAt && [item.startAt, item.endAt]) || [],
                  }}
                  onSubmit={(data) =>
                    onSubmitData({
                      ...data,
                      id: item.id,
                    })
                  }
                />,
                <Button
                  size="small"
                  type="link"
                  key="detail"
                  href={`/class/detail/${item?.id}?gradeId=${item.gradeId}`}
                  target="_blank"
                >
                  详情
                </Button>,
              ];
            },
          },
        }}
      />
      {/* <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        // search={false}
        toolBarRender={() => [<AddModal onSubmit={(data) => onSubmitData(data)} />]}
        request={getClassList}
        columns={columns}
        scroll={{ x: 1500 }}
      /> */}
    </PageContainer>
  );
};

export default Draft;
