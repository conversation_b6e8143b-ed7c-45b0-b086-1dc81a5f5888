import { addStage, delStage, getStateList, updateStage, updateStageSort } from '@/services/api';
import { DragSortTable } from '@ant-design/pro-components';

import { useParams } from '@umijs/max';
import { Button, message, Modal } from 'antd';
import { useEffect, useState } from 'react';
import AddModal from './AddStage';

const Activity = (props: { isAIOK: boolean }) => {
  const { isAIOK } = props;
  // const [terms, setTerms] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const params = useParams();
  const id = params.id;
  const getList = async () => {
    try {
      const list = await getStateList({
        subjectId: id,
      });
      setDataSource(list?.data);
    } catch (e) {
      setDataSource([]);
    }
  };

  useEffect(() => {
    getList();
  }, []);

  const onSubmitData = async (data) => {
    try {
      let res;
      if (data.id) {
        res = await updateStage({
          ...data,
        });
      } else {
        res = await addStage({
          ...data,
          subjectId: id,
        });
      }

      if (res?.status === 0) {
        message.success('更新成功');
        getList();
      } else {
        message.success('更新失败');
      }
    } catch (error) {
      message.error(error?.message || '更新失败，请重试');
    }
  };

  const onDeleteState = async (data) => {
    try {
      const res = await delStage({
        id: data.id,
      });

      if (res?.status === 0) {
        message.success('删除成功');
        getList();
      } else {
        message.success('删除失败');
      }
    } catch (error) {
      message.error(error?.message || '删除失败，请重试');
    }
  };
  const columns = [
    {
      title: '序号',
      dataIndex: 'id',
      valueType: 'text',
      width: 40,
      search: false,
      className: 'drag-visible',
    },
    {
      title: '阶段名称',
      dataIndex: 'title',
      valueType: 'text',
      width: 200,
      search: false,
    },
    {
      title: '操作',
      ellipsis: true,
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: '10em',
      search: false,
      render: (_, record) => [
        <AddModal
          key={'add'}
          id={record.id}
          data={record}
          isAIOK={isAIOK}
          onSubmit={(data) =>
            onSubmitData({
              ...data,
              id: record.id,
            })
          }
        />,
        <Button
          size="small"
          key={'del'}
          disabled={isAIOK}
          onClick={() => {
            Modal.confirm({
              title: '是否确认删除',
              onOk() {
                onDeleteState(record);
              },
            });
          }}
        >
          删除
        </Button>,
      ],
    },
  ];

  const handleDragSortEnd = async (beforeIndex: number, afterIndex: number, newDataSource: any) => {
    console.log('排序后的数据', newDataSource, beforeIndex, afterIndex);

    try {
      const res = await updateStageSort({
        subjectId: id,
        ids: newDataSource?.map((item: any) => item.id),
      });
      if (res?.status === 0) {
        message.success('修改列表排序成功');
        getList();
      } else {
        message.success('修改列表排序失败');
      }
    } catch (error) {
      message.error(error?.message || '修改失败，请重试');
    }
    // getList()
    // setDataSource(newDataSource);

    // message.success('修改列表排序成功');
  };

  // TODO: 提交后清空输入框
  return (
    // <PageContainer>
    <DragSortTable<API.RuleListItem, API.PageParams>
      search={false}
      pagination={false}
      rowKey="id"
      columns={columns}
      toolBarRender={() => <AddModal isAIOK={isAIOK} onSubmit={(data) => onSubmitData(data)} />}
      dataSource={dataSource}
      dragSortKey="id"
      onDragSortEnd={handleDragSortEnd}
    />
    // </PageContainer>
  );
};

export default Activity;
