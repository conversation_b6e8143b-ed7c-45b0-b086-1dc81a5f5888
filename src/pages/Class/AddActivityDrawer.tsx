/* eslint-disable guard-for-in */
import { addActivity, fetchSubjectDetail, getMatrix, getTarget } from '@/services/api';

// import { fetchClient } from '@/services/fileUpload';
// import { checkPositiveInteger, checkPrice, checkWeight, formatOption } from '@/services/utils';
import {
  DrawerForm,
  ProFormDigit,
  ProFormGroup,
  ProFormList,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';

import { Button, Card, Col, Form, Row, message } from 'antd';
import { useState } from 'react';

const getTargetList = async (params) => {
  try {
    const res = await getTarget(params);

    return res.data?.map((item) => ({ label: item.title, value: item.id }));
  } catch (e) {
    return [];
  }
};

export const getLists = async (params: { pid: number }) => {
  try {
    const res = await getMatrix(params);
    return res.data?.map((item) => ({ label: item.title, value: item.id }));
  } catch (e) {
    return [];
  }
};

const AddActivityDrawer = (props: {
  subjectStageId: string;
  onRefresh: () => void;
  id?: string;
  data?: Record<string, any>;
  selects: {
    plannedOrGeneration: Record<string, any>[];
    organization: Record<string, any>[];
  };
  isAIOK?: boolean;
}) => {
  const params = useParams();
  const subjectId = params.id;
  const searchParams = new URLSearchParams(location.search);
  const gradeId = searchParams.get('gradeId');
  const { subjectStageId, onRefresh, data: formInitData, selects, isAIOK } = props || {};

  const [form] = Form.useForm();

  const [matrix, setMatrix] = useState([]);

  // 获取领域
  const fetchList = async () => {
    const list = await getLists({ pid: 0, current: 1, pageSize: 1000 });
    setMatrix(list);
  };

  const fetchDetail = async () => {
    try {
      const res = await fetchSubjectDetail(subjectId);
      return res.data;
    } catch (e) {}
  };

  const handleFinish = async (value: Record<string, any>) => {
    const isValid = await form.validateFields();

    if (isValid) {
      // 表单校验通过后的处理逻辑
      try {
        const res = await addActivity({ ...value, subjectStageId, subjectId });
        if (res?.status === 0) {
          message.success('新建成功');
          onRefresh();
          return true;
        }
      } catch (error) {
        console.log(error);
        message.error(error?.message || '新建失败，请重试');
      }
    }
  };

  const onFinishFailed = () => {
    message.error('表单校验失败，请检查输入');
  };

  return (
    <>
      <DrawerForm
        style={{
          margin: 'auto',
          marginTop: 8,
          maxWidth: '100%',
        }}
        onOpenChange={async (open) => {
          
          if (open) {
            fetchList();
            const res = await fetchDetail();
            form.setFieldsValue({
              ...(formInitData || {}),
              author: formInitData?.author || res?.creator,
            });
          } else {
            form.setFieldsValue({});

            form?.resetFields();
          }
        }}
        clearOnDestroy
        trigger={
          <Button size="small" type="primary" disabled={isAIOK}>
            新增活动
          </Button>
        }
        form={form}
        name="basic"
        layout="vertical"
        onFinish={(value) => handleFinish(value)}
        onFinishFailed={onFinishFailed}
      >
        <Card bordered={false} title={'活动基本信息'}>
          <Row gutter={8}>
            <Col span={12}>
              <ProFormText
                rules={[
                  {
                    required: true,
                    message: '请填写活动名称',
                  },
                ]}
                label="活动名称"
                name="name"
              />
            </Col>
            <Col span={12}>
              <ProFormSelect
                rules={[
                  {
                    required: true,
                    message: '请选择预设生成',
                  },
                ]}
                name="plannedGeneration"
                label="预设生成"
                // showSearch
                // fieldProps={{
                //   labelInValue: true,
                // }}
                options={selects.plannedOrGeneration}
              />
            </Col>
            <Col span={12}>
              <ProFormText
                rules={[
                  {
                    required: true,
                    message: '请填写作者',
                  },
                ]}
                label="作者"
                name="author"
              />
            </Col>
            <Col span={12}>
              <ProFormSelect
                rules={[
                  {
                    required: true,
                    message: '请选择组织形式',
                  },
                ]}
                name="organizationForm"
                label="组织形式"
                // showSearch
                // fieldProps={{
                //   labelInValue: true,
                // }}
                options={selects.organization}
              />
            </Col>
            <Col span={12}>
              <ProFormDigit
                fieldProps={{
                  addonAfter: '分钟',
                }}
                rules={[
                  {
                    required: true,
                    message: '请输入活动时长',
                  },
                ]}
                // width="xs"
                name="durationInMinutes"
                label="活动时长"
                placeholder="请输入"
                min={0}
              />
            </Col>
            <Col span={24}>
            <ProFormList
              name="matrices"
              label="核心经验"
              copyIconProps={false}
              alwaysShowItemLabel
              creatorButtonProps={{
                creatorButtonText: '新增核心经验',
              }}
            >
              <ProFormGroup key="matrices">
                <ProFormSelect
                  rules={[
                    {
                      required: true,
                      message: '请选择领域',
                    },
                  ]}
                  width={'sm'}
                  name="matrix1Id"
                  label={'请选择领域'}
                  placeholder="请选择领域"
                  showSearch
                  // fieldProps={{
                  //   labelInValue: true,
                  // }}
                  options={matrix}
                />
                <ProFormSelect
                  dependencies={['matrix1Id']}
                  width={'sm'}
                  rules={[
                    {
                      required: true,
                      message: '请选择维度',
                    },
                  ]}
                  placeholder={'请选择维度'}
                  showSearch
                  label={'请选择维度'}
                  name="matrix2Id"
                  request={async (params) => {
                    if (!params?.matrix1Id) {
                      return [];
                    }
                    return await getLists({ pid: params.matrix1Id, current: 1, pageSize: 1000 });
                  }}
                />
                <ProFormSelect
                  dependencies={['matrix2Id']}
                  showSearch
                  rules={[
                    {
                      required: true,
                      message: '请选择子维度',
                    },
                  ]}
                  label={'请选择子维度'}
                  width={'lg'}
                  placeholder="请选择子维度"
                  name="matrix3Id"
                  request={async (params) => {
                    if (!params?.matrix2Id) {
                      return [];
                    }
                    return await getLists({ pid: params.matrix2Id, current: 1, pageSize: 1000 });
                  }}
                />
                <ProFormSelect
                  showSearch
                  width={'xl'}
                  dependencies={['matrix3Id']}
                  rules={[
                    {
                      required: true,
                      message: '请选择指标',
                    },
                  ]}
                  placeholder="请选择指标"
                  label={'请选择指标'}
                  name="targetId"
                  request={async (params) => {
                    if (!params?.matrix3Id) {
                      return [];
                    }
                    const res = await getTargetList({
                      matrix3Id: params.matrix3Id,
                      current: 1,
                      pageSize: 1000,
                      gradeId,
                    });

                    return res;
                  }}
                />
              </ProFormGroup>
            </ProFormList>
            </Col>
            
          </Row>
        </Card>
      </DrawerForm>
    </>
  );
};

export default AddActivityDrawer;
