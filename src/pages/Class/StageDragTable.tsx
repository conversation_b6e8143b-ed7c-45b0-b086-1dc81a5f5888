import { delActivity, updateActivitySort } from '@/services/class';
import { MinusSquareOutlined, PlusSquareOutlined } from '@ant-design/icons';
import { DragSortTable } from '@ant-design/pro-components';
import { useLocation, useParams } from '@umijs/max';
import { Button, message, Modal, Tag } from 'antd';
import dayjs from 'dayjs';
import { useCallback, useRef, useState } from 'react';
import AddActivityDrawer from './AddActivityDrawer';
import './index.less';
import { arrayToObject } from './util';
import { useModal } from '@/components/UseModal';
import TemplateList from './TemplateList';
import { exportTemplate } from '@/services/apis';
import { downloadWithUrl } from '@/services/utils';

const StageDragTable = (props: {
  queryList: () => void;
  selects: Record<string, any>;
  data: Record<string, any>;
  index: number;
  isAIOK: boolean;
}) => {
  const { selects, queryList, data, index, isAIOK } = props;
  const params = useParams();
  const subjectId = params.id;
  const templateRef = useRef<any>(null)
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const gradeId = searchParams.get('gradeId');
  const [isExpand, setIsExpand] = useState(!index);
  const [activeRows, setActiveRows] = useState<any[]>()
  const [AntModal, showModal, hideModal] = useModal()

  const deleteActivity = async (id: string) => {
    try {
      const res = await delActivity({
        id,
      });
      if (res?.status === 0) {
        message.success('删除成功');
        queryList();
      } else {
        message.success('删除失败');
      }
    } catch (error) {
      message.error(error?.message || '删除失败，请重试');
    }
  };

  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
      valueType: 'text',
      width: 30,
      search: false,
      hideInTable: isAIOK,
    },

    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'text',
      width: 50,
      search: false,
      render: (_, record, index) => {
        return `${index + 1}`;
      },
    },
    {
      title: '活动名称',
      dataIndex: 'name',
      valueType: 'text',
      width: 120,
      search: false,
    },
    // 什么时间？？
    {
      title: '实施时间',
      dataIndex: 'implementedAt',
      valueType: 'text',
      width: 80,
      search: false,
      render: (value: string, record: Record<string, any>) => {
        return record.implementedAt ? dayjs(value).format('YYYY-MM-DD') : '-';
      },
    },
    {
      title: '组织形式',
      dataIndex: 'organizationForm',
      valueType: 'select',
      valueEnum: arrayToObject(selects.organization || []),
      width: 80,
      search: false,
      render: (text, record) => {
        // #B5A282, #BAB8B9, #85858D, #9CAA9D, #4D4F4A (备选：#D2DAC3）
        const color = {
          Collective: '#B5A282',
          Group: '#BAB8B9',
          Talk: '#85858D',
          Team: '#9CAA9D',
          Default: '#D2DAC3',
        };
        return (
          <Tag
            style={{ textAlign: 'center' }}
            color={color[record.organizationForm] || color.Default}
          >
            {text}
          </Tag>
        );
      },
    },
    {
      title: '领域',
      valueType: 'text',
      dataIndex: 'matrix1',
      width: 80,
      search: false,
      render: (_, record) => {
        const texts = record?.matrices?.reduce((total, item) => {
          total[item.matrix1?.title] = item.matrix1?.title;
          return total;
        }, {});
        return (
          Object.keys(texts || {})?.map((item, index) => {
            return <div key={index}>{item || ''}</div>;
          }) || '-'
        );
      },
    },
    {
      title: '预设生成',
      valueType: 'select',
      dataIndex: 'plannedGeneration',
      valueEnum: arrayToObject(selects.plannedOrGeneration || []),
      width: 90,
      search: false,
      render: (text, record) => {
        return (
          <Tag
            style={{ textAlign: 'center' }}
            color={record.plannedGeneration === 'Planned' ? 'blue' : 'green'}
          >
            {text}
          </Tag>
        );
      },
    },
    {
      title: '评价',
      dataIndex: 'activityHighlight',
      valueType: 'text',
      width: 120,
      search: false,
      // ellipsis: true,
      render: (_, record) => {
        const color = {
          process: '#B5A282',
          question: '#BAB8B9',
          work: '#85858D',
          interest: '#9CAA9D',
        };
        const hasChildTeacherInteraction = !!record.childTeacherInteraction;
        const hasChildTeacherInteractionPhotoResourceIds =
          !!record.childTeacherInteractionPhotoResources?.length;
        const hasChildTeacherInteractionVideoResourceIds =
          !!record.childTeacherInteractionVideoResources?.length;
        const result = [];
        if (
          hasChildTeacherInteraction ||
          hasChildTeacherInteractionPhotoResourceIds ||
          hasChildTeacherInteractionVideoResourceIds
        ) {
          result.push(
            <Tag style={{ textAlign: 'center' }} color={color.process}>
              过程
            </Tag>,
          );
        }
        const hasActivityHighlight = !!record.activityHighlight;
        const hasActivityProblem = !!record.activityProblem;
        const hasImprovementMeasure = !!record.improvementMeasure;
        if (hasActivityHighlight || hasActivityProblem || hasImprovementMeasure) {
          result.push(
            <Tag style={{ textAlign: 'center' }} color={color.question || color.Default}>
              反思
            </Tag>,
          );
        }
        const hasChildArtworkResourceIds = !!record.childArtworkResources?.length;
        if (hasChildArtworkResourceIds) {
          result.push(
            <Tag style={{ textAlign: 'center' }} color={color.work || color.Default}>
              作品
            </Tag>,
          );
        }
        const hasInterestScore = !!record.interestScore;
        if (hasInterestScore) {
          result.push(
            <Tag style={{ textAlign: 'center' }} color={color.interest || color.Default}>
              兴趣
            </Tag>,
          );
        }
        return result;
      },
    },
    {
      title: '儿童兴趣',
      valueType: 'select',
      dataIndex: 'interestScore',
      valueEnum: arrayToObject(selects.SubjectActivityInterestScoreEnumDesc || []),
      width: 90,
      search: false,
    },
    {
      title: '活动难度',
      valueType: 'select',
      dataIndex: 'difficultyScore',
      valueEnum: arrayToObject(selects.SubjectActivityDifficultyScoreEnumDesc || []),
      width: 90,
      search: false,
    },
    {
      title: '教学准备',
      dataIndex: 'tin',
      valueType: 'text',
      width: 120,
      search: false,
      render: (text, record) => {
        return `电子${record?.electronicPreparationResources?.length || 0}份,实物${
          record?.physicalPreparationResources?.length || 0
        }份`;
      },
    },
    

    {
      title: '核心经验',
      valueType: 'text',
      dataIndex: 'matrices',
      width: 320,
      search: false,
      render: (_) => {
        return (
          _?.map((item, index) => {
            return (
              <div key={index}>
                <div style={{ marginBottom: 5, fontSize: 11 }}>
                  <Tag className="columnTags" color={'#199b00'} key={'领域'}>
                    领域
                  </Tag>
                  {item.matrix1?.title || ''}
                  <Tag className="columnTagsLeft" color={'#0084f6'} key={'维度'}>
                    维度
                  </Tag>
                  {item.matrix2?.title || ''}
                  <Tag className="columnTagsLeft" color={'#B5A282'} key={'子维度'}>
                    子维度
                  </Tag>
                  {item.matrix3?.title || ''}
                </div>
                <div style={{ fontSize: 11, marginBottom: 5 }}>
                  <Tag className="columnTagsLeft" color={'#BAB8B9'} key={'指标'}>
                    指标
                  </Tag>
                  {item.target?.title || ''}
                </div>
              </div>
            );
          }) || '-'
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: '16em',
      search: false,
      disable: true,
      fixed: 'right',
      render: (_, record) => {
        return [
          <Button
            size="small"
            type="link"
            key="edit"
            href={`/class/edit/${record?.id}?subjectStageId=${record.subjectStage}&subjectId=${subjectId}&gradeId=${gradeId}`}
            target="_blank"
            disabled={isAIOK}
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            编辑活动
          </Button>,
          <Button
            size="small"
            type="link"
            key="evaluate"
            href={`/class/evaluate/${record?.id}?subjectStageId=${record.subjectStage}&subjectId=${subjectId}`}
            target="_blank"
            disabled={isAIOK}
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            评价
          </Button>,
          <Button
            size="small"
            type="link"
            key="detail"
            href={`/class/activity/detail/${record?.id}?subjectStageId=${record.subjectStage}&subjectId=${subjectId}`}
            target="_blank"
            disabled={isAIOK}
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            详情
          </Button>,
          <Button
            size="small"
            danger
            key="del"
            disabled={isAIOK}
            onClick={(e) => {
              e.stopPropagation();
              Modal.confirm({
                title: '确认删除',
                content: '确认删除吗？',
                onOk: () => {
                  deleteActivity(record.id);
                },
              });
            }}
          >
            删除
          </Button>,
          <Button
            size="small"
            type="link"
            key="export"
            disabled={isAIOK}
            onClick={(e) => {
              e.stopPropagation();
              setActiveRows([record]);
              showModal();
            }}
          >
            导出模板
          </Button>,
        ];
      },
    },
  ];

  const handleDragSortEnd = async (
    beforeIndex: number,
    afterIndex: number,
    newDataSource: any,
    subjectStageId: string,
  ) => {
    console.log('排序后的数据', newDataSource, beforeIndex, afterIndex);
    try {
      const res = await updateActivitySort({
        subjectId,
        subjectStageId,
        ids: newDataSource?.map((da: any) => da.id),
      });
      if (res?.status === 0) {
        message.success('修改列表排序成功');
        queryList();
      } else {
        message.success('修改列表排序失败');
      }
    } catch (error) {
      message.error(error?.message || '修改失败，请重试');
    }
    // getList()
    // setDataSource(newDataSource);

    // message.success('修改列表排序成功');
  };

  const onConfirmDownload = useCallback(() => {
    const templateId = templateRef.current.templateId;
    if (!templateId) {
      message.warning('未找到模板');
      return;
    }
    exportTemplate({
      id: templateId,
      subjectActivityIds: activeRows?.map((v) => v.id),
    }).then((response) => {
      if (response.status === 0) {
        const data = response.data;
        downloadWithUrl(data.uri, data.filename);
        hideModal();
      }
    });
  }, [activeRows]);
  return (
    <>
      <DragSortTable<API.RuleListItem, API.PageParams>
        search={false}
        headerTitle={
          <div
            onClick={() => {
              setIsExpand(!isExpand);
            }}
          >
            {isExpand ? (
              <MinusSquareOutlined color="gray" style={{ marginRight: 15 }} />
            ) : (
              <PlusSquareOutlined color="gray" style={{ marginRight: 15 }} />
            )}{' '}
            {data.subjectStage?.title}
          </div>
        }
        tableClassName={isExpand ? 'expandDragSort' : 'noExpandDragSort'}
        bordered={false}
        pagination={false}
        className={'clearBorderTable'}
        // empty={false}
        scroll={{ x: 1600 }}
        rowKey="id"
        columns={columns}
        locale={{
          emptyText: <></>,
        }}
        onRow={(record) => ({
          onClick: () => {
            if (!isAIOK) {
              window.open(
                `/class/activity/detail/${record?.id}?subjectStageId=${record.subjectStage}&subjectId=${subjectId}`,
              );
              return;
            }
            message.error('请先到小程序中，生成所有课程！')
          },
        })}
        toolBarRender={() => {
          return [
            <AddActivityDrawer
              subjectStageId={data.subjectStage?.id || ''}
              onRefresh={() => queryList()}
              selects={selects}
              key={data?.subjectStage?.id}
              isAIOK={isAIOK}
            />,

            <Button
              onClick={() => {
                showModal();
                setActiveRows(data?.subjectActivities);
              }}
              disabled={isAIOK}
              size="small"
              type="primary"
              key={'exportAll'}
            >
              批量导出
            </Button>,
          ];
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
          reload: false,
        }}
        dataSource={
          isExpand
            ? data?.subjectActivities?.map((sub: any) => {
                return {
                  ...sub,
                  subjectStage: data.subjectStage?.id,
                };
              })
            : []
        }
        columnsState={{
          persistenceKey: `${data.subjectStage?.id}`,
          persistenceType: 'localStorage',
          // value: columnsState,
          defaultValue: {
            matrices: { show: false, order: 2 },
            keyQuestion: { show: false, order: 2 },
            interestScore: { show: false, order: 2 },
            difficultyScore: { show: false, order: 2 },
          },
        }}
        dragSortKey="id"
        onDragSortEnd={(beforeIndex, afterIndex, newDataSource) =>
          handleDragSortEnd(beforeIndex, afterIndex, newDataSource, data.subjectStage?.id)
        }
      />
      <AntModal onOk={() => onConfirmDownload()} title="选择模版" width={'80%'}>
        <TemplateList ref={templateRef} />
      </AntModal>
    </>
  );
};

export default StageDragTable;
