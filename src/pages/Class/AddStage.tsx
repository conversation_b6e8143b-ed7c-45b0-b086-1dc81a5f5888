import { PlusOutlined } from '@ant-design/icons';
import { ModalForm, ProFormText } from '@ant-design/pro-components';
import { Button, Col, Form, Row } from 'antd';

export default (props: {
  id?: string;
  isDetail?: boolean;
  onSubmit?: (params: Record<string, any>) => void;
  data?: Record<string, any>;
  isAIOK: boolean;
}) => {
  const { isDetail, id, data, isAIOK } = props;
  const [form] = Form.useForm<{ name: string; company: string }>();

  const getTitle = () => {
    if (isDetail) {
      return data?.title || '阶段详情';
    }
    if (id) {
      return '编辑阶段';
    }
    return '新增阶段';
  };

  const getButtonText = () => {
    if (id) {
      return `编辑`;
    }
    return (
      <>
        <PlusOutlined />
        新增阶段
      </>
    );
  };
  return (
    <ModalForm<{
      name: string;
      company: string;
    }>
      clearOnDestroy
      key={data?.id || Date.now()}
      {...(isDetail && {
        readonly: true,
      })}
      //  readonly
      title={`${getTitle()}`}
      trigger={<Button type="primary" disabled={isAIOK} size={getButtonText() === '编辑' ? 'small' : ''}>{getButtonText()}</Button>}
      form={form}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: () => console.log('run'),
      }}
      submitTimeout={2000}
      initialValues={data}
      onFinish={async (values) => {
        props.onSubmit(values);
        return true;
      }}
    >
      <Row gutter={8}>
        <Col span={18}>
          <ProFormText
            rules={[
              {
                required: true,
                message: '请选择课程类别',
              },
            ]}
            name="title"
            label="标题"
            placeholder="请输入阶段名称，如“阶段一：什么样的泥适合制作陶器？”"
          />
        </Col>
      </Row>
    </ModalForm>
  );
};
