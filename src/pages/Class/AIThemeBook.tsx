/* eslint-disable guard-for-in */
import { ProList } from '@ant-design/pro-components';
import { Tag, Button, Space, Modal, Empty, Typography, Spin, message } from 'antd'
import React, { useEffect, useRef, useState, useMemo } from 'react';
import { getTaskList } from '@/services/class';
import FileViewer from 'react-file-viewer'
import { useParams } from '@umijs/max';
import './AIThemeBook.less'


// let image = [
//   {
//     title: '语雀的天空',
//     eee:
//       'https://gw.alipayobjects.com/zos/antfincdn/efFD%24IOql2/weixintupian_20170331104822.jpg',
//   }
// ];

// let bookId = '1487'

let initPage = {
  current: 1,
  pageSize: 10,
  // 101 - 儿童 - 生成 - AI报告
  // 102 - 儿童 - 生成 - 合并AI报告结果
  // 120 - 课程 - 生成 - AI 主题书
  category: 120,
};


// 文件
function formatFileSize(size:number) {
  if (!size) return "0KB";
  if (size < 1024) {
    return size + "B";
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + "KB";
  } else {
    return (size / 1024 / 1024).toFixed(2) + "MB";
  }
}

// 解构
const deconstruction = (data:string) => {
  return (data ? JSON.parse(data) : {})
};

// 获取状态文本
function getStatusText(status:number |string) {
  switch (status) {
    case 0:
      return "完成";
    case 1:
      return "待生成";
    case 2:
      return "生成中";
    default:
      return "未知";
  }
}

// 获取tag样式
function getStatusColor(status:number) {
  switch (status) {
    case 0:
      return "success";
    case 1:
      return "warning";
    case 2:
      return "processing";
    default:
      return "default";
  }
}

interface FileType {
    [props:string]: string
}
// 获取文件类型
function getFileType(fileUrl: string)  {
  if (!fileUrl) return "pdf";
  const extension:string = fileUrl.split(".").pop()?.toLowerCase() || "pdf";
  const typeMap:FileType = {
    pdf: "pdf",
    doc: "doc",
    docx: "docx",
    xls: "xls",
    xlsx: "xlsx",
    ppt: "ppt",
    pptx: "pptx",
    txt: "txt",
  };
  return typeMap[extension] ;
}

// 全局提示
 const tipsMessage = (tips: string, type:'warning'|'error'| 'success') => {
     message.error({
         content: tips,
         type: type
    })
  };

const StatisticsList: React.FC = () => {
    const [previewFile, setPreviewFile] = useState<any>()
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false)
    const [reportList, setReportList] = useState([]);
    const [loading, setLoading] = useState(false)
    const [dataSource, setDataSource] = useState([])
    const params = useParams()
    // 获取书籍列表
    const getBookList = async (id: string | undefined) => {
        if (!id) {
            tipsMessage('请求出错，未获得id', 'error')
            return
        }
        let params = { ...initPage, subjectId: id };
        setLoading(true)
        try {
            let res = await getTaskList(params);
            if (res.status === 0) {
                setReportList(res?.data || [])
                const list:any[] =  res?.data.map((dataItem: any) => {
                    const item = deconstruction(dataItem.result)
                    return {
                        title: item.filename || "AI主题书生成中...",
                        avatar: item.createdAt || '',//formatFileSize(item.size)
                        uri: item.uri,
                        filePath: item.filepath,
                    }
                })
            
                setDataSource(list)
            } else {
                tipsMessage('主题书获取失败', 'error')
                // uni.$u.toast("主题书获取失败");
            }
            setLoading(false)
        } catch (e) {
            setLoading(false)
            tipsMessage('请求出错', 'error')
        }
        
    };
    
    useEffect(() => {
        if (typeof params.id === 'string') {
            getBookList(params.id);
        } else {
            tipsMessage('请求出错', 'error')
        }
       
    }, [params.id]);

    function onModalCancel() {
        setIsModalOpen(false)
    }

    const Card = useMemo(() => { 
        
        const onTestPdf = (item: any) => {
            console.log(2123123, item)
            setPreviewFile({
                fileName: item.title,
                createAt: item.avatar,
                fileUrl: item.uri,
                filePath: item.filepath,
                fileType: getFileType(item.uri)
            })
            setIsModalOpen(true)
        }

        
         let pageComponent = <div></div>;
        if (reportList.length) {
            pageComponent = reportList?.map((item: Record<string, any>, index) => {
                const listItem = deconstruction(item.result)
                return (
                    <div key={item?.key} >
                    
                        <ProList<{ title: string } >
                            toolBarRender={() => {
                                return [
                                    <Button key="3" type="primary" onClick={() => {getBookList(params.id)}}>
                                        生成最新主题书
                                    </Button>,
                                ];
                            }}
                            metas={{
                                title: {},
                                subTitle: {
                                    render: () => {
                                        return (
                                            <Space size={0}>
                                                <Tag color={getStatusColor(item.state)}>{getStatusText(item.state)}</Tag>
                                                {listItem.size ? <Tag color="default" bordered={false}>{formatFileSize(listItem.size)}</Tag> : ''}
                                            </Space>
                                        );
                                    },
                                },
                                description: {
                                    render: (dom, record, index) => {
                                        console.log(dataSource[index]['avatar'], '<<<')
                                        return dataSource[index]['avatar'];
                                    },
                                },
                                avatar: {},
                                actions: {
                                    render: (dataNode, record, index) => {
                                        if (item.state === 0) {
                                            return [<a key="init" onClick={() => {
                                                onTestPdf(record)
                                            }}>查看</a>];
                                        } else {
                                            const txt: string = getStatusText(item.state)
                                            const btn = <Tag>{txt}</Tag>
                                            return [btn]
                                        }
                                    
                                    },
                                },
                            }}
                            rowKey="title"
                            headerTitle="AI主题书"
                            split={true}
                            dataSource={dataSource}
                        />          


                    </div>
                );
            });
        } else {
            pageComponent = (<div style={{ height: '400px', width: '100%',}}>
                <div style={{position:'relative', height: '70px'}}>
                    <Button key="3" type="primary" style={{ position: 'absolute', top:'18px', right:'23px'}} onClick={()=>getBookList(params.id)}>
                        生成最新主题书
                    </Button>
                </div> 
                <Empty
                    styles={{ image: { height: 60 } }}
                    description={
                    <Typography.Text>
                        暂无主题书
                    </Typography.Text>
                    }
                >
                </Empty>
            </div>)
        }

        return <>
            <Spin tip="Loading" spinning={loading} size="large">
                {pageComponent}
            </Spin>
        
        </>
      }, [reportList]);


  return (
    <>
      {reportList.length ? (
        ''
      ) : (
        <div
          style={{
            padding: 10,
          }}
        >
           <Button key="4" type="primary">
                生成AI主题书
            </Button>,
        </div>
      )}
    
    {Card}
          
    {/* {!!previewFile?.fileUrl && (
        <PDFPreviewModal
          fileName={previewFile?.fileName}
          fileUrl={previewFile?.filePath}
          onCancel={() => setPreviewFile('')}
        />
      )} */}
          {/* filePath={previewFile?.filePath} */}
          {/* onError={this.onError.bind(this)} //函数[可 */}
          {/* fileType='docx'//文件类型
                    filePath={previewFile?.filePath} //文件地址(后台给返的二进制流也可 */}
           <Modal
                title={`【${previewFile?.fileName}】预览`}
                open={isModalOpen}
                onCancel={onModalCancel}
                footer={null}
                width={'80%'}
                centered
                className={'custom-modal-conten'}
                style={{ top: 20, height: '600px' }}
          >
              <FileViewer
                className="custom-file-viewer"
                fileType={ previewFile?.fileType}//文件类型
                filePath={previewFile?.fileUrl} //文件地址(后台给返的二进制流也可
                errorComponent={<div>加载出现错误</div>}
                unsupportedComponent = {<div>浏览器不支持插件，请升级至最新的chrome浏览器</div>}
            />  
            
        </Modal>
          
    
    </>
   

  );
};

export default StatisticsList;
