import UploadImageComponent from '@/components/UploadImage';
import { addResourceByTimer } from '@/services/class';
import { useLocation } from '@umijs/max';
import { Button, message } from 'antd';
import { useState } from 'react';
export default () => {
  const [client, setClient] = useState<any>();
  const [headerList, setHeaderList] = useState<API.FileItem[]>([]);
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const timestamp = searchParams.get('timestamp');
  const token = searchParams.get('token');

  const onClick = async () => {
    if(!headerList.length) return;
    const res = await addResourceByTimer({
      token,
      resourceIds: headerList?.map((item) => item.id),
      timestamp,
    });
    if (res.status === 0) {
      message.success('上传成功');
    } else {
      message.success('上传失败');
    }
  };
  return (
    <div
      style={{
        background: '#fff',
        padding: '10px',
        height: '60vh',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
      }}
    >
      <UploadImageComponent
        key="upload"
        fileName="headerId"
        label="上传"
        max={10}
        client={client}
        fileList={headerList}
        setClient={setClient}
        setFileList={setHeaderList}
        edit={true}
        // accept=".png,.jpg,.jpeg,.gif"
      />

      <Button
        size="small"
        style={{
          marginTop: 10,
          width: 100,
        }}
        onClick={onClick}
      >
        提交
      </Button>
    </div>
  );
};
