import { fetchSubjectDetail, getActivityList, getAICreate, updataActState } from '@/services/class';
import { DictionaryCategory } from '@/services/constants';
import { PageContainer } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import type { TabsProps } from 'antd';
import { Tabs, Switch, message } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useState, useCallback } from 'react';
import { FormItem } from './ActivityDetail';
import Detail from './DetailForm';
import './index.less';
import StageDragTable from './StageDragTable';
import StageList from './StageList';
import StatisticsList from './StatisticsList';
import AIThemeBook from './AIThemeBook';
import { fetchCategory, FetchSelectProps, fetchSelects, getTerm } from './util';
import { getToken } from '@/services/common';

const ActivityList = (props: {
  selects: Record<string, any>; // selects 是一个键值对对象，键为字符串，值为任意类型
  isActivity: boolean; // isActivity 是一个布尔值，表示是否为活动状态
  isAIOK: boolean; // isAIOK 是一个布尔值，表示是否所有条件都满足
}) => {
  const { isActivity, selects, isAIOK } = props;
  const [list, setList] = useState([]);
  const params = useParams();
  const subjectId = params.id;

  const queryList = async () => {
    try {
      const list = await getActivityList({ subjectId });
      setList(list?.data);
    } catch (e) {
      setList([]);
    }
  };

  useEffect(() => {
    if (isActivity) {
      queryList();
    }
  }, [isActivity]);
  useEffect(() => {
    console.log('isAIOK', isAIOK);

    isAIOK && queryList();
  }, []);

  const Table = useMemo(() => {
    if (list.length) {
      return list?.map((item: Record<string, any>, index) => {
        return (
          <div key={item.subjectStage?.id} style={{ marginBottom: '20px' }}>
            <StageDragTable
              queryList={queryList}
              data={{
                ...item,
                id: item.subjectStage?.id,
              }}
              selects={selects}
              index={index}
              isAIOK={isAIOK}
            />
          </div>
        );
      });
    }
    return <></>;
  }, [list]);

  return (
    // <PageContainer style={{ background: '#fff' }}>
    <>
      {list.length ? (
        ''
      ) : (
        <div
          style={{
            padding: 10,
          }}
        >
          请先新增阶段！
        </div>
      )}
      {Table}
    </>
  );
};

const App: React.FC = () => {
  const params = useParams();
  const subjectId = params.id;
  const [activityKey, setActivityKey] = useState('1');
  const [selects, setSelects] = useState<FetchSelectProps>({
    plannedOrGeneration: [],
    organization: [],
  });
  const [detail, setDetail] = useState<{ title?: string }>({});
  const [categoryOptions, setCategoryOptions] = useState([]);
  const [gradeOptions, setGradeOptions] = useState([]);
  const [termList, setTermList] = useState([]);
  const [isAIOK, setIsAIOK] = useState<boolean>(true);
  const [mindMapKey, setMindMapKey] = useState(0);
  const [checked, setChecked] = useState(true);

  const getSelects = async () => {
    const data = await fetchSelects();
    setSelects(data);
  };

  // 查询小程序端是否点击了生成所有教案的按钮

  const getAIOk = useCallback(
    async (record: any) => {
      const res = await getAICreate(record);
      if (res.status === 0) {
        const { aiFlag, submitAiFlag } = res.data;
        setIsAIOK(aiFlag === 1 && submitAiFlag === 0);
      }
    },
    [getAICreate],
  );

  useEffect(() => {
    getAIOk(subjectId);
  }, [getAIOk]);

  useEffect(() => {
    getSelects();
  }, []);

  const onChange = (key: string) => {
    setActivityKey(key);
    if (key === '3') {
      setMindMapKey((prev) => prev + 1);
    }
  };
  const fetchDetail = async () => {
    try {
      const res = await fetchSubjectDetail(subjectId);
      console.log(res.data, '页面详情');

      setDetail(res.data || {});
      setChecked(res.data?.state !== 4);
    } catch (e) {
      setDetail({});
    }
  };

  // 切换草稿状态
  const headleChange = async (v) => {
    try {
      let res = await updataActState({
        id: detail?.id,
        state: v ? 1 : 4,
      });
      setChecked(v);
      if (res.status === 0) {
        message.success('切换成功！');
        return;
      }
      message.error('切换失败！：' + res.message);
      console.log(res, 'res');
    } catch (e) {
      console.log(e, 'e');
      message.error('操作失败');
    }
  };

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '活动列表',
      children: <ActivityList selects={selects} isActivity={activityKey === '1'} isAIOK={isAIOK} />,
    },
    {
      key: '2',
      label: '课程文本',
      children: <Detail isAIOK={isAIOK} />,
    },
    {
      key: '3',
      label: '思维导图',
      children: (
        <div style={{ width: '100%', height: '80vh' }}>
          <iframe
            key={mindMapKey}
            src={`https://ai.mypacelab.com/app/mindMap?title=${encodeURIComponent(
              detail?.title || '',
            )}&type=ylf&id=${subjectId || ''}&token=${getToken()}`}
            style={{ width: '100%', height: '100%', border: 'none' }}
            title="思维导图"
          />
        </div>
      ),
    },
    {
      key: '4',
      label: '阶段列表',
      children: <StageList isAIOK={isAIOK} />,
    },
    {
      key: '5',
      label: '核心经验统计',
      children: <StatisticsList />,
    },
    {
      key: '61',
      label: 'AI主题书',
      children: <AIThemeBook />,
    },
  ];

  const getTermList = async () => {
    const result = await getTerm();
    setTermList(result);
  };
  useEffect(() => {
    getTermList();
    fetchCategory(DictionaryCategory.Category)
      .then((res) => {
        const lists = res?.map((item) => ({ ...item, value: parseInt(item.value) })) || [];
        setCategoryOptions(lists);
      })
      .catch(() => {
        setCategoryOptions([]);
      });

    fetchCategory(3)
      .then((res) => {
        const lists = res?.map((item) => ({ ...item, value: parseInt(item.value) })) || [];
        setGradeOptions(lists);
      })
      .catch(() => {
        setGradeOptions([]);
      });
    fetchDetail();
  }, []);
  return (
    <PageContainer style={{ background: '#fff' }} title={detail.title}>
      <FormItem
        // title={detail.title}
        data={detail}
        lists={[
          { dataIndex: 'categoryId', label: '课程类别', type: 'select', options: categoryOptions },
          {
            dataIndex: 'startTimer',
            label: '实施时间',
            render: (data) => {
              return `${(data?.startAt && dayjs(data?.startAt).format('YYYY-MM-DD')) || ''} - ${
                (data?.startAt && dayjs(data?.endAt).format('YYYY-MM-DD')) || ''
              }`;
            },
            span: 2,
          },
          { dataIndex: 'term', label: '学期', type: 'select', options: termList },
          { dataIndex: 'gradeId', label: '年级', type: 'select', options: gradeOptions, span: 2 },
          { dataIndex: 'creator', label: '创作者', span: 3 },
        ]}
      />
      <div style={{ display: 'flex' }}>
        <div className={'switch-container'}>
          { checked ? '' : <span className={'text-red'}>{isAIOK ? '待采用，请到小程序采用课程' : '草稿'}</span> }
          {isAIOK ? (
            ''
          ) : (
            <Switch checked={checked} defaultChecked onChange={headleChange} disabled={isAIOK} />
          )}
        </div>
      </div>
      <Tabs defaultActiveKey="1" items={items} onChange={onChange} />
    </PageContainer>
  );
};

export default App;
