import { getDictionary, fetchEnum } from '@/services/api';
export const fetchCategory = async (category: number) => {
  try {
    const res = await getDictionary({
      current: 1,
      pageSize: 0,
      category,
    });
    const list = res?.data.map((item: any) => {
      return {
        label: item.value,
        text: item.value,
        value: `${item.id}`,
      };
    });
    return list;
  } catch (e) {
    return [];
  }
};

export const getTerm = async () => {
  const res = await fetchEnum();
  const SubjectTermEnum = res?.data?.SubjectTermEnum;
  return Object.keys(SubjectTermEnum || {}).map((key) => {
    return { value: parseInt(key), label: SubjectTermEnum[key], text: SubjectTermEnum[key] };
  });
};

export const arrayToObject = (list: any) => {
  return (
    list?.reduce((total: any, next: any) => {
      total[next.value] = next;
      return total;
    }, {}) || {}
  );
};

const getList = (data: Record<string, any>) => {
  return Object.keys(data || {}).map((key) => {
    return {
      value: key,
      label: data[key],
      text: data[key],
    };
  });
};

// 获取预设、组织形式的options
interface EnumOptions {
  value: string | number;
  label: any;
  text: any;
}
export interface FetchSelectProps {
  [key: string]: EnumOptions[];
}
export const fetchSelects: () => Promise<FetchSelectProps> = async () => {
  try {
    const res = await fetchEnum();
    const SubjectActivityOrganizationFormEnumDesc =
      res?.data?.SubjectActivityOrganizationFormEnumDesc;
    const SubjectActivityPlannedOrGenerationEnumDesc =
      res?.data?.SubjectActivityPlannedOrGenerationEnumDesc;
    const EducationalEnum = res?.data?.EducationalEnum;
    return {
      organization: getList(SubjectActivityOrganizationFormEnumDesc),
      educational: getList(EducationalEnum),
      plannedOrGeneration: getList(SubjectActivityPlannedOrGenerationEnumDesc),
      SubjectActivityDifficultyScoreEnumDesc: getList(
        res?.data?.SubjectActivityDifficultyScoreEnumDesc,
      )?.map((item) => ({ ...item, value: parseInt(item.value) })),
      SubjectActivityInterestScoreEnumDesc: getList(
        res?.data?.SubjectActivityInterestScoreEnumDesc,
      )?.map((item) => ({ ...item, value: parseInt(item.value) })),
    };
  } catch (e) {
    return {} as FetchSelectProps;
  }
};
