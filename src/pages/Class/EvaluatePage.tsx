/* eslint-disable guard-for-in */
import UploadImageComponent from '@/components/UploadImage';
import { getClassDetail, updateEvaluate } from '@/services/api';
import { PageContainer, ProForm, ProFormSelect, ProFormTextArea } from '@ant-design/pro-components';
import { history, useLocation, useParams } from '@umijs/max';
import { Card, Col, Form, Row, message } from 'antd';
import { useEffect, useState } from 'react';
import { FetchSelectProps, fetchSelects } from './util';
import ToIphoneButton from '@/components/UploadImage/ToIphoneButton';

import { EvaluateDetailPage } from './ActivityDetail';
import './index.less';

const Evaluate = () => {
  const params = useParams();
  const id = params.id;
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const [selects, setSelects] = useState<FetchSelectProps>({
    plannedOrGeneration: [],
    organization: [],
  });

  const getSelects = async () => {
    const data = await fetchSelects();
    setSelects(data);
  };

  const [client, setClient] = useState<any>();
  const [ugcList, setUgcList] = useState<API.FileItem[]>([]);
  const [headerList, setHeaderList] = useState<API.FileItem[]>([]);
  const [worksList, setWorksList] = useState<API.FileItem[]>([]);
  const [detail, setDetail] = useState({});

  const [form] = Form.useForm();

  const handleFinish = async (value) => {
    const isValid = await form.validateFields();

    if (isValid) {
      // 表单校验通过后的处理逻辑
      const { headerId, ugcIds, works, ...rest } = value;

      const result = {
        ...rest,
      };
      if (ugcIds?.length) {
        result['childTeacherInteractionVideoResourceIds'] = ugcList?.map((item) => item?.id);
      }
      if (headerId?.length) {
        result['childTeacherInteractionPhotoResourceIds'] = headerList?.map((item) => item?.id);
      }
      if (works?.length) {
        result['childArtworkResourceIds'] = worksList?.map((item) => item?.id);
      }

      try {
        const res = await updateEvaluate({
          ...result,
          interestScore: result.interestScore || null,
          difficultyScore: result.difficultyScore || null,
          id,
        });
        if (res?.status === 0) {
          message.success('评价成功');
          const newSearchParams = new URLSearchParams(location.search);
          newSearchParams.set('isDetail', 'true');
          const newLocation = {
            ...location,
            search: newSearchParams.toString(),
          };
          history.push(newLocation);
          return true;
        } else {
          message.success('评价失败');
        }
      } catch (error) {
        console.log(error);
        message.error(error?.message || '评价失败，请重试');
      }
    }
  };
  const isDetail = !!searchParams.get('isDetail');

  const getDetail = async () => {
    try {
      const _detail = await getClassDetail({ id });
      const {
        childTeacherInteractionVideoResources,
        childTeacherInteractionPhotoResources,
        childArtworkResources,
      } = _detail?.data || {};

      const ugcList = childTeacherInteractionVideoResources?.map((item) => ({
        id: item.id,
        uri: item.uri,
        filename: item.filename,
        category: item.category,
      }));

      const headerList = childTeacherInteractionPhotoResources?.map((item) => ({
        id: item.id,
        uri: item.uri,
        filename: item.filename,
        category: item.category,
      }));
      const worksList = childArtworkResources?.map((item) => ({
        id: item.id,
        uri: item.uri,
        filename: item.filename,
        category: item.category,
      }));
      setHeaderList(headerList || []);
      setUgcList(ugcList || []);
      setWorksList(worksList || []);
      form.setFieldsValue({
        ...(_detail?.data || {}),
        works: worksList,
        headerId: headerList,
        ugcIds: ugcList,
      });
      setDetail(_detail?.data);
    } catch (e) {
      setHeaderList([]);
      setUgcList([]);
      form.setFieldsValue({});
      setDetail({});
    }
  };

  useEffect(() => {
    getSelects();
  }, []);

  useEffect(() => {
    if (id) {
      getDetail();
    }
  }, [id, isDetail]);

  const onFinishFailed = () => {
    message.error('表单校验失败，请检查输入');
  };

  return (
    <PageContainer style={{ background: '#fff' }}>
      {isDetail ? (
        <EvaluateDetailPage detail={detail} selects={selects} />
      ) : (
        <ProForm
          className="evaluate"
          style={{
            margin: 'auto',
            marginTop: 8,
            maxWidth: '100%',
          }}
          clearOnDestroy
          form={form}
          name="basic"
          layout="vertical"
          {...((isDetail && { submitter: false, readonly: true }) || {
            submitter: {
              resetButtonProps: {
                style: {
                  display: 'none', // 隐藏提交按钮
                },
              },
            },
          })}
          onFinish={(value) => handleFinish(value)}
          onFinishFailed={onFinishFailed}
        >
          <Card bordered={false} title={'过程性记录'}>
            <Row gutter={8}>
              <Col span={24}>
                <ProFormTextArea
                  name="childTeacherInteraction"
                  label="幼儿与教师的语言互动"
                  placeholder="请输入幼儿与教师的语言互动"
                />
              </Col>
              <Col span={24}>
                <UploadImageComponent
                  key="upload"
                  fileName="headerId"
                  label={
                    <div style={{ display: 'flex' }}>
                      <div>照片</div>
                      <ToIphoneButton form={form} setList={setHeaderList} list={headerList} fileName='headerId' />
                    </div>
                  }
                  max={10}
                  client={client}
                  fileList={headerList}
                  setClient={setClient}
                  setFileList={setHeaderList}
                  edit={!isDetail}
                  // accept=".png,.jpg,.jpeg,.gif"
                />
              </Col>
              <Col span={24}>
                <UploadImageComponent
                  key="upload"
                  fileName="ugcIds"
                  label={
                    <div style={{ display: 'flex' }}>
                      <div>音视频</div>
                      <ToIphoneButton form={form} setList={setUgcList} list={ugcList} fileName='ugcIds' />
                    </div>
                  }
                  max={10}
                  client={client}
                  fileList={ugcList}
                  setClient={setClient}
                  setFileList={setUgcList}
                  edit={!isDetail}
                />
              </Col>
            </Row>
          </Card>
          <Card bordered={false} title={
            <div style={{ display: 'flex' }}>
              <div>幼儿作品</div>
              <ToIphoneButton form={form} setList={setWorksList} list={worksList} fileName='works' />
            </div>
          }>
            <UploadImageComponent
              key="upload"
              fileName="works"
              label=""
              max={1}
              client={client}
              fileList={worksList}
              setClient={setClient}
              setFileList={setWorksList}
              edit={!isDetail}
            />
          </Card>
          <Card title={'嵌入式儿童评估'}>
            <Row>
              <Col span={24}>
                <ProFormSelect
                  name="interestScore"
                  label="幼儿对本活动的兴趣程度"
                  // showSearch
                  // fieldProps={{
                  //   labelInValue: true,
                  // }}
                  options={selects.SubjectActivityInterestScoreEnumDesc || []}
                />
              </Col>
              <Col span={24}></Col>
            </Row>

            <Row>
              <Col span={24}>
                <ProFormSelect
                  // rules={[
                  //   {
                  //     required: true,
                  //     message: '请选择预设生成',
                  //   },
                  // ]}
                  name="difficultyScore"
                  label="本活动的难度"
                  // showSearch
                  // fieldProps={{
                  //   labelInValue: true,
                  // }}
                  options={selects.SubjectActivityDifficultyScoreEnumDesc || []}
                />
              </Col>
            </Row>
          </Card>
          <Card title={'活动实施反思'} style={{ marginBottom: 15 }}>
            <ProFormTextArea
              name="activityHighlight"
              label="活动的亮点"
              placeholder="请输入活动的亮点"
            />
            <ProFormTextArea
              name="activityProblem"
              label="活动存在的问题"
              placeholder="请输入活动存在的问题"
            />
            <ProFormTextArea
              name="improvementMeasure"
              label="改进措施"
              placeholder="请输入改进措施"
            />
          </Card>
        </ProForm>
      )}
    </PageContainer>
  );
};

export default Evaluate;
