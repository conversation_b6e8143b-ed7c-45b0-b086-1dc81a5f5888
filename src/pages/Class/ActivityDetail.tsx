/* eslint-disable guard-for-in */
import Player from '@/components/Player';
import { getClassContext, getClassDetail, getMatrix, getStateList } from '@/services/api';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Button, Card, Col, ConfigProvider, Descriptions, Form, Image, Row, Typography } from 'antd';

import dayjs from 'dayjs';
import { get } from 'lodash';
import { ReactNode, useEffect, useState } from 'react';
import './index.less';
import { fetchSelects } from './util';

const { Meta } = Card;

export const FormItem = ({
  title,
  lists,
  data,
}: {
  lists: {
    label: string;
    dataIndex: string | string[];
    render?: (data: Record<string, any>, key: string | string[]) => string | ReactNode;
    span?: number;
    options?: Record<string, any>[];
    type?: 'text' | 'select' | 'file' | 'protable';
    columns?: Record<string, any>[];
  }[];
  data: Record<string, any>;
  title?: string;
}) => {
  return (
    <Descriptions
      title={title || ''}
      style={{
        marginBottom: 32,
      }}
      className="descriptionDetail"
    >
      {lists?.map((list) => {
        const { dataIndex, span, options, type, columns } = list;
        let value = get(data, dataIndex);
        const key = Array.isArray(dataIndex) ? dataIndex.join(',') : dataIndex;
        if (type === 'file') {
          
          return (
            <Descriptions.Item className="descriptionsCard" key={key} span={4} label={list.label}>
              {value?.length ? (
                <Image.PreviewGroup
                  preview={{
                    onChange: (current, prev) =>
                      console.log(`current index: ${current}, prev index: ${prev}`),
                  }}
                >
                  {value?.map((item: any, index: number) => {
                    return (
                      <Card
                        key={index}
                        hoverable
                        // className='descriptionsCard'
                        style={{ width: 200, marginRight: 10, marginBottom: 10 }}
                        cover={
                          <div className="descriptionsImage">
                            <Player
                              style={{
                                height: 140,
                                width: 200,
                                // height: '100%',
                                // width: 'auto',
                              }}
                              {...item}
                            />
                            {/* <Image
                              style={{
                                maxHeight: 140,
                                maxWidth: 200,
                                height: '100%',
                                width: 'auto',
                              }}
                              src={item.uri}
                              fallback={
                                'https://pace-toy-store.oss-cn-shenzhen.aliyuncs.com/df/02/4d151d7d5a3ca1684c02736cbc8b02df.png'
                              }
                            /> */}
                          </div>
                        }
                      >
                        <Meta title="" description={item.filename} />
                      </Card>
                    );
                  })}
                </Image.PreviewGroup>
              ) : (
                <div style={{ color: '#9e9b9b' }} className="descriptionsTips">
                  暂未上传
                </div>
              )}
            </Descriptions.Item>
          );
        }
        if (type === 'protable') {
          return (
            <Descriptions.Item key={key} span={4} label={list.label}>
              <ConfigProvider
                renderEmpty={() => {
                  return <div>暂无数据</div>;
                }}
              >
                <ProTable<API.RuleListItem, API.PageParams>
                  rowKey="id"
                  search={false}
                  pagination={false}
                  dataSource={value || []}
                  options={false}
                  columns={columns}
                />
              </ConfigProvider>
            </Descriptions.Item>
          );
        }
        if (options?.length && type === 'select') {
          value = options?.find((item) => item.value === get(data, dataIndex))?.label;
        } else if (list?.render) {
          value = list?.render(data, dataIndex);
        }
        let _val = '';
        try {
          _val =
            (value && typeof value === 'string' && value.replace(/\n/g, '<br />')) || value || '-';
        } catch (e) {
          _val = value;
        }

        return (
          <Descriptions.Item key={key} span={span || 1} label={list.label}>
            <span style={{ fontSize: 13 }} dangerouslySetInnerHTML={{ __html: _val }} />
          </Descriptions.Item>
        );
      })}
    </Descriptions>
  );
};

export const EvaluateDetailPage = (props: {
  detail: Record<string, any>;
  selects: Record<string, any>;
}) => {
  const { detail, selects } = props;
  return (
    <Card title={'评价详情'}>
      <FormItem
        title="过程性记录"
        data={detail}
        lists={[
          {
            dataIndex: 'childTeacherInteraction',
            label: '幼儿与教师的语言互动',
            span: 4,
            render(data, key) {
              const val = data[key as any];
              return val
            },
          },
          {
            dataIndex: 'childTeacherInteractionPhotoResources',
            label: '照片',
            span: 4,
            type: 'file',
          },
          {
            dataIndex: 'childTeacherInteractionVideoResources',
            label: '音视频',
            span: 4,
            type: 'file',
          },
        ]}
      />
      <FormItem
        title="幼儿作品"
        data={detail}
        lists={[{ dataIndex: 'childArtworkResources', label: '', span: 4, type: 'file' }]}
      />
      <FormItem
        title="嵌入式儿童评估"
        data={detail}
        lists={[
          // @ts-ignore
          {
            dataIndex: 'interestScore',
            label: '幼儿对本活动的兴趣程度',
            span: 4,
            type: 'select',
            options: selects.SubjectActivityInterestScoreEnumDesc,
          },
          // @ts-ignore
          {
            dataIndex: 'difficultyScore',
            label: '本活动的难度',
            span: 4,
            type: 'select',
            options: selects.SubjectActivityDifficultyScoreEnumDesc,
          },
        ]}
      />
      <FormItem
        title="活动实施反思"
        data={detail}
        lists={[
          { dataIndex: 'activityHighlight', label: '活动的亮点', span: 4 },
          { dataIndex: 'activityProblem', label: '活动存在的问题', span: 4 },
          { dataIndex: 'improvementMeasure', label: '改进措施', span: 4 },
        ]}
      />
    </Card>
  );
};

export const ActivityDetail = (props: {
  detail: Record<string, any>;
  selects: Record<string, any>;
  subjectId:string | null;
  subjectStageId: string | null
}) => {
  const { detail, selects, subjectId } = props;
  const [options, setOptions] = useState(selects)

  const getList = async () => {
    try {
      const list = await getStateList({
        subjectId,
      });
      const _list = list.data?.map(item =>({...item,label: item.title, value: parseFloat(item.id)})) || []
      setOptions({
        ...selects,
        subjectStageId: _list
      })
    } catch (e) {
      setOptions({
        ...selects,
        subjectStageId: []
      })
    }
  };

  useEffect(() => {
    getList();
   
  }, [])


  return (
    <Card title={<Row>
      <Col style={{marginRight: 12}}>活动基本信息</Col>
   
    </Row>} style={{ marginBottom: 16 }}>
      <FormItem
        data={detail}
        lists={[
          { dataIndex: 'name', label: '活动名称' },
          {
            dataIndex: 'plannedGeneration',
            label: '预设生成',
            // @ts-ignore
            options: selects.plannedOrGeneration,
            type: 'select',
          },
          { dataIndex: 'author', label: '作者' },
          {
            dataIndex: 'organizationForm',
            label: '组织形式',
            // @ts-ignore
            options: selects.organization,
            type: 'select',
          },
          { dataIndex: 'durationInMinutes', label: '活动时长(分)' },
          {
            dataIndex: 'implementedAt',
            label: '实施时间',
            render: (data) => {
              return `${
                (data?.implementedAt && dayjs(data?.implementedAt).format('YYYY-MM-DD')) || ''
              } `;
            },
          },
          {
            dataIndex: 'subjectStageId',
            label: '阶段',
            options: options.subjectStageId,
            type: 'select',
          },
          // { dataIndex: 'keyQuestion', label: '关键问题', span: 4 },
          { dataIndex: 'activityIdea', label: '活动背景和目的', span: 4 },
          {
            dataIndex: 'matrices',
            label: '核心经验',
            span: 4,
            type: 'protable',
            columns: [
              {
                dataIndex: ['matrix1', 'title'],
                title: '领域',
                valueType: 'text',
                width: 60,
                search: false,
              },
              {
                dataIndex: ['matrix2', 'title'],
                title: '维度',
                valueType: 'text',
                width: 180,
                search: false,
              },
              {
                dataIndex: ['matrix3', 'title'],
                title: '子维度',
                valueType: 'text',
                width: 180,
                search: false,
              },
              {
                dataIndex: ['target', 'title'],
                title: '指标',
                valueType: 'text',
                width: 320,
                search: false,
              },
            ],
          },
          { dataIndex: 'activityKeyPoint', label: '活动重点', span: 4 },
          { dataIndex: 'activityDifficulty', label: '活动难点', span: 4 },
          { dataIndex: 'teachingObjective', label: '教学目标', span: 4 },
          { dataIndex: 'resourcesSupport', label: '教学准备、资源或环境支持', span: 4 },
          {
            dataIndex: 'electronicPreparationResources',
            label: '教学准备、资源或环境支持(附件)',
            span: 4,
            type: 'file',
          },
          // {
          //   dataIndex: 'physicalPreparationResources',
          //   label: '实物教学准备',
          //   span: 4,
          //   type: 'file',
          // },
          // { dataIndex: 'expectedOutcome', label: '预设成果或环创', span: 4 },
        ]}
      />

      <FormItem
        data={detail}
        lists={[
          {
            dataIndex: 'teachingSteps',
            label: '教学流程',
            span: 4,
            type: 'protable',
            columns: [
              {
                dataIndex: 'detailedProcess',
                title: '',
                valueType: 'text',
                width: 600,
                search: false,
              
                // ellipsis: true,
                render: (value: any) => {
                  return (
                    <Typography.Paragraph
                      ellipsis={{
                        rows: 5,
                        expandable: 'collapsible',
                      }}
                      style={{
                        marginBottom: 0,
                        whiteSpace: 'pre-wrap'
                      }}
                    >
                      {value}
                    </Typography.Paragraph>
                  );
                },
              },
             
            ],
          },
        ]}
      />
    </Card>
  );
};
export const getLists = async (params: { pid: number }) => {
  try {
    const res = await getMatrix(params);
    return res.data?.map((item: any) => ({ label: item.title, value: item.id }));
  } catch (e) {
    return [];
  }
};

const ActivityAllDetail = () => {
  const params = useParams();
  const id = params.id;
  const searchParams = new URLSearchParams(location.search);
  const subjectId = searchParams.get('subjectId');
  const subjectStageId = searchParams.get('subjectStageId');
  
  const [selects, setSelects] = useState({
    plannedOrGeneration: [],
    organization: [],
  });

  const getSelects = async () => {
    const data = await fetchSelects();
    setSelects(data as any);
  };
  // @ts-ignore
  const [detail, setDetail] = useState<Record<string, any>>({});
  const [form] = Form.useForm();

  const getDetail = async () => {
    try {
      const _detail = await getClassDetail({ id });
      const { matrices, teachingSteps } = _detail?.data || {};

      setDetail({
        ..._detail.data,
      });

      form.setFieldsValue({
        ...(_detail?.data || {}),
        matrices: matrices?.length ? matrices : [{}],
        teachingSteps: teachingSteps?.length ? teachingSteps : [{}],
      });
    } catch (e) {
      form.setFieldsValue({});
    }
  };

  useEffect(() => {
    if (id) {
      getSelects();
      getDetail();
    }
  }, [id]);


  const PageTitle = () => {
    const [context, setContext] = useState({prevActivityId: 0,
      nextActivityId: 0
    })


    useEffect(() => {
      getClassContext({
        id: params.id,
        subjectId: subjectId
      }).then(response => {
        setContext(response.data || {prevActivityId: 0, nextActivityId: 0})
      })
    }, [])

    return <Row>
      <Col style={{marginRight: 12, fontSize: 16}}>活动详情</Col>
      <Col>
        {
          context.prevActivityId ? <Button type="link" href={`/class/activity/detail/${context.prevActivityId}?subjectStageId=${subjectStageId}&subjectId=${subjectId}`} onClick={() => onViewOtherActivity(true)} style={{marginRight: 8}} size='small'>上一个</Button> : null
        }
       {
        context.nextActivityId ?  <Button type="link" href={`/class/activity/detail/${context.nextActivityId}?subjectStageId=${subjectStageId}&subjectId=${subjectId}`} onClick={() => onViewOtherActivity(true)} style={{marginRight: 8}} size='small' >下一个</Button> : null
       }
      </Col>
    </Row>
  }
  return (
    <PageContainer title={<PageTitle />} className="descriptionDetail" style={{ background: '#fff' }}>
      <ActivityDetail subjectStageId={subjectStageId} subjectId={subjectId || ''} detail={detail} selects={selects} />
      <EvaluateDetailPage detail={detail} selects={selects} />
    </PageContainer>
  );
};

export default ActivityAllDetail;
