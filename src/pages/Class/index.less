.clearBorderTable {
  th {
    font-weight: 400 !important;
  }
}
.tableHeader {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  .title {
    font-weight: 500;
  }
}

.detailForm {
  .ant-form-item-control {
    padding: 0 10px;
    font-size: 13px;
  }
  .ant-form-item-label label {
    font-weight: 600 !important;
    font-size: 15px;
  }
  .prospect {
    .ant-form-item-label label {
      font-weight: 600 !important;
      font-size: 13px;
    }
    .ant-form-item-control-input {
      justify-content: flex-start;
      width: 100%;
    }
  }
}
.editForm {
  .ant-form-item-label label {
    font-weight: 600 !important;
    font-size: 14px;
  }
}
.teachingSteps {
  .ant-pro-form-list-item,
  .ant-pro-form-list-container {
    width: 100% !important;
  }
  .ant-form-item-required {
    font-weight: 600 !important;
    font-size: 16px !important;
  }
}
.matrices {
  .ant-pro-form-group-title {
    font-weight: 600 !important;
    font-size: 15px;
    margin-left: -10px;
  }
}
.evaluate,
.activityDetail {
  .ant-form-item-label {
    label {
      font-weight: 600;
    }
  }
}

.columnTagsLeft {
  margin-left: 5px;
  padding: 0 3px;
  font-size: 11;
  height: 18px;
  line-height: 16px !important;
}
.columnTags {
  padding: 0 3px;
  font-size: 11;
  height: 18px;
  line-height: 16px !important;
}
.classList {
  .ant-pro-checkcard-body {
    padding: 10px !important;
  }
  .ant-list-item-meta-title {
    white-space: normal;
  }
}

.descriptionDetail {
  .ant-table-cell {
    font-size: 13px !important;
  }
  // .ant-image {
  //   width: 96px !important;
  // }
}
.noExpandDragSort {
  display: none;
}
.descriptionsCard {
  .ant-card-body {
    padding: 10px;
    border-top: 1px dashed #ccc;
  }
  .ant-card-meta-description {
    word-wrap: break-word;
    word-break: break-all;
    max-height: 66px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
  .ant-descriptions-item-content {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    align-items: center !important;
  }
}
.descriptionsImage {
  width: 100%;
  height: 160px;
  padding: 10px 0;

  .ant-image-error {
    img {
      width: 40px !important;
      height: auto !important;
      opacity: 0.4;
      height: auto;
    }
  }
  .ant-image {
    display: flex;
    justify-content: center !important;
    align-items: center !important;
    height: 100%;
  }

  img {
    width: 100%;
    max-height: 140px;
  }
}
.descriptionsModal {
  position: relative;

  .anticon-info-circle {
    display: none;
  }

  .ant-modal-confirm-btns,
  .descriptionsModalClose {
    position: absolute;
    top: 8px;
    right: 0;
    height: 42px;
    width: 100px;
    display: flex;
    justify-content: center;
    align-items: center;

    .ant-btn {
      background: transparent;
      box-shadow: none;
      margin-top: 0;
      &:active,
      &:hover {
        background-color: transparent;
      }
      &:focus-visible {
        outline: none;
      }
    }
    span {
      color: transparent;
    }
  }
}

.moreTh {
  height: 300px;
}
.matricesDetail {
  .ant-form-item-control {
    padding: 0;
  }
  .ant-pro-form-group-title {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
    font-weight: normal;
  }
  .ant-pro-form-group {
    display: flex;
    .ant-space {
      margin-left: 16px;
    }
  }
}

.text-red {
  color: red;
}
.switch-container {
  margin-left: auto;
  margin-top: -22px;
  width: fit-content;
  display: flex;
  align-items: center;
  span {
    margin-right: 10px;
  }
}
