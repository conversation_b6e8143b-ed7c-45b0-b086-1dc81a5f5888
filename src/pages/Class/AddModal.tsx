import { DictionaryCategory } from '@/services/constants';
import { PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProFormDateRangePicker,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Button, Col, Form, Row } from 'antd';
import { fetchCategory, getTerm } from './util';

export default (props: {
  id?: string;
  isDetail?: boolean;
  onSubmit?: (params: Record<string, any>) => void;
  data?: Record<string, any>;
}) => {
  const { isDetail, id, data } = props;
  const [form] = Form.useForm<{ startTimer: string; categoryId: string }>();

  const getTitle = () => {
    if (isDetail) {
      return data?.title || '课程详情';
    }
    if (id) {
      return '编辑课程';
    }
    return '新增课程';
  };

  const getButtonText = () => {
    if (id) {
      return `编辑基本信息`;
    }
    return (
      <>
        <PlusOutlined />
        新增课程
      </>
    );
  };
  return (
    <ModalForm<{
      startTimer: string;
      categoryId: string;
    }>
      key={data?.id || Date.now()}
      {...(isDetail && {
        readonly: true,
      })}
      
      //  readonly
      title={`${getTitle()}`}
      trigger={
        <Button
          style={{
            marginLeft: 16,
          }}
          size="small"
          type="primary"
        >
          {getButtonText()}
        </Button>
      }
      form={form}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        // onCancel: () => console.log('run'),
        afterOpenChange: (open) => {
          if (open) {
            console.log('%c Line:73 🥑 data', 'color:#f5ce50', data);

            form.setFieldsValue({
              ...(data || {}),
            });
          } else {
            form.setFieldsValue({});
          }
        },
      }}
      submitTimeout={2000}
      initialValues={data}
      onFinish={async (values) => {
        const { startTimer, ...rest } = values;
        // @ts-ignore
        const data: {
          startAt?: string;
          endAt?: string;
        } = {
          ...rest,
        };
        if (startTimer?.length) {
          const [startAt, endAt] = startTimer;
          data.startAt = startAt;
          data.endAt = endAt;
        } else {
          data.startAt = '';
          data.endAt = '';
        }
        const res = await props.onSubmit?.(data);
        return res;
      }}
    >
      <Row gutter={8}>
        <Col span={12}>
          <ProFormText
            name="title"
            rules={[
              {
                required: true,
                message: '请选择主题名称',
              },
            ]}
            label="主题名称"
            placeholder="请输入主题名称"
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            rules={[
              {
                required: true,
                message: '请选择课程类别',
              },
            ]}
            label="课程类别"
            name="categoryId"
            request={async () => {
              const res = await fetchCategory(DictionaryCategory.Category);
              return res?.map((item) => ({ ...item, value: parseInt(item.value) })) || [];
            }}
          />
        </Col>
        {/* <Col span={12}>
          <ProFormSelect
            rules={[
              {
                required: true,
                message: '请选择课程标签',
              },
            ]}
            fieldProps={{
              mode: 'multiple',
            }}
            label="课程标签"
            name="tagIds"
            request={async () => {
              return fetchCategory(DictionaryCategory.Tag);
            }}
          />
        </Col> */}
        <Col span={12}>
          <ProFormDateRangePicker name={['startTimer']} label="实施时间" />
        </Col>
        <Col span={12}>
          <ProFormSelect
            rules={[
              {
                required: true,
                message: '请选择学期',
              },
            ]}
            label="学期"
            name="term"
            request={async () => {
              return getTerm();
            }}
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            rules={[
              {
                required: true,
                message: '请选择年级',
              },
            ]}
            label="年级"
            name="gradeId"
            request={async () => {
              return fetchCategory(3)?.then((res) =>
                res?.map((item) => ({ ...item, value: parseInt(item.value) })),
              );
            }}
          />
        </Col>
        <Col span={12}>
          <ProFormText name="creator" label="创作者" />
        </Col>
      </Row>
      {/* TODO:  字段*/}

      {/* <ProForm.Group>
        <UploadImageComponent
          key="upload"
          fileName="instructionIds"
          label="图片"
          max={10}
          client={client}
          fileList={instructionList}
          setClient={setClient}
          setFileList={setInstructionList}
        />
      </ProForm.Group> */}
    </ModalForm>
  );
};
