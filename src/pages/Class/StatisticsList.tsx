/* eslint-disable guard-for-in */
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';

import { useParams } from '@umijs/max';


import React, { useEffect, useRef, useState } from 'react';
import { ChildEntity } from '../Child/data';
import { fetchSubjectStatistics } from '@/services/class';

class Tips extends React.Component {
  render() {
    return <div>统计数据有延迟，每天凌晨统计前一日数据</div>;
  }
}

function sortActivities(activities: any[]) {
  if(!activities.length) {
    return []
  }
  let stageTitle = ''
  const result: { type?: string; text: string, index: number, id: number }[] = []
  let index = 1
  activities.forEach(a => {
    if(a.stageTitle !== stageTitle) {
      stageTitle = a.stageTitle
      index = 1
      result.push({
        type: 'stageTitle',
        text: a.stageTitle,
        id: a.id,
        index: 0
      })
    }
    result.push({
      text: a.title,
      index: index++,
      id: a.id
    })
  })

  return result
}
export interface StatisticsProps {
  matrix1Id: number;
  matrix1Title: string;
  matrix2Id: number;
  matrix2Title: string;
  matrix3Id: number;
  matrix3Title: string;
  children: ChildEntity[];
  matrix1Count?: number;
  matrix2Count?: number;
  evaluationId: number;
}

const StatisticsList: React.FC = () => {
  const params = useParams();
  const actionRef = useRef<ActionType>();
  const [evaluationData, setEvaluationData] = useState<StatisticsProps[]>([]);
  const baseColumn: ProColumns<API.RuleListItem>[] = [
    {
      title: '领域',
      dataIndex: 'matrix1Title',
      fixed: 'left',
      search: false,
      width: 100,
    },
    {
      title: '维度',
      search: false,
      fixed: 'left',
      width: 200,
      dataIndex: 'matrix2Title',
    },
    {
      title: '子维度',
      search: false,
      fixed: 'left',
      width: 300,
      ellipsis: true,
      dataIndex: 'matrix3Title',
    },
    {
      title: '主题活动出现次数',
      width: 160,
      sorter(a: any, b: any) {
        return Number(a.count) - Number(b.count);
      },
      dataIndex: 'count',
    },
    {
      title: '对应活动',
      search: false,
      dataIndex: 'activities',
      render(dom, entity: any) {
        const activities = sortActivities(entity.activities || []);
        if(!activities.length) {
          return <div>-</div>
        }


        return <div>
          {
            activities?.map((a) => a.type === 'stageTitle' ? <b key={a.text}>{a.text}</b> : <div key={a.text}>{a.id}、{a.text}</div>)
          }
        </div>
      },
    }
  ];

  useEffect(() => {
    fetchSubjectStatistics(params.id as any).then((res) => {
      const data = res.data;
      if (data instanceof Array) {
        setEvaluationData(data);
        actionRef.current?.reload();
      }
    });
  }, [params.id]);


  return (
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        scroll={{ y: 500, x: 'max-content' }}
        pagination={false}
        expandable={{ showExpandColumn: false }}
        toolBarRender={() => [
          <Tips key={'tips'} />
        ]}
        search={false}
        dataSource={evaluationData as any}
        columns={baseColumn}
      />
   
  );
};

export default StatisticsList;


