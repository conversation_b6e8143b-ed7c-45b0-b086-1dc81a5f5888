import { fetchSubjectDetail, getMatrix, updateSubject } from '@/services/class';
import { CloseCircleOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormGroup,
  ProFormList,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Button, Col, Form, Row, message } from 'antd';
import { useEffect, useState } from 'react';
import './index.less';
import { FormItem } from './ActivityDetail';

export const getLists = async (params: { pid: number }) => {
  try {
    const res = await getMatrix(params);
    return res.data?.map((item) => ({ label: item.title, value: item.id }));
  } catch (e) {
    return [];
  }
};

const TabTextForm = (props: { isAIOK: boolean }) => {
  const { isAIOK } = props;
  const params = useParams();
  const subjectId = params.id;
  const [detail, setDeatil] = useState({});
  const [isDetail, setIsDetail] = useState(true);

  const [matrix, setMatrix] = useState([]);

  // 获取领域
  const fetchList = async () => {
    const list = await getLists({ pid: 0, current: 1, pageSize: 1000 });
    setMatrix(list);
  };

  const [form] = Form.useForm<{ name: string; company: string }>();

  const fetchDetail = async () => {
    try {
      const res = await fetchSubjectDetail(subjectId);
      setDeatil(res.data || {});
      form.setFieldsValue({
        ...res.data,
      });
    } catch (e) {
      setDeatil({});
    }
  };

  useEffect(() => {
    fetchList();
  }, []);

  useEffect(() => {
    if (subjectId) {
      fetchDetail();
    }
  }, [subjectId, isDetail]);

  const onSubmit = async (params) => {
    try {
      const res = await updateSubject({
        ...params,
        id: subjectId,
      });
      if (res?.status === 0) {
        message.success('更新成功');
        fetchDetail();
        setIsDetail(true);
        return true;
      } else {
        message.error('更新失败，请重试');
      }
    } catch (error) {
      console.log(error);
      message.error('更新失败，请重试');
    }
  };
  return (
    <>
      <div
        style={{
          display: 'flex',
          justifyContent: 'flex-end',
        }}
      >
        {isDetail && (
          <Button
            type="primary"
            size="middle"
            disabled={isAIOK}
            style={{ marginBottom: 10 }}
            onClick={() => setIsDetail(false)}
          >
            编辑
          </Button>
        )}
      </div>
      {isDetail ? (
        <FormItem
          data={detail}
          lists={[
            { dataIndex: 'origin', label: '缘起', span: 4 },
            {
              dataIndex: 'prospect',
              label: '教师先行',
              span: 4,
              type: 'protable',
              columns: [
                {
                  dataIndex: 'question',
                  title: '教师的困惑',
                  valueType: 'text',
                  width: 300,
                  search: false,
                },
                {
                  dataIndex: 'answer',
                  title: '自学知识',
                  valueType: 'text',
                  width: 500,
                  search: false,
                },
              ],
            },
            { dataIndex: 'studyAnalyze', label: '学情分析', span: 4 },
            { dataIndex: 'interest', label: '幼儿兴趣', span: 4 },
            { dataIndex: 'objectives', label: '课程目标', span: 4 },
            { dataIndex: 'keynote', label: '课程重点', span: 4 },
            { dataIndex: 'difficulties', label: '课程难点', span: 4 },
            { dataIndex: 'resourceUtilization', label: '资源使用', span: 4 },
            { dataIndex: 'materialPlacement', label: '材料投放', span: 4 },
            { dataIndex: 'environmentSuggest', label: '环创建议', span: 4 },
            { dataIndex: 'completionActivity', label: '结题活动', span: 4 },
            // {
            //   dataIndex: 'matrices',
            //   label: '主题目标',
            //   span: 4,
            //   type: 'protable',
            //   columns: [
            //     {
            //       dataIndex: ['matrix1Id'],
            //       title: '领域',
            //       valueType: 'select',
            //       width: 200,
            //       search: false,
            //       request: () => {
            //        return matrix
            //       },
            //     },
            //     {
            //       dataIndex: ['matrix2Id'],
            //       title: '维度',
            //       valueType: 'select',
            //       width: 200,
            //       search: false,
            //       request: async (res, data, value) => {
            //         const { record } = data;
            //         const list = await getLists({ pid: record.matrix1Id, current: 1, pageSize: 0 });
            //         return list;
            //       },
            //     },
            //     {
            //       dataIndex: ['matrix3Id'],
            //       title: '子维度',
            //       valueType: 'select',
            //       width: 400,
            //       search: false,
            //       request: async (res, data) => {
            //         const { record } = data;
            //         const list = await getLists({ pid: record.matrix2Id, current: 1, pageSize: 0 });
            //         return list;
            //       },
            //     },
            //   ],
            // },
          ]}
        />
      ) : (
        <ProForm<{
          name: string;
          company: string;
        }>
          className={`formInfo ${(isDetail && 'detailForm') || 'editForm'}`}
          layout={'vertical'}
          submitter={{
            searchConfig: {
              resetText: '取消',
            },
            onReset: () => {
              setIsDetail(true);
            },
          }}
          form={form}
          autoFocusFirstInput
          initialValues={detail}
          onFinish={async (values) => {
            onSubmit(values);
            return true;
          }}
        >
          {/* <ProForm.Group> */}
          <Row>
            <Col span={24}>
              <ProFormTextArea
                name="origin"
                label="缘&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;起"
                placeholder="请输入主题/项目缘起，可以思考：为什么要做这个主题/项目？这个主题/项目是基于幼儿什么样的兴趣？幼儿有什么发展需求？幼儿有什么前期经验？"
              />
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <ProFormList
                name="prospect"
                label={
                  <div style={{ fontWeight: '700', fontSize: isDetail ? 15 : 16 }}>教师先行</div>
                }
                copyIconProps={false}
                deleteIconProps={{
                  Icon: CloseCircleOutlined,
                  tooltipText: '不需要这行了',
                }}
                className="prospect"
              >
                <ProFormGroup key="prospect">
                  <ProFormText
                    layout={'vertical'}
                    placeholder="请输入背景知识相关问题"
                    width="sm"
                    name="question"
                    label="教师的困惑"
                  />
                  <ProFormTextArea
                    layout={'vertical'}
                    width="lg"
                    name="answer"
                    placeholder="请输入背景知识的答案"
                    label="自学知识"
                  />
                </ProFormGroup>
              </ProFormList>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <ProFormTextArea
                name="studyAnalyze"
                label="学情分析"
                placeholder="请输入本主题的学情分析"
              />
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <ProFormTextArea
                style={{ minHeight: 120 }}
                name="interest"
                label="幼儿兴趣"
                placeholder={`可以思考：幼儿前期提出了什么问题？幼儿喜欢观察和讨论过什么话题？可以将幼儿的兴趣点逐个列出，例如：
                            1.小鸡头顶上的是什么？
                            2.小鸡最喜欢吃什么？
                            3.幼儿对于小鸡在幼儿园要有一个专属的家非常感兴趣，说：“小鸡可以在幼儿安家啦！”
                            4.XXX
                          `}
              />
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <ProFormTextArea
                name="objectives"
                label="课程目标"
                placeholder="请输入本主题的课程目标"
              />
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <ProFormTextArea
                name="keynote"
                label="课程重点"
                placeholder="请输入本主题的课程重点"
              />
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <ProFormTextArea
                name="difficulties"
                label="课程难点"
                placeholder="请输入本主题的课程难点"
              />
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <ProFormTextArea
                name="resourceUtilization"
                label="资源使用"
                placeholder="请输入本主题的资源使用"
              />
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <ProFormTextArea
                name="materialPlacement"
                label="材料投放"
                placeholder="请输入本主题的材料投放"
              />
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <ProFormTextArea
                name="environmentSuggest"
                label="环创建议"
                placeholder="请输入本主题的环创建议"
              />
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <ProFormTextArea
                name="completionActivity"
                label="结题活动"
                placeholder="请输入本主题的结题活动"
              />
            </Col>
          </Row>
          {/* <ProFormList
            name="matrices"
            className={'matrices'}
            // label="指南目标项"
            copyIconProps={false}
            rules={[
              {
                required: true,
                message: '请添加主题目标',
              },
            ]}
            creatorButtonProps={{
              // position: 'top',
              creatorButtonText: '新增主题目标',
            }}
            // deleteIconProps={false}
          >
            <ProFormGroup key="matrices" title="主题目标">
              <ProFormSelect
                rules={[
                  {
                    required: true,
                    message: '请选择领域',
                  },
                ]}
                name="matrix1Id"
                label={isDetail ? '领域' : ''}
                placeholder="请选择领域"
                showSearch
                options={matrix}
              />
              <ProFormSelect
                dependencies={['matrix1Id']}
                width={'sm'}
                rules={[
                  {
                    required: true,
                    message: '请选择维度',
                  },
                ]}
                showSearch
                placeholder={'请选择维度'}
                label={isDetail ? '维度' : ''}
                name="matrix2Id"
                request={async (params) => {
                  if (!params?.matrix1Id) {
                    return [];
                  }
                  return await getLists({ pid: params.matrix1Id, current: 1, pageSize: 1000 });
                }}
              />
              <ProFormSelect
                dependencies={['matrix2Id']}
                showSearch
                rules={[
                  {
                    required: true,
                    message: '请填写子维度',
                  },
                ]}
                label={isDetail ? '子维度' : ''}
                width={'lg'}
                placeholder="请选择子维度"
                name="matrix3Id"
                request={async (params) => {
                  if (!params?.matrix2Id) {
                    return [];
                  }
                  return await getLists({ pid: params.matrix2Id, current: 1, pageSize: 1000 });
                }}
              />
            </ProFormGroup>
          </ProFormList> */}
        </ProForm>
      )}
    </>
  );
};

export default TabTextForm;
