/* eslint-disable guard-for-in */
import UploadImageComponent from '@/components/UploadImage';
import {
  getClassDetail,
  getMatrix,
  getResourceByTimer,
  getStateList,
  getTarget,
  updateActivity,
} from '@/services/api';
import { CloseCircleOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';

import QRCode from 'react-qr-code';

// import { fetchClient } from '@/services/fileUpload';
// import { checkPositiveInteger, checkPrice, checkWeight, formatOption } from '@/services/utils';
import {
  PageContainer,
  ProForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormGroup,
  ProFormList,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useLocation, useParams } from '@umijs/max';
import { Button, Card, Col, Form, Modal, Row, message } from 'antd';
import { useEffect, useState } from 'react';
import { ActivityDetail } from './ActivityDetail';
import ToIphoneButton from '@/components/UploadImage/ToIphoneButton';
import { FetchSelectProps, fetchSelects } from './util';
import './index.less';


const getTargetList = async (params) => {
  try {
    const res = await getTarget(params);

    return res.data?.map((item) => ({ label: item.title, value: item.id }));
  } catch (e) {
    return [];
  }
};

export const getLists = async (params: { pid: number }) => {
  try {
    const res = await getMatrix(params);
    return res.data?.map((item) => ({ label: item.title, value: item.id }));
  } catch (e) {
    return [];
  }
};

const UpdateActivityDrawer = () => {
  const params = useParams();
  const id = params.id;
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const subjectStageId = searchParams.get('subjectStageId');
  const subjectId = searchParams.get('subjectId');
  const gradeId = searchParams.get('gradeId');
  const [selects, setSelects] = useState<FetchSelectProps>({
    plannedOrGeneration: [],
    organization: [],
  });

  const getSelects = async () => {
    const data = await fetchSelects();
    setSelects(data);
  };

  const [modalValue, setModalValue] = useState({
    url: '',
    timestamp: 0,
    fileName: '',
  });

  const [client, setClient] = useState<any>();
  const [ugcList, setUgcList] = useState<API.FileItem[]>([]);
  const [headerList, setHeaderList] = useState<API.FileItem[]>([]);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [detail, setDetail] = useState({});
  const [matrix, setMatrix] = useState([]);
  const [form] = Form.useForm();

  // 获取领域
  const fetchList = async () => {
    const list = await getLists({ pid: 0, current: 1, pageSize: 1000 });
    setMatrix(list);
  };

  const getDetail = async () => {
    try {
      const _detail = await getClassDetail({ id });
      const {
        physicalPreparationResources,
        electronicPreparationResources,
        matrices,
        teachingSteps,
        // organizationForm,
      } = _detail?.data || {};

      const ugcList = physicalPreparationResources?.map((item) => ({
        id: item.id,
        uri: item.uri,
        filename: item.filename,
        category: item.category,
      }));

      const headerList = electronicPreparationResources?.map((item) => ({
        id: item.id,
        uri: item.uri,
        filename: item.filename,
        category: item.category,
      }));
      setHeaderList(headerList || []);
      setUgcList(ugcList || []);
      setDetail({
        ..._detail.data,
        subjectStageId: parseFloat(_detail.data.subjectStageId)
      });
      fetchList();
      form.setFieldsValue({
        ...(_detail?.data || {}),
        matrices: matrices?.length ? matrices : [{}],
        subjectStageId: parseFloat(_detail.data.subjectStageId),
        headerId: headerList,
        ugcIds: ugcList,
        teachingSteps: teachingSteps?.length ? teachingSteps : [{}],
      });
    } catch (e) {
      setHeaderList([]);
      setUgcList([]);
      form.setFieldsValue({});
    }
  };

  const getIphoneFile = async () => {
    const result = await getResourceByTimer({
      timestamp: modalValue.timestamp,
    });
    if (result.data?.length) {
      setHeaderList([...(headerList || []), ...(result.data || [])]);
      form?.setFieldsValue({
        headerId: [...(headerList || []), ...(result.data || [])],
      });
      message.success('同步图片成功');
      setModalValue({});
    } else {
      message.success('未获取到同步文件！！');
    }
  };

  const handleFinish = async (value) => {
    const isValid = await form.validateFields();

    if (isValid) {
      // 表单校验通过后的处理逻辑
      const { headerId, ugcIds, ...rest } = value;

      const result = {
        ...rest,
      };
      if (ugcIds?.length) {
        result['physicalPreparationResourceIds'] = ugcList?.map((item) => item?.id);
      }

      if (headerId?.length) {
        result['electronicPreparationResourceIds'] = headerList?.map((item) => item?.id);
      }

      try {
        const res = await updateActivity({ ...result, subjectStageId, subjectId, id });
        if (res?.status === 0) {
          message.success(id ? '更新成功' : '新建成功');
          const newSearchParams = new URLSearchParams(location.search);
          newSearchParams.set('isDetail', 'true');
          const newLocation = {
            ...location,
            search: newSearchParams.toString(),
          };
          history.push(newLocation);
          return true;
        }
      } catch (error) {
        console.log(error);
        message.error(
          id ? error?.message || '更新失败，请重试' : error?.message || '新建失败，请重试',
        );
      }
    }
  };

  const onFinishFailed = () => {
    message.error('表单校验失败，请检查输入');
  };

  const isDetail = !!searchParams.get('isDetail');

  useEffect(() => {
    if (id) {
      getSelects();

      getDetail();
    }
  }, [id, isDetail]);

  const getList = async () => {
    try {
      const list = await getStateList({
        subjectId,
      });
      const _list = list.data?.map(item =>({...item,label: item.title, value: parseFloat(item.id)})) || []
      return _list;
    } catch (e) {
      return []
    }
  };

  return (
    <PageContainer title={isDetail ? '活动详情' : '编辑活动'} style={{ background: '#fff' }}>
      <Modal
        title="通过手机上传"
        open={!!modalValue.url}
        onCancel={() => {
          setModalValue({});
        }}
        okText="关闭"
        destroyOnClose
        footer={false}
      >
        <QRCode
          style={{
            margin: '10px auto 20px',
            display: 'block',
          }}
          value={modalValue.url} //生成二维码的内容
          size={156} //二维码尺寸
          fgColor="#000000" //二维码颜色
        />
        <Button
          onClick={() => {
            getIphoneFile();
          }}
          style={{
            margin: '0 auto',
            display: 'block',
          }}
        >
          同步到电脑
        </Button>
      </Modal>
      {isDetail ? (
        <ActivityDetail detail={detail} subjectId={subjectId || ''} selects={selects} />
      ) : (
        <ProForm
          style={{
            margin: 'auto',
            marginTop: 8,
          }}
          {...((isDetail && { submitter: false, readonly: true }) || {
            submitter: {
              resetButtonProps: {
                style: {
                  display: 'none', // 隐藏提交按钮
                },
              },
            },
          })}
          className={`activityDetail ${(isDetail && 'detailActivityDetail') || ''}`}
          clearOnDestroy
          form={form}
          name="basic"
          layout="vertical"
          onFinish={(value) => handleFinish(value)}
          onFinishFailed={onFinishFailed}
        >
          <Card bordered={false} title={'活动基本信息'}>
            <Row gutter={8}>
              <Col span={12}>
                <ProFormText
                  rules={[
                    {
                      required: true,
                      message: '请填写活动名称',
                    },
                  ]}
                  label="活动名称"
                  name="name"
                />
              </Col>
              <Col span={12}>
                <ProFormSelect
                  rules={[
                    {
                      required: true,
                      message: '请选择预设生成',
                    },
                  ]}
                  name="plannedGeneration"
                  label="预设生成"
                  options={selects.plannedOrGeneration || []}
                />
              </Col>
              <Col span={12}>
                <ProFormText
                  rules={[
                    {
                      required: true,
                      message: '请填写作者',
                    },
                  ]}
                  label="作者"
                  name="author"
                />
              </Col>
              <Col span={12}>
                <ProFormSelect
                  rules={[
                    {
                      required: true,
                      message: '请选择组织形式',
                    },
                  ]}
                  // disabled
                  name="organizationForm"
                  label="组织形式"
                  options={selects.organization || []}
                />
              </Col>
              <Col span={12}>
                <ProFormDigit
                  fieldProps={{
                    addonAfter: '分钟',
                  }}
                  rules={[
                    {
                      required: true,
                      message: '请输入活动时长',
                    },
                  ]}
                  // width="xs"
                  name="durationInMinutes"
                  label={`${isDetail ? '活动时长(分钟)' : '活动时长'}`}
                  placeholder="请输入"
                  min={0}
                />
              </Col>
              <Col span={12}>
                <ProFormDatePicker
                  rules={[
                    {
                      required: true,
                      message: '请选择实施时间',
                    },
                  ]}
                  width="md"
                  name="implementedAt"
                  label="实施时间"
                  placeholder="请选择实施时间"
                />
              </Col>
              <Col span={12}>
                <ProFormSelect
                  name="subjectStageId"
                  label='阶段'
                  request={getList}
                />

              </Col>
              {/* <Col span={24}>
                <ProFormText placeholder="请输入本活动的关键问题，通过关键问题可以迅速了解本活动探究的主要内容，例如，“普通的黄泥土可以制作陶瓷碗吗？如果用黄泥土捏模型，你发现了什么问题？为什么不能用黄泥土做陶瓷碗？”" label="关键问题" name="keyQuestion" />
              </Col> */}
              <Col span={24}>
                <ProFormTextArea name="activityIdea" label="活动背景和目的" placeholder={`请输入活动背景和目的，可以思考：为什么要开展这个活动？上一个活动有什么悬而未解的问题引出了这个活动？这个活动是基于幼儿什么样的兴趣或需求？，例如，“幼儿第一次尝试做的陶艺模型出现了裂痕、款式单一、容易破碎，因此幼儿将在本活动探讨这些问题的解决方案并再次尝试。”
`} />
              </Col>
            </Row>

            <Row>
              <Col span={24}>
                <ProFormTextArea
                  rules={[
                    {
                      required: true,
                      message: '请填写教学目标',
                    },
                  ]}
                  label="教学目标"
                  name="teachingObjective"
                  placeholder="请输入认知、情感、技能目标，建议分点逐个列出"
                // fieldProps={{ maxLength: 200 }}
                />
              </Col>
              <Col span={24}>
                <ProFormText
                  // rules={[
                  //   {
                  //     required: true,
                  //     message: '请填写活动重点',
                  //   },
                  // ]}
                  label="活动重点"
                  name="activityKeyPoint"
                  placeholder="请输入活动重点"
                // fieldProps={{ maxLength: 200 }}
                />
              </Col>
              <Col span={24}>
                <ProFormText
                  // rules={[
                  //   {
                  //     required: true,
                  //     message: '请填写活动难点',
                  //   },
                  // ]}
                  label="活动难点"
                  name="activityDifficulty"
                  placeholder="请输入活动难点"
                // fieldProps={{ maxLength: 200 }}
                />
              </Col>
              
            </Row>
            <ProFormList
              name="matrices"
              label="核心经验"
              copyIconProps={false}
              rules={[
                {
                  required: true,
                  message: '请添加核心经验',
                },
              ]}
              alwaysShowItemLabel
              creatorButtonProps={{
                // position: 'top',
                creatorButtonText: '新增核心经验',
              }}
              {...(isDetail && { alwaysShowItemLabel: true })}
            // deleteIconProps={false}
            >
              <ProFormGroup key="matrices">
                <ProFormSelect
                  rules={[
                    {
                      required: true,
                      message: '请选择领域',
                    },
                  ]}
                  width={'sm'}
                  name="matrix1Id"
                  label={isDetail ? '领域' : '请选择领域'}
                  placeholder="请选择领域"
                  showSearch
                  // fieldProps={{
                  //   labelInValue: true,
                  // }}
                  options={matrix}
                />
                <ProFormSelect
                  dependencies={['matrix1Id']}
                  width={'sm'}
                  rules={[
                    {
                      required: true,
                      message: '请选择维度',
                    },
                  ]}
                  placeholder={'请选择维度'}
                  showSearch
                  label={isDetail ? '维度' : '请选择维度'}
                  name="matrix2Id"
                  request={async (params) => {
                    if (!params?.matrix1Id) {
                      return [];
                    }
                    return await getLists({ pid: params.matrix1Id, current: 1, pageSize: 1000 });
                  }}
                />
                <ProFormSelect
                  dependencies={['matrix2Id']}
                  showSearch
                  rules={[
                    {
                      required: true,
                      message: '请选择子维度',
                    },
                  ]}
                  label={isDetail ? '子维度' : '请选择子维度'}
                  width={'lg'}
                  placeholder="请选择子维度"
                  name="matrix3Id"
                  request={async (params) => {
                    if (!params?.matrix2Id) {
                      return [];
                    }
                    return await getLists({ pid: params.matrix2Id, current: 1, pageSize: 1000 });
                  }}
                />
                <ProFormSelect
                  showSearch
                  width={'xl'}
                  dependencies={['matrix3Id']}
                  rules={[
                    {
                      required: true,
                      message: '请选择指标',
                    },
                  ]}
                  placeholder="请选择指标"
                  label={isDetail ? '指标' : '请选择指标'}
                  name="targetId"
                  request={async (params) => {
                    if (!params?.matrix3Id) {
                      return [];
                    }
                    const res = await getTargetList({
                      matrix3Id: params.matrix3Id,
                      current: 1,
                      pageSize: 1000,
                      gradeId,
                    });

                    return res;
                  }}
                />
              </ProFormGroup>
            </ProFormList>


            <Row>
            <Col span={24}>
                <ProFormTextArea
                  // rules={[
                  //   {
                  //     required: true,
                  //     message: '请填写资源和环境支持',
                  //   },
                  // ]}
                  label="教学准备、资源或环境支持"
                  placeholder="请输入使用到的教具、资源或环境，包括但不限于：教具、玩具、实物、ppt、图画书、图片、音视频、家长资源、园所资源、社区资源、班级环境、园所环境等"
                  name="resourcesSupport"
                />
              </Col>
              <UploadImageComponent
                key="upload"
                fileName="headerId"
                label={
                  <div style={{ display: 'flex' }}>
                    <div>教学准备、资源或环境支持(附件)</div>
                    <ToIphoneButton form={form} setList={setHeaderList} list={headerList} fileName='headerId' />
                  </div>
                }
                max={10}
                client={client}
                fileList={headerList}
                setClient={setClient}
                setFileList={setHeaderList}
                edit={!isDetail}
              // accept=".png,.jpg,.jpeg,.gif"
              />
            </Row>
            {/* <Row>
              <UploadImageComponent
                key="upload"
                fileName="ugcIds"
                label={
                  <div style={{ display: 'flex' }}>
                    <div>实物教学准备</div>
                    <ToIphoneButton form={form} setList={setUgcList} list={ugcList} fileName='ugcIds' />
                  </div>
                }
                max={10}
                client={client}
                fileList={ugcList}
                setClient={setClient}
                setFileList={setUgcList}
                edit={!isDetail}
              />
            </Row>
            <Row>
              <Col span={24}>
                <ProFormTextArea
                  // rules={[
                  //   {
                  //     required: true,
                  //     message: '请填写预设成果或环创',
                  //   },
                  // ]}
                  label="预设成果或环创"
                  placeholder="请输入本活动结束后，预计产出的幼儿作品、教学成果或环境创设等"
                  name="expectedOutcome"
                />
              </Col>
            </Row> */}
          </Card>

          <Card style={{ marginBottom: 10 }}>
            <ProFormList
              name="teachingSteps"
              label="教学流程"
              copyIconProps={false}
              alwaysShowItemLabel={true}
              deleteIconProps={{
                Icon: CloseCircleOutlined,
                tooltipText: '不需要这行了',
              }}
              creatorButtonProps={{
                creatorButtonText: '新增环节',
              }}
              className="teachingSteps"
              itemRender={(doms, listMeta) => <Col span={24}>
              <ProFormTextArea label={`环节${listMeta.index + 1}`} placeholder="请输入具体教学流程" name="detailedProcess" />
            </Col>}
            >
              
            </ProFormList>
          </Card>
        </ProForm>
      )}
    </PageContainer>
  );
};

export default UpdateActivityDrawer;
