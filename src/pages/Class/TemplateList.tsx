import { getTemplateList } from '@/services/apis';
import { useGetEnumsByKey } from '@/services/enums';
import { useModel } from '@umijs/max';
import { Select } from 'antd';

import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

/** 状态 */
export enum StateStatus {
  // 停用
  Off = 0,
  // 启用
  On = 1,
}

const TemplateList = forwardRef((props: any, ref: any) => {
  const [subjectCategoryOptions, subjectTypeOptions] = useGetEnumsByKey([
    'DocumentTemplateCategoryEnumDesc',
    'SubjectTypeEnumDesc',
  ]);
  const { initialState } = useModel('@@initialState');
  const [templateId, setTemplateId] = useState();
  const [options, setOptions] = useState([]);
  const { index = 0 } = props;

  useImperativeHandle(ref, () => ({
    templateId,
  }));

  useEffect(() => {
    console.log(Object.keys(subjectCategoryOptions), 'Object.keys(subjectCategoryOptions');

    getTemplateList({
      documentTemplateCategory: Object.keys(subjectCategoryOptions)[index],
      subjectType: Object.keys(subjectTypeOptions)[0],
      subjectId: initialState?.currentUser?.currentSchoolId,
    }).then((response) => {
      console.log(response);
      const op = response.data.map((d: any) => ({ label: d.name, value: d.id }));
      setOptions(op);
      if (op.length) {
        setTemplateId(op[0].value);
      }
    });
  }, []);

  return (
    <div>
      <Select options={options} value={templateId} onChange={setTemplateId}></Select>
    </div>
  );
});

export default TemplateList;
