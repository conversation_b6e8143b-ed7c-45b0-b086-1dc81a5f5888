import { TermColumn } from '@/components/CommonColumn/TermColumn';
import { getChildList } from '@/services/child';
import { addChildReportList } from '@/services/childReport';
import { PageContainer, ProForm, ProFormSelect } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { useEffect } from 'react';

import { ChildAddEntity } from '@/services/childReport';
import { message } from 'antd';

const ChildrenReportEdit = () => {
  const { initialState } = useModel('@@initialState');

  const handleSubmit = async (values: ChildAddEntity) => {
    addChildReportList(values).then((res) => {
      console.log(res);
      if (res.status === 0) {
        message.success('添加成功');
      }
    });
  };

  useEffect(() => {}, []);
  return (
    <PageContainer style={{ background: '#fff' }}>
      <ProForm
        initialValues={{
          term: initialState?.currentClass,
        }}
        onFinish={async (values) => {
          console.log(values);
          await handleSubmit({
            classId: values.term[0],
            term: values.term[1],
            childId: values.childId,
          });
        }}
        submitter={{
          resetButtonProps: {
            style: {
              // display: 'none', // 隐藏提交按钮
            },
          },
        }}
      >
        <TermColumn name="term" disabled />
        <ProFormSelect
          disabled={true}
          showSearch={true}
          request={(props: any) => {
            return getChildList({
              title: props.keyWords,
            }).then((res) => {
              return res.data.map((r: any) => ({ label: r.title, value: r.id }));
            });
          }}
          name="childId"
          label="儿童姓名"
        ></ProFormSelect>
      </ProForm>
    </PageContainer>
  );
};

export default ChildrenReportEdit;
