import {
  PageContainer,
  ProForm,
  ProFormInstance,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useModel, useParams } from '@umijs/max';
import { useEffect, useRef, useState } from 'react';

import { fetchChildReportDetail, updateChildReportDetail } from '@/services/childReport';
import { Button, message } from 'antd';
import { parse } from 'querystring';
import { useGetEnumOptionsByKey } from '@/services/enums';

const ChildrenReportEdit = () => {
  const formRef = useRef<ProFormInstance>();
  const params = useParams();
  const query = parse(location.href.split('?')[1]);
  const [isEdit, setIsEdit] = useState(false);
  const [originResult, setOriginResult] = useState('');  
  const [SubjectTermEnum] = useGetEnumOptionsByKey(['SubjectTermEnum']);
  
  const [title, setTitle] = useState('');
    const { initialState } = useModel('@@initialState');
   

  useEffect(() => {
    setTitle(query.name + '的报告');
    console.log(params);
    fetchChildReportDetail(params.id).then((res) => {
      if (res.status === 0) {
        formRef.current?.setFieldsValue({
          classId: res.data.classId,
          term: res.data.term,
          childName: query.name,
          result: res.data.result,
        });
      
        const cs: any = initialState?.userClass?.find((v: any) => v.id === res.data.classId) || {};
const tm:any = SubjectTermEnum.find((v: any) => v.value === res.data.term) || {};
        setTitle(`${query.name}的报告(${cs.title}${cs.nickname ? '-' + cs.nickname : ''}/${tm.label})`);

        setOriginResult(res.data.result);
      }
    });
  }, []);

  const onConfirmReport = () => {
    updateChildReportDetail({
      id: params.id,
      result: formRef.current?.getFieldValue('result'),
    }).then((res) => {
      if (res.status === 0) {
        message.success('提交成功');
        setIsEdit(false);
      }
    });
  };

  return (
    <PageContainer title={title}>
      <div className="buttons" style={{ position: 'fixed', zIndex: 1, right: 100, top: 76 }}>
        {!isEdit ? (
          <Button onClick={() => setIsEdit(true)} type="primary">
            编辑
          </Button>
        ) : (
          <>
            <Button onClick={onConfirmReport} type="primary">
              保存
            </Button>
            <Button style={{ marginLeft: 10 }} onClick={() => {
              formRef.current?.setFieldsValue({
                result: originResult,
              });
              setIsEdit(false);
            }}>
              取消
            </Button>
          </>
        )}
      </div>

      
      <ProForm formRef={formRef} initialValues={{}} submitter={false}>

        <ProFormTextArea
          disabled={!isEdit}
          fieldProps={{
            autoSize: true,
          }}
          name={'result'}
          placeholder={'报告正在生成中，请稍等'}
        ></ProFormTextArea>
      </ProForm>
      {/* <Row justify={'end'}>
        <Button style={{ marginRight: 10 }} onClick={() => {
          history.back();
        }}>取消</Button>
        <Button onClick={() => {}} type="primary">
          提交
        </Button>
      </Row> */}
    </PageContainer>
  );
};

export default ChildrenReportEdit;
