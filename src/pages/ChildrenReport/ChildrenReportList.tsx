import {
  fetchChildReportList,
} from '@/services/childReport';
import { PageContainer, ProColumns, ProFormInstance, ProTable } from '@ant-design/pro-components';
import { useModel, useParams } from '@umijs/max';
import { Button } from 'antd';

import { parse, stringify } from 'querystring';
import { useEffect, useRef } from 'react';


interface ChildReportEntity {
  evaluationId: number;
  schoolId: number;
  classId: number;
  term: number;
  childId: number;
  request: any;
  result: string;
  createdBy: number;
}

const ChildrenReport = () => {
  const { initialState } = useModel('@@initialState');
  const formRef = useRef<ProFormInstance>();
  const params = useParams();

  const query = parse(location.href.split('?')[1]);

  const baseColumn: ProColumns<ChildReportEntity, 'text'>[] = [
    {
      title: '操作',
      ellipsis: true,
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: '300px',
      search: false,
      fixed: 'right',
      render: (_, entity: any) => [
        <Button
          size="small"
          type="primary"
          key="matrix"
          target="_blank"
          href={
            '/childrenReport/ChildrenReportEdit/' +
            entity.id +
            '?' +
            stringify({ name: query.name })
          }
        >
          查看
        </Button>,
      ],
    },

    {
      title: '儿童姓名',
      dataIndex: 'title',
      valueType: 'text',
      width: 120,
      search: false,
      renderText() {
        const f = parse(location.href.split('?')[1]);
        return f.name;
      },
    },
    {
      title: '是否已完成报告',
      dataIndex: 'result',
      valueType: 'text',
      width: 120,
      search: false,
      renderText(text) {
        return text ? '是' : '否'
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'text',
      width: 220,
      search: false
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      valueType: 'text',
      width: 220,
      search: false,
    },
  ];

  useEffect(() => {
    formRef.current?.setFieldsValue({
      term: initialState?.currentClass,
    });
  }, []);
  return (
    <PageContainer style={{ background: '#fff' }}>
      <ProTable<ChildReportEntity, any>
        // actionRef={actionRef}
        formRef={formRef}
        rowKey="id"
        // columnsState={{
        //   value: columnsMap,
        //   onChange(map) {
        //     setColumnsMap(map as any)
        //   }
        // }}
        scroll={{ y: 500, x: 'max-content' }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 10,
        }}
        search={false}
        expandable={{ showExpandColumn: false }}
        toolBarRender={() => []}
        request={() => {
          return fetchChildReportList(params.id);
        }}
        columns={baseColumn}
      />

     
    </PageContainer>
  );
};

export default ChildrenReport;
