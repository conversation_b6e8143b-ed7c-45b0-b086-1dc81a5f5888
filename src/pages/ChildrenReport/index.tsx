import { getTermColumns } from '@/components/CommonColumn';

import { addChildReportList, fetchChildReportStatistic } from '@/services/childReport';
import { PageContainer, ProColumns, ProFormInstance, ProTable } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Button, message } from 'antd';
import confirm from 'antd/es/modal/confirm';

import moment from 'moment';
import { stringify } from 'querystring';
import { useEffect, useRef, useState } from 'react';

interface ChildReportEntity {
  evaluationId: number;
  schoolId: number;
  classId: number;
  term: number;
  childId: number;
  request: any;
  result: string;
  createdBy: number;
}

const ChildrenReport = () => {
  const { initialState } = useModel('@@initialState');
  const formRef = useRef<ProFormInstance>();

  const onAddRepory = (entity: any) => {
    const params = formRef.current?.getFieldsValue();
    addChildReportList({
      ...params,
      childId: entity.id,
    })
      .then((res) => {
        if (res.status === 0) {
          confirm({
            title: '新增报告成功',
            content: '10-15分钟之后生成报告',
          });
        }
      })
      .catch((e) => {
        message.error(e.message || '新增报告失败');
        console.log([e]);
      });
  };

  const [] = useState();
  // const onConfirmReport = (p: any) => {
  //   updateChildReportDetail(p).then((res) => {
  //     if (res.status === 0) {
  //       message.success('提交成功');
  //       hideModal();
  //     }
  //   });
  // };

  const baseColumn: ProColumns<ChildReportEntity, 'text'>[] = [
    {
      title: '操作',
      ellipsis: true,
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: '300px',
      search: false,
      fixed: 'right',
      render: (_, entity: any) => {
        const params = formRef.current?.getFieldsValue();
        return [
          <div key={'action'}>
            <Button
              size="small"
              type="primary"
              danger
              key="add"
              style={{ marginRight: 10 }}
              onClick={() => onAddRepory(entity)}
            >
              新增报告
            </Button>
            {entity.count ? (
              <Button
                size="small"
                type="primary"
                key="list"
                href={
                  '/ChildrenReport/ChildrenReportList/' +
                  entity.id +
                  '?' +
                  stringify({ name: entity.title, ...params })
                }
                style={{ marginRight: 10 }}
                target="_blank"
              >
                报告列表
              </Button>
            ) : null}
            {entity.lastId ? (
              <Button
                href={
                  '/childrenReport/ChildrenReportEdit/' +
                  entity.lastId +
                  '?' +
                  stringify({ name: entity.title })
                }
                size="small"
                type="primary"
                key="edit"
                target="_blank"
              >
                查看最新报告
              </Button>
            ) : null}
          </div>,
        ];
      },
    },
    ...getTermColumns(formRef),
    {
      title: '编号',
      search: false,
      width: 120,
      dataIndex: 'index',
      valueType: 'index',
    },
    {
      title: '儿童姓名',
      dataIndex: 'title',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '出生年月',
      dataIndex: 'birthday',
      valueType: 'text',
      search: false,
      renderText(text) {
        return moment(text).format('YYYY-MM-DD');
      },
      width: 120,
    },
    {
      title: '入园时间',
      dataIndex: 'entryTime',
      valueType: 'text',
      search: false,
      width: 120,
      renderText(text) {
        return moment(text).format('YYYY-MM-DD');
      },
    },
    {
      title: '性别',
      dataIndex: 'sex',
      valueType: 'text',
      search: false,
      width: 120,
      renderText(text) {
        return ['', '男', '女'][text];
      },
    },
    {
      title: '报告数量',
      search: false,
      dataIndex: 'count',
      valueType: 'text',
      sorter(a: any, b: any) {
        return Number(a.count) - Number(b.count);
      },
      width: 120,
    },
  ];

  useEffect(() => {
    formRef.current?.setFieldsValue({
      term: initialState?.currentClass[1],
      classId: initialState?.currentClass[0],
    });
  }, []);
  return (
    <PageContainer style={{ background: '#fff' }}>
      <ProTable<ChildReportEntity, any>
        // actionRef={actionRef}
        formRef={formRef}
        rowKey="id"
        // columnsState={{
        //   value: columnsMap,
        //   onChange(map) {
        //     setColumnsMap(map as any)
        //   }
        // }}
        scroll={{ y: 500, x: 'max-content' }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 50,
        }}
        onChange={(p,b) => {
          console.log(p, b)
        }}
        expandable={{ showExpandColumn: false }}
        toolBarRender={() => []}
        request={(params: any) => {
         
          const p: any = { ...params};
          if (!params.classId) {
            p.classId = initialState?.currentClass[0];
            p.term = initialState?.currentClass[1];
          }
          console.log(p);
          return fetchChildReportStatistic(p);
        }}
        columns={baseColumn}
      />
    </PageContainer>
  );
};

export default ChildrenReport;
