import { DeleteOutlined, DownloadOutlined, EyeOutlined } from '@ant-design/icons';
import { Image, Modal } from 'antd';
import { useRef, useState } from 'react';
import ReactPlayer from 'react-player';
import './index.less';

interface IProps {
  category: number;
  uri: string;
  filename: string;
  style: React.CSSProperties | undefined;
  del: boolean;
  onDel: (props: IProps) => void;
}

enum ECategory {
  IMAGE = 1,
  FILE = 10,
  MEDIA = 5,
}

export default (props: IProps) => {
  const { category, uri, filename, style, del, onDel } = props;

  const [playing, setPlaying] = useState(false);
  const [isShow, setIsShow] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);

  const playerRef = useRef();
  // const [firstFrame, setFirstFrame] = useState(null);

  const onReady = () => {
    // 当播放器准备就绪后，获取首帧图片
    if (playerRef.current) {
      const canvas = playerRef.current.getInternalPlayer('html5')?.canvas;
      const ctx = canvas.getContext('2d');
      ctx.drawImage(canvas, 0, 0, canvas.width, canvas.height);
      setFirstFrame(canvas.toDataURL('image/png'));
    }
  };

  if (del) {
    if (category === ECategory.IMAGE) {
      return (
        <>
          <div
            style={{
              ...style,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
            className="overlayWrapper"
          >
            <Image
              alt={filename}
              wrapperClassName="image-wrapper"
              style={{
                maxHeight: style?.height || 50,
                // width: 'auto',
                maxWidth: style?.width,
              }}
              src={uri || ''}
            />
            <Modal open={previewVisible} footer={null} onCancel={() => setPreviewVisible(false)}>
              <Image preview={false} src={uri} alt="preview" style={{ maxWidth: '90%' }} />
            </Modal>

            <div className="overlay">
              <EyeOutlined
                className="preview-icon"
                onClick={() => {
                  setPreviewVisible(true);
                }}
              />
              <DeleteOutlined
                className="delete-icon"
                onClick={() => {
                  onDel?.(props);
                }}
              />
            </div>
          </div>
        </>
      );
    }
    if (category === ECategory.MEDIA) {
      return (
        <div
          style={{
            ...style,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
          className="overlayWrapper"
        >
          <div className="delplay" style={style}>
            <ReactPlayer
              className="playerWrapper"
              url={uri} // 视频链接
              onReady={onReady}
              // controls // 显示播放控件
              width={style?.width || '80px'}
              height={style?.height || '40px'}
              config={{
                file: {
                  attributes: {
                    disablePictureInPicture: true, // 禁用画中画
                  },
                },
              }}
            />
          </div>
          {isShow ? (
            <Modal
              title="播放视频"
              open={isShow}
              cancelButtonProps={{
                style: {
                  display: 'none',
                },
              }}
              destroyOnClose
              onCancel={() => {
                setPlaying(false);
                setIsShow(false);
              }}
              okButtonProps={{
                style: {
                  display: 'none',
                },
              }}
            >
              <ReactPlayer
                className="playerWrapper"
                url={uri} // 视频链接
                onReady={onReady}
                // controls // 显示播放控件
                playing={playing}
                onClick={() => {
                  setPlaying((res) => {
                    return !res;
                  });
                }}
                width="480px"
                height="324px"
                config={{
                  file: {
                    attributes: {
                      disablePictureInPicture: true, // 禁用画中画
                    },
                  },
                }}
                onEnded={() => {
                  setPlaying(false);
                }}
              />
            </Modal>
          ) : null}

          <div className="overlay">
            <EyeOutlined
              className="preview-icon"
              onClick={() => {
                setPlaying(true);
                setIsShow(true);
              }}
            />
            <DeleteOutlined
              className="delete-icon"
              onClick={() => {
                onDel?.(props);
              }}
            />
          </div>
        </div>
      );
    }
    if (category === ECategory.FILE) {
      return (
        <div
          style={{
            ...style,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
          className="overlayWrapper"
        >
          <a href={uri} download={filename}>
            {filename}
          </a>
          <div className="overlay">
            <a href={uri} download={filename}>
              <DownloadOutlined className="preview-icon" />
            </a>
            <DeleteOutlined
              className="delete-icon"
              onClick={() => {
                onDel?.(props);
              }}
            />
          </div>
        </div>
      );
    }
  }
  if (category === ECategory.IMAGE) {
    return (
      <div
        style={{
          ...style,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Image
          alt={filename}
          style={{
            maxHeight: style?.height || 50,
            // width: 'auto',
            maxWidth: style?.width,
          }}
          src={uri || ''}
        />
      </div>
    );
  }
  if (category === ECategory.MEDIA) {
    return (
      <>
        <div className="playWrapper">
          <div
            onClick={() => {
              setPlaying(true);
              setIsShow(true);
            }}
            className="play"
            style={style}
          >
            <ReactPlayer
              className="playerWrapper"
              url={uri} // 视频链接
              onReady={onReady}
              // controls // 显示播放控件
              width={style?.width || '80px'}
              height={style?.height || '40px'}
              config={{
                file: {
                  attributes: {
                    disablePictureInPicture: true, // 禁用画中画
                  },
                },
              }}
            />
          </div>
        </div>
        {isShow ? (
          <Modal
            title="播放视频"
            open={isShow}
            cancelButtonProps={{
              style: {
                display: 'none',
              },
            }}
            destroyOnClose
            onCancel={() => {
              setPlaying(false);
              setIsShow(false);
            }}
            okButtonProps={{
              style: {
                display: 'none',
              },
            }}
          >
            <ReactPlayer
              className="playerWrapper"
              url={uri} // 视频链接
              onReady={onReady}
              // controls // 显示播放控件
              playing={playing}
              onClick={() => {
                setPlaying((res) => {
                  return !res;
                });
              }}
              width="480px"
              height="324px"
              config={{
                file: {
                  attributes: {
                    disablePictureInPicture: true, // 禁用画中画
                  },
                },
              }}
              onEnded={() => {
                setPlaying(false);
              }}
            />
          </Modal>
        ) : null}
      </>
    );
  }
  if (category === ECategory.FILE) {
    return (
      <div
        style={{
          ...style,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          padding: '0 16px',
          overflow: 'hidden',
        }}
      >
        <a href={uri} download={filename}>
          {filename}
        </a>
      </div>
    );
  }
  // return (
  //   <a href={uri} download={filename}>{filename}</a >
  // );
};

// export default (props: IProps) => {
//   const { category, uri, filename, style, del } = props;

//   const [playing, setPlaying] = useState(false);
//   const [isShow, setIsShow] = useState(false);
//   const [previewVisible, setPreviewVisible] = useState(false);

//   const playerRef = useRef();
//   // const [firstFrame, setFirstFrame] = useState(null);

//   const onReady = () => {
//     // 当播放器准备就绪后，获取首帧图片
//     if (playerRef.current) {
//       const canvas = playerRef.current.getInternalPlayer('html5')?.canvas;
//       const ctx = canvas.getContext('2d');
//       ctx.drawImage(canvas, 0, 0, canvas.width, canvas.height);
//       setFirstFrame(canvas.toDataURL('image/png'));
//     }
//   };

//   let dom = null;

//   if (category === ECategory.IMAGE) {
//     dom = (
//       <>
//         <Image
//           alt={filename}
//           wrapperClassName="image-wrapper"
//           style={{
//             maxHeight: style?.height || 50,
//             // width: 'auto',
//             maxWidth: style?.width,
//           }}
//           src={uri || ''}
//         />
//         <Modal open={previewVisible} footer={null} onCancel={() => setPreviewVisible(false)}>
//           <Image preview={false} src={uri} alt="preview" style={{ maxWidth: '90%' }} />
//         </Modal>
//       </>
//     );
//     // return (
//     //   <div
//     //     style={{
//     //       ...style,
//     //       display: 'flex',
//     //       justifyContent: 'center',
//     //       alignItems: 'center',
//     //     }}
//     //   >
//     //     <Image
//     //       alt={filename}
//     //       style={{
//     //         maxHeight: style?.height || 50,
//     //         // width: 'auto',
//     //         maxWidth: style?.width,
//     //       }}
//     //       src={uri || ''}
//     //     />
//     //   </div>
//     // );
//   }
//   if (category === ECategory.MEDIA) {
//     dom = (
//       <>
//         <div className="playWrapper">
//           <div
//             onClick={() => {
//               setPlaying(true);
//               setIsShow(true);
//             }}
//             className="play"
//             style={style}
//           >
//             <ReactPlayer
//               className="playerWrapper"
//               url={uri} // 视频链接
//               onReady={onReady}
//               // controls // 显示播放控件
//               width={style?.width || '80px'}
//               height={style?.height || '40px'}
//               config={{
//                 file: {
//                   attributes: {
//                     disablePictureInPicture: true, // 禁用画中画
//                   },
//                 },
//               }}
//             />
//           </div>
//         </div>
//         {isShow ? (
//           <Modal
//             title="播放视频"
//             open={isShow}
//             cancelButtonProps={{
//               style: {
//                 display: 'none',
//               },
//             }}
//             destroyOnClose
//             onCancel={() => {
//               setPlaying(false);
//               setIsShow(false);
//             }}
//             okButtonProps={{
//               style: {
//                 display: 'none',
//               },
//             }}
//           >
//             <ReactPlayer
//               className="playerWrapper"
//               url={uri} // 视频链接
//               onReady={onReady}
//               // controls // 显示播放控件
//               playing={playing}
//               onClick={() => {
//                 setPlaying((res) => {
//                   return !res;
//                 });
//               }}
//               width="480px"
//               height="324px"
//               config={{
//                 file: {
//                   attributes: {
//                     disablePictureInPicture: true, // 禁用画中画
//                   },
//                 },
//               }}
//               onEnded={() => {
//                 setPlaying(false);
//               }}
//             />
//           </Modal>
//         ) : null}
//       </>
//     );
//   }
//   if (category === ECategory.FILE) {
//     dom = (
//       <a href={uri} download={filename}>
//         {filename}
//       </a>
//     );
//     // return (
//     //   <div
//     //     style={{
//     //       ...style,
//     //       display: 'flex',
//     //       justifyContent: 'center',
//     //       alignItems: 'center',
//     //       padding: '0 16px',
//     //       overflow: 'hidden',
//     //     }}
//     //   >
//     //     <a href={uri} download={filename}>
//     //       {filename}
//     //     </a>
//     //   </div>
//     // );
//   }
//   return (
//     <div
//       style={{
//         ...style,
//         display: 'flex',
//         justifyContent: 'center',
//         alignItems: 'center',
//       }}
//       className="overlayWrapper"
//     >
//       {dom}
//       <div className="overlay">
//         <EyeOutlined
//           className="preview-icon"
//           onClick={() => {
//             if (category === ECategory.IMAGE) {
//               setPreviewVisible(true);
//             }
//             if (category === ECategory.FILE) {
//               setPlaying(true);
//               setIsShow(true);
//             }
//           }}
//         />
//         <DeleteOutlined className="delete-icon" />
//       </div>
//     </div>
//   );

//   // return (
//   //   <a href={uri} download={filename}>{filename}</a >
//   // );
// };
