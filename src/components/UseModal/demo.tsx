import { Button } from 'antd';
import { useModal } from './';

const App = () => {
  const [AntModal, showModal, hideModal] = useModal();

  const onConfirmModal = () => {
    hideModal();
  };
  return (
    <div>
      <Button onClick={showModal}>tttt</Button>
      <AntModal onOk={onConfirmModal}>
        <div>
          <div>222222</div>
          <Button onClick={hideModal}>33333</Button>
        </div>
      </AntModal>
    </div>
  );
};

export default App;
