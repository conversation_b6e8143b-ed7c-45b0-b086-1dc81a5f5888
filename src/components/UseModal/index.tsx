import { Modal, ModalProps } from 'antd';
import { useState } from 'react';

export const useModal = () => {
  const [open, setOpen] = useState(false);

  const hideModal = () => setOpen(false);

  const showModal = () => setOpen(true);

  const AntModal = (props: ModalProps) => (
    <Modal open={open} onCancel={hideModal} {...props}>
      {props.children}
    </Modal>
  );

  return [AntModal, showModal, hideModal] as [
    (props: ModalProps) => JSX.Element,
    () => void,
    () => void,
  ];
};
