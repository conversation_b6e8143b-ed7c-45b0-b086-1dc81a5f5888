/**
 *  基础表单组件
 *  该组件提供部分基础表单功能，基础布局
 *  复杂表单请使用render方法
 *  后续如果有通用功能可按照实际情况添加
 */
import {
  DatePicker,
  DatePickerProps,
  Form,
  FormProps,
  Input,
  InputProps,
  Radio,
  RadioProps,
  Select,
  SelectProps,
} from 'antd';
import { Rule } from 'antd/es/form';
import { FormInstance } from 'antd/lib';
import React from 'react';
import FileFormItem, { FileFormItemProps } from './FileFormItem';

type RenderProps = {
  render: (props: { form: FormField; formInstance: FormInstance<any> }) => JSX.Element;
};
type InputTextareaProps = InputProps & {
  autoSize?: boolean;
  classNames?: string;
  styles?: React.CSSProperties;
  onSubmit?: undefined;
  prefix?: undefined;
};

type CommonField = {
  label: string;
  name: string;
  rules?: Rule[];
  enumType?: keyof Enums;
  span?: number;
  style?: React.CSSProperties;
};
export type FormField =
  | (InputProps & CommonField & { formType: FormTypes.input })
  | (SelectProps & CommonField & { formType: FormTypes.select })
  | (DatePickerProps &
      CommonField & {
        formType: FormTypes.datepicker;
      })
  | (RadioProps &
      CommonField & {
        formType: FormTypes.radio;
        options: { label: string; value: any }[];
      })
  | (InputTextareaProps & CommonField & { formType: FormTypes.textarea })
  | (RenderProps & CommonField & { formType: FormTypes.render })
  | (FileFormItemProps & CommonField & { formType: FormTypes.file });

export type FormFields = (FormField | FormField[])[];

interface AntFormsProps {
  formProps: FormProps;
  forms: FormFields;
  children?: JSX.Element | JSX.Element[];
  form: FormInstance<any>;
  style?: React.CSSProperties;
}

export enum FormTypes {
  'input' = 'input',
  'select' = 'select',
  'datepicker' = 'datepicker',
  'radio' = 'radio',
  'render' = 'render',
  'textarea' = 'textarea',
  'file' = 'file',
}

const FormEle = (props: { form: FormField; formInstance: FormInstance<any> }) => {
  const { form, formInstance } = props;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { formType, name, label, rules, ...restProps } = form;
  // const [options] = useGetEnumOptionsByKey(form.enumType);

  switch (form.formType) {
    case FormTypes.select:
      return (
        <Select
          {...(restProps as SelectProps)}
          disabled={'disabled' in restProps ? restProps.disabled : false}
        ></Select>
      );
    case FormTypes.radio:
      return (
        <Radio.Group
          {...(restProps as RadioProps)}
          disabled={'disabled' in restProps ? restProps.disabled : false}
        >
          {form.options!.map((o) => (
            <Radio key={o.value} value={o.value}>
              {o.label}
            </Radio>
          ))}
        </Radio.Group>
      );
    case FormTypes.datepicker:
      return (
        <DatePicker
          style={{ width: '100%' }}
          {...(restProps as DatePickerProps)}
          disabled={'disabled' in restProps ? restProps.disabled : false}
        />
      );

    case FormTypes.render:
      return form.render(props);
    case FormTypes.textarea:
      return (
        <Input.TextArea
          {...(restProps as any)}
          disabled={'disabled' in restProps ? restProps.disabled : false}
        />
      );
    case FormTypes.file:
      return (
        <FileFormItem
          formInstance={formInstance}
          fileKey={form.fileKey}
          name={name}
          multiple={form.multiple}
          disabled={'disabled' in restProps ? restProps.disabled : false}
          onRemove={form.onRemove}
        />
      );
    case FormTypes.input:
    default:
      return (
        <Input
          {...(restProps as InputProps)}
          disabled={'disabled' in restProps ? restProps.disabled : false}
        />
      );
  }
};

function renderForm(form: FormField, formInstance: FormInstance<any>) {
  return (
    <Form.Item key={form.name} name={form.name} rules={form.rules} label={form.label}>
      {FormEle({ form, formInstance })}
    </Form.Item>
  );
}

const AntForms: React.FC<AntFormsProps> = (props) => {
  return (
    <Form
      style={props.style}
      labelCol={{ style: { width: 120 } }}
      {...props.formProps}
      form={props.form}
    >
      {props.forms.map((form, index) => {
        if (Array.isArray(form)) {
          return (
            <div style={{ display: 'flex' }} key={index}>
              {form.map((f: any) => (
                <div key={f.name} style={{ flex: 1, ...f.style }}>
                  {renderForm(f, props.form)}
                </div>
              ))}
            </div>
          );
        } else {
          return renderForm(form, props.form);
        }
      })}

      {props.children ? <Form.Item>{props.children}</Form.Item> : null}
    </Form>
  );
};

export default AntForms;
