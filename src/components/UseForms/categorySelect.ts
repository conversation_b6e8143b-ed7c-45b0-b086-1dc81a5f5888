import { fetchDictionaryList } from '@/services/apis';
import { DictionaryCategory } from '@/services/constants';
import { useState } from 'react';
import { FormField } from '.';
import { FormTypes } from './index';

interface Props {
  label: string;
  name: string;
  category: DictionaryCategory;
  flex?: number;
  mode?: 'multiple' | 'tags';
}

export function formatterTagOptions(options: any[]) {
  return options.map((v: any) => ({ label: v.value, value: v.id }));
}

export const useCategorySelect = (props: Props): [FormField, any, any] => {
  const [options, setOptions] = useState<any>();

  const unshiftSelectOptions = (initOptions: any) => {
    setOptions((options: any) => {
      const all = [...initOptions, ...options];
      return Array.from(new Set(all.map((v: any) => v.value))).map((v: any) => {
        return all.find((o: any) => o.value === v);
      });
    });
  };

  const updateSelectOptions = (input: string, initOptions?: any) => {
    fetchDictionaryList({
      category: props.category,
      pageSize: 50,
      current: 1,
      value: input,
    }).then((r) => {
      let options = [...(initOptions || []), ...formatterTagOptions(r.data)];
      setOptions(options);
      if (initOptions) {
        unshiftSelectOptions(initOptions);
      }
    });
  };

  return [
    {
      label: props.label,
      name: props.name,
      showSearch: true,
      filterOption: false,
      mode: props.mode,
      onSearch: (input) => {
        updateSelectOptions(input);
      },
      formType: FormTypes.select,
      options: options,
      style: { flex: props.flex || 2 },
    },
    updateSelectOptions,
    unshiftSelectOptions
  ];
};
