import { Form } from 'antd';
import { useEffect, useState } from 'react';
import AntForms, { FormFields, FormTypes } from './';
interface FormProps {
  name: string;
  date: string;
  option: string;
}
const App = () => {
  const [form] = Form.useForm();
  const forms: FormFields = [
    [
      {
        label: '姓名',
        name: 'name',
        formType: FormTypes.input,
      },
      {
        label: '时间',
        name: 'date',
        formType: FormTypes.datepicker,
        placeholder: '请选择时间',
        style: { width: '100%' },
      },
    ],
    {
      label: '单选',
      name: 'option',
      formType: FormTypes.radio,
      options: [
        { label: '选项1', value: 1 },
        { label: '选项2', value: 2 },
      ],
    },
  ];
  const [formData] = useState<FormProps>({
    name: '',
    date: '',
    option: '',
  });
  useEffect(() => {
    form.setFieldsValue(formData);
  }, [form, formData]);

  return (
    <AntForms
      formProps={{
        form: form,
      }}
      forms={forms}
    />
  );
};

export default App;
