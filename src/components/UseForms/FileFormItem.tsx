import { FormInstance } from 'antd';
import { useWatch } from 'antd/es/form/Form';
import { useCallback, useState } from 'react';
import UploadImageComponentV2 from '../UploadImage/V2';

export interface FileFormItemProps {
  name: string;
  formInstance?: FormInstance<any>;
  fileKey: string;
  disabled?: boolean;
  multiple?: boolean;
  onRemove?: (file: API.SyncFileData) => void;
}
const FileFormItem: React.FC<FileFormItemProps> = (props) => {
  const [client, setClient] = useState<any>();
  const fileList = useWatch(props.name, props.formInstance);
  const onFileListChange = useCallback(
    (fs: API.SyncFileData[]) => {
      if (props.formInstance) {        
        if(props.multiple !== undefined && !props.multiple) {
          props.formInstance.setFieldValue(props.name, [fs[fs.length-1]]);
        } else {
          props.formInstance.setFieldValue(props.name, fs);
        }
        
      }
    },
    [props.formInstance],
  );
  return (
    <UploadImageComponentV2
      fileName={props.name}
      fileList={fileList}
      setFileList={onFileListChange}
      client={client}
      setClient={setClient}
      edit={!props.disabled}
      formInstance={props.formInstance}
      onRemove={props.onRemove || null}
    />
  );
};

export default FileFormItem;
