import { SelectProps } from 'antd';
import { useState } from 'react';
import { FormField, FormTypes } from '.';

interface Props {
  label: string;
  name: string;
  flex?: number;
  mode?: 'multiple' | 'tags';
  getSelectOptions?: (text?: any) => Promise<any>;
}

export const useSelectForm = (props: Props & SelectProps): [FormField, any, any] => {
  const [options, setOptions] = useState<any>();
  const { label, name, flex, mode, getSelectOptions, ...selectPros } = props;
  const unshiftSelectOptions = (initOptions: any) => {
    setOptions((options: any) => {
      const all = [...initOptions, ...options];
      return Array.from(new Set(all.map((v: any) => v.value))).map((v: any) => {
        return all.find((o: any) => o.value === v);
      });
    });
  };

  const updateSelectOptions = (input: string, initOptions?: any) => {
    if (getSelectOptions) {
      getSelectOptions(input).then((options: any) => {
        console.log(options, 'oooo')
        setOptions(options);
        if (initOptions) {
          unshiftSelectOptions(initOptions);
        }
      });
    }
  };

  return [
    {
      label: label,
      name: name,
      showSearch: true,
      filterOption: false,
      mode: mode,
      onSearch: (input) => {
        updateSelectOptions(input);
      },
      formType: FormTypes.select,
      options: options,
      style: { flex: flex || 2 },
      ...selectPros,
    },
    updateSelectOptions,
    unshiftSelectOptions,
  ];
};
