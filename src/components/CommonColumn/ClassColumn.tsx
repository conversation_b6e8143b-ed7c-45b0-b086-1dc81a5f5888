// import { updateUserClassesTerm } from '@/services/apis';
import { ProFormSelect } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { useState } from 'react';

export const ClassColumn = (props: { name: string,disabled?: boolean, onChange: (value: any, b: any) => void }) => {
  const { initialState } = useModel('@@initialState');
  const options = initialState?.userClass?.map((v: any) => ({
    ...v,
    label: `${v.title}${v.nickname ? '-' + v.nickname : ''}`,
    value: v.id,
    key: v.id,
    isLeaf: false,
    term: v.term,
   
  }));
  const [classes] = useState<any[]>(options || []);
  return (
    <ProFormSelect
      name={props.name}
      onChange={props.onChange}
      disabled={props.disabled}
      fieldProps={{
        options: classes,
      }}
    />
  );
};

