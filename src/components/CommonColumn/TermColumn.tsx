// import { updateUserClassesTerm } from '@/services/apis';
import { useGetEnumOptionsByKey } from '@/services/enums';
import { ProFormCascader } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { useState } from 'react';

export const TermColumn = (props: { name: string,disabled?: boolean }) => {
  const [SubjectTermEnum] = useGetEnumOptionsByKey(['SubjectTermEnum']);
  const { initialState } = useModel('@@initialState');
  const options = initialState?.userClass?.map((v: any) => ({
    ...v,
    label: `${v.title}${v.nickname ? '-' + v.nickname : ''}`,
    value: v.id,
    key: v.id,
    isLeaf: false,
    term: v.term,
    children: SubjectTermEnum,
  }));
  const [classes] = useState<any[]>(options || []);
  return (
    <ProFormCascader
      name={props.name}
      disabled={props.disabled}
      fieldProps={{
        options: classes,
        // TODO  如有需要 可更新当前绑定关系
        // onChange(option, options) {
        //   updateUserClassesTerm({
        //     ...options[0],
        //     children: null,
        //     options: null,
        //     term: option[1]
        //   })
        // }
      }}
    />
  );
};

