// import { updateUserClassesTerm } from '@/services/apis';
import { useGetEnumOptionsByKey } from '@/services/enums';
import { ProFormSelect } from '@ant-design/pro-components';

export const TermColumn2 = (props: { name: string; disabled?: boolean }) => {
  const [SubjectTermEnum] = useGetEnumOptionsByKey(['SubjectTermEnum']);

  return (
    <ProFormSelect
      name={props.name}
      disabled={props.disabled}
      fieldProps={{
        options: SubjectTermEnum,
      }}
    />
  );
};
