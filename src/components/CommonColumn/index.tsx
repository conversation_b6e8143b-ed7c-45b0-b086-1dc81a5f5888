import { fetchClassList, fetchSchoolList } from '@/services/apis';

import { debounceSearch } from '@/services/utils';
import { ProColumns, ProFormDependency, ProFormSelect } from '@ant-design/pro-components';
import { ClassColumn } from './ClassColumn';
import { TermColumn } from './TermColumn';
import { TermColumn2 } from './TermColumn2';

export type MatrixOptions = {
  matrix1Options: EnumOption[];
  matrix2Options: EnumOption[];
  matrix3Options: EnumOption[];
};
export const getSchoolColumn = (): ProColumns<API.RuleListItem> => {
  return {
    title: '学校',
    dataIndex: 'schoolId',
    hideInTable: true,
    renderFormItem: () => {
      return (
        <ProFormSelect
          name="schoolId"
          showSearch
          request={(value) => {
            return new Promise((resolve) => {
              // TODO  使用debounce会导致加载出错问题排查
              fetchSchoolList({
                current: 1,
                pageSize: 20,
                title: value?.keyWords,
                state: 1,
              }).then((res) => {
                resolve(
                  res.data?.map((item: any) => {
                    return { label: item.title, value: item.id };
                  }),
                );
              });
            });
          }}
        />
      );
    },
  };
};

export const getClassColumn = (): ProColumns<API.RuleListItem> => {
  return {
    title: '班级',
    dataIndex: 'classId',
    hideInTable: true,
    renderFormItem: () => {
      return (
        <ProFormDependency name={['schoolId']}>
          {({ schoolId }) => (
            <ProFormSelect
              name="classId"
              dependencies={['schoolId']}
              showSearch
              request={async (value) => {
                return new Promise((resolve) => {
                  debounceSearch(value, fetchClassList, resolve, {
                    current: 1,
                    pageSize: 20,
                    title: value?.keyWords,
                    schoolId,
                    state: 1,
                  });
                });
              }}
            />
          )}
        </ProFormDependency>
      );
    },
  };
};

export const getTermColumn = (args: any): ProColumns<any> => {
  return {
    title: '学期1',
    dataIndex: 'term',
    hideInTable: true,
    renderFormItem: () => {
      return <TermColumn name="term" />;
    },
    ...args,
  };
};

export const getTermColumns = (formRef: any): ProColumns<any>[] => {
  return [
    {
      title: '班级',
      dataIndex: 'classId',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <ClassColumn
            onChange={(a, b) => {
              const term = b.term;
              formRef.current?.setFieldsValue({
                term: term,
                classId: a
              });
            }}
            name="classId"
          />
        );
      },
    },
    {
      title: '学期',
      dataIndex: 'term',
      hideInTable: true,
      renderFormItem: () => {
        return <TermColumn2 name="term" />;
      },
    },
  ];
};
