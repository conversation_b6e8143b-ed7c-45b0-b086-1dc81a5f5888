import { getResourceByTimer } from '@/services/class';
import { Button, Modal, message } from 'antd';
import { FormInstance } from 'antd/lib';
import { useState } from 'react';
import QRCode from 'react-qr-code';

export default (props: {
    form: FormInstance<any>;
    setList: React.Dispatch<React.SetStateAction<any[]>>
    list: any[], 
    fileName: string;
}) => {
  const { form, setList, list, fileName } = props;
  const [modalValue, setModalValue] = useState<
    | {
        url: string;
        timestamp: number;
        fileName: string;
      }
    | undefined
  >({
    url: '',
    timestamp: 0,
    fileName: '',
  });

  const getIphoneFile = async () => {
    const result = await getResourceByTimer({
      timestamp: modalValue?.timestamp,
    });
    if (result.data?.length) {
      setList([...(list || []), ...(result.data || [])]);
      form?.setFieldsValue({
        headerId: [...(list || []), ...(result.data || [])],
      });
      message.success('同步图片成功');
      setModalValue(undefined);
    } else {
      message.success('未获取到同步文件！！');
    }
  };

  return (
    <>
      <Modal
        title="通过手机上传"
        open={!!modalValue?.url}
        onCancel={() => {
          setModalValue(undefined);
        }}
        okText="关闭"
        destroyOnClose
        footer={false}
      >
        <QRCode
          style={{
            margin: '10px auto 20px',
            display: 'block',
          }}
          value={modalValue?.url || ''} //生成二维码的内容
          size={156} //二维码尺寸
          fgColor="#000000" //二维码颜色
        />
        <Button
          onClick={() => {
            getIphoneFile();
          }}
          style={{
            margin: '0 auto',
            display: 'block',
          }}
        >
          同步到电脑
        </Button>
      </Modal>
      <Button
        style={{ marginLeft: 30 }}
        size="small"
        type="link"
        onClick={async () => {
          const timestamp = Date.now();
          const tk = localStorage.getItem('token');
          const value = `${window.location.origin}/class/upload?timestamp=${timestamp}&token=${tk}&fileName=${fileName}`;
          setModalValue({
            url: value,
            timestamp,
            fileName,
          });
        }}
      >
        通过手机上传
      </Button>
    </>
  );
};
