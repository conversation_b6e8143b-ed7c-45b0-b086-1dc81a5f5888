/*
 * @Description:文件上传
 * @Author: SaraSL
 * @Date: 2024-02-26 13:25:01
 */

/**
 * 文件上传逻辑
 *
 * 1、前端上传文件组件，拿到文件对象后，使用hash算法，算得文件的 Hash 值，然后2
 * 2、前端请求后端 /admin/resource/hash 接口，查询该 Hash 值是否已有对应文件实体；有则6，无则3
 * 3、前端请求后端 /admin/resource/auth 接口，获取上传oss使用的auth信息；此步骤可在页面加载时直接调用，然后4
 * 4、前端从后端获取 auth 数据后，调用 ali-oss SDK中 简单上传 方法，上传文件到 阿里云OSS，然后5
 * 5、前端获取 oss 数据实体，请求后端 /admin/resource/sync 接口，将 oss 实体数据存储到 resource 实体，然后6
 * 6、前端获取 resource 数据实体，请求后端 /admin/resource/bind 接口，将 resource 实体数据绑定到对应业务实体，END
 */

import React, { useEffect, useCallback, useState } from 'react';

import { ProFormUploadButton } from '@ant-design/pro-form';
import { FormInstance, Modal } from 'antd';

import { fetchClient, handleUpload } from '@/services/fileUpload';
import { isImageFile } from '@/services/utils';

/**
 * ===== 上传图片组件
 * @see [阿里云OSS简单上传] (https://help.aliyun.com/document_detail/383950.html)
 * @see [antd上传图片到阿里云的oss服务器] (https://www.jianshu.com/p/7ca4b4fad6eb)
 */
type UploadImageProps = {
  label?: string;
  fileList: API.SyncFileData[];
  setFileList: (fileList: API.SyncFileData[]) => void;
  max?: number;
  client: any;
  setClient: (client: any) => void;
  edit?: boolean;
  fileName: string;
  accept?: string;
  formInstance?: FormInstance;
  onRemove?: (file: any) => void;
};

const fName = 'UploadImageComponentV2';
const UploadImageComponentV2: React.FC<UploadImageProps> = (props) => {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');

  const [client, setClient] = useState<any>();
  const { max = 10, label, edit = false, accept, onRemove } = props;

  useEffect(() => {
    fetchClient(setClient);
  }, []);

  const handleRemove = useCallback(
    async (file: any) => {
      if (!edit) {
        return false;
      }
      if(onRemove) onRemove(file);
      props.setFileList(
        (props.fileList || []).filter((item) => (item.uid || item.hash) !== file.uid),
      );
      return true;
    },
    [props.fileList],
  );

  const handleSave = useCallback(
    async (file: any) => {
      if (!client) {
        await fetchClient(setClient);
      }
      const resource = await handleUpload(client, file);
      const files = props.formInstance?.getFieldValue(props.fileName) || [];
      props.setFileList([
        ...files,
        {
          id: resource.id,
          uri: resource.uri,
          filename: resource.filename,
          uid: resource.hash,
          hash: resource.hash,
        },
      ]);
      return resource;
    },
    [props.fileList],
  );

  const customRequest = async (option) => {
    const { onSuccess, onError } = option;
    try {
      const res = await handleSave(option.file);
      onSuccess(res.uri);
    } catch (e) {
      onError(e?.message || '上传失败');
    }
  };

  const handlePreview = async (file: any) => {
    if (file.faceUrl && (!file.url || !isImageFile(file?.name))) {
      return;
    }
    setPreviewImage(file.url);
    setPreviewOpen(true);
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  };

  return (
    <>
      <ProFormUploadButton
        disabled={!edit}
        max={max}
        label={label}
        fieldProps={{
          name: 'file',
          listType: 'picture',
          fileList:
            (props.fileList instanceof Array ? props.fileList : []).map((item) => ({
              uid: item.hash,
              url: item.uri || item.uri,
              name: item.filename,
            })) || [],
          onRemove: handleRemove,
          onPreview: handlePreview,
          multiple: true,
          showUploadList: true,
          customRequest,
        }}
        name={fName}
        accept={accept}
        // action={handleSave}
      />
      <Modal
        open={previewOpen}
        title={previewTitle}
        footer={null}
        onCancel={() => setPreviewOpen(false)}
      >
        <img alt="example" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </>
  );
};

export default UploadImageComponentV2;
