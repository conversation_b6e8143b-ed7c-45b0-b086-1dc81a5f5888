/*
 * @Description:文件上传
 * @Author: SaraSL
 * @Date: 2024-02-26 13:25:01
 */

/**
 * 文件上传逻辑
 *
 * 1、前端上传文件组件，拿到文件对象后，使用hash算法，算得文件的 Hash 值，然后2
 * 2、前端请求后端 /admin/resource/hash 接口，查询该 Hash 值是否已有对应文件实体；有则6，无则3
 * 3、前端请求后端 /admin/resource/auth 接口，获取上传oss使用的auth信息；此步骤可在页面加载时直接调用，然后4
 * 4、前端从后端获取 auth 数据后，调用 ali-oss SDK中 简单上传 方法，上传文件到 阿里云OSS，然后5
 * 5、前端获取 oss 数据实体，请求后端 /admin/resource/sync 接口，将 oss 实体数据存储到 resource 实体，然后6
 * 6、前端获取 resource 数据实体，请求后端 /admin/resource/bind 接口，将 resource 实体数据绑定到对应业务实体，END
 */

import React, { useEffect, useState } from 'react';

import { fetchClient, handleUpload } from '@/services/fileUpload';
import { isImageFile } from '@/services/utils';
import { LeftCircleFilled, RightCircleFilled } from '@ant-design/icons';
import { ProFormUploadButton } from '@ant-design/pro-form';
import { Modal } from 'antd';
import Player from '../Player';

/**
 * ===== 上传图片组件
 * @see [阿里云OSS简单上传] (https://help.aliyun.com/document_detail/383950.html)
 * @see [antd上传图片到阿里云的oss服务器] (https://www.jianshu.com/p/7ca4b4fad6eb)
 */
type UploadImageProps = {
  label?: string;
  fileList: API.FileItem[];
  setFileList: (fileList: any[]) => void;
  max?: number;
  client: any;
  setClient: (client: any) => void;
  edit?: boolean;
  fileName?: string;
  accept?: string;
  fileCategory?: number;
  rules?: any[];
};
const UploadImageComponent: React.FC<UploadImageProps> = (props) => {
  // const [currentImage] = useState<API.FileItem>();
  // const [previewVisible, setPreviewVisible] = useState<boolean>(false);

  const [previewOpen, setPreviewOpen] = useState(false);
  const [preview, setPreview] = useState({});
  const [previewIndex, setPreviewIndex] = useState(-1);
  const {
    fileList = [],
    setFileList,
    max = 10,
    client,
    setClient,
    label,
    edit = false,
    fileName = 'uploadImages',
    accept,
    fileCategory = 0,
    rules = []
  } = props;

  useEffect(() => {
    fetchClient(setClient);
  }, []);

  const pushFileList = (resource: API.ResourceItem) => {
    // @ts-ignore
    setFileList((res) => {
      return [
        ...res,
        {
          id: resource.id,
          uri: resource.uri,
          filename: resource.filename,
          category: resource.category,
          url: resource.uri,
          name: resource.filename,
        },
      ];
    });
  };

  const handleRemove = async (file: any) => {
    setFileList(fileList.filter((item) => item.id !== file.id));
    return true;
  };

  const handleSave = async (file: any) => {
    if (!client) {
      await fetchClient(setClient);
    }
    if (fileCategory) file.fileCategory = fileCategory;
    const resource = await handleUpload(client, file);
    pushFileList(resource);
    return resource;
  };

  const customRequest = async (option) => {
    const { onSuccess, onError } = option;
    try {
      const res = await handleSave(option.file);
      onSuccess(res.uri);
    } catch (e) {
      onError(e?.message || '上传失败');
    }
  };

  const handlePreview = async (file) => {
    if (!file.url || !isImageFile(file?.filename)) {
      return;
    }
    const { id } = file;
    const index = fileList?.findIndex((item) => item.id === id);
    setPreviewIndex(index);
    setPreview(file);
    setPreviewOpen(true);
  };

  const handleCancel = () => setPreviewOpen(false);

  return (
    <>
      <ProFormUploadButton
        rules={rules}
        max={max}
        label={label}
        fieldProps={{
          name: 'file',
          listType: 'picture-card',
          fileList:
            fileList?.map((item) => ({
              ...item,
              url: item.uri || item?.url,
              name: item.filename || item?.name,
              uid: `${item?.id}` || item.uri,
            })) || [],
          onRemove: handleRemove,
          onPreview: handlePreview,
          multiple: true,
          showUploadList: true,
          customRequest,
          itemRender: (originNode, fileList) => {
            return (
              <>
                {(fileList.category && (
                  // @ts-ignore
                  <Player
                    del={true}
                    onDel={(item) => {
                      handleRemove(item);
                    }}
                    style={{ width: 102, height: 102, fontSize: 12 }}
                    {...fileList}
                  />
                )) ||
                  originNode}
              </>
            );
          },
        }}
        name={fileName}
        accept={accept}
      />
      <Modal
        open={previewOpen}
        className="previewModal"
        title={
          <div
            style={{
              height: 60,
            }}
          >
            {preview.filename ||
              preview.name ||
              preview.uri?.substring(preview.uri!.lastIndexOf('/') + 1) ||
              '-'}
          </div>
        }
        footer={null}
        onCancel={handleCancel}
      >
        <div style={{ display: 'flex' }}>
          {(fileList?.length > 1 && (
            <div
              onClick={() => {
                if (previewIndex > 0) {
                  setPreviewIndex(previewIndex - 1);
                  setPreview(fileList[previewIndex - 1]);
                }
              }}
              style={{
                fontSize: 30,
                marginTop: 120,
                color: previewIndex === 0 ? '#f3f3f3' : '#e4e4e4',
                cursor: previewIndex === 0 ? 'not-allowed' : 'pointer',
              }}
            >
              <LeftCircleFilled />
            </div>
          )) ||
            ''}

          <div>
            <div
              style={{
                width: 410,
                display: 'flex',
                justifyContent: 'center',
                alignContent: 'center',
              }}
            >
              <img
                alt="example"
                style={{ width: 'auto', maxWidth: '100%', height: 400, maxHeight: 400 }}
                src={preview.uri}
              />
            </div>
            {(fileList.length > 1 && (
              <div style={{ textAlign: 'center', color: '#fff', marginTop: 10 }}>
                {previewIndex + 1} / {fileList.length}
              </div>
            )) ||
              null}
          </div>

          {(fileList?.length > 1 && (
            <div
              style={{
                fontSize: 30,
                marginTop: 120,
                color: previewIndex === fileList.length - 1 ? '#f3f3f3' : '#e4e4e4',
                cursor: previewIndex === fileList.length - 1 ? 'not-allowed' : 'pointer',
              }}
              onClick={() => {
                if (previewIndex < fileList.length - 1) {
                  setPreviewIndex(previewIndex + 1);
                  setPreview(fileList[previewIndex + 1]);
                }
              }}
            >
              <RightCircleFilled />
            </div>
          )) ||
            ''}
        </div>
      </Modal>
      {/* {edit ? (
        <ModalForm
          modalProps={{
            onCancel: () => {
              return true;
            },
            destroyOnClose: true,
          }}
          onFinish={handleUpdate}
          open={previewVisible}
          onOpenChange={setPreviewVisible}
          // visivle={previewVisible}
          // onVisibleChange={setPreviewVisible}
          title={`预览${label}`}
          labelCol={{ span: 4 }}
          layout="horizontal"
          initialValues={currentImage}
        >
          <Image alt={label} style={{ width: '100%' }} src={currentImage?.uri || ''} />
          <Divider />
          <ProFormText name="id" label="图片ID" disabled width="xs" />
          <ProFormText
            name="name"
            label="图片名称"
            width="sm"
            placeholder="请输入图片名称"
            rules={[{ required: true }]}
          />
        </ModalForm>
      ) : (
        ''
      )} */}
    </>
  );
};

export default UploadImageComponent;
