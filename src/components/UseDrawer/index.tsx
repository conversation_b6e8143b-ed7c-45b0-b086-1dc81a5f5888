import { Drawer, DrawerProps } from 'antd';
import { useState } from 'react';

export const useDrawer = () => {
  const [open, setOpen] = useState(false);

  const onClose = () => {
    setOpen(false);
  };
  const AntDrawer = (props: DrawerProps) => (
    <Drawer open={open} onClose={onClose} {...props}>
      {props.children}
    </Drawer>
  );

  const hideDrawer = () => setOpen(false);

  const showDrawer = () => setOpen(true);

  return [AntDrawer, showDrawer, hideDrawer] as [
    (props: DrawerProps) => JSX.Element,
    () => void,
    () => void,
  ];
};
