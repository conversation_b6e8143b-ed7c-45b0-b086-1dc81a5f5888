/**
 * 用于储存全局变量
 */

import { FetchSelectProps, fetchSelects } from '@/pages/Class/util';
import { createContext, useContext, useEffect, useState } from 'react';

interface GlobalState {
  enums: FetchSelectProps;
  setEnums: any;
}
const UserContext = createContext<GlobalState>({ enums: {}, setEnums: () => {} });

export default function UserProvider(props: { children: JSX.Element | JSX.Element[] }) {
  const [enums, setEnums] = useState({});

  useEffect(() => {
    fetchSelects().then((res) => {
      setEnums(res);
    });
  }, []);
  return <UserContext.Provider value={{ enums, setEnums }}>{props.children}</UserContext.Provider>;
}

export function useGlobal() {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}
