import './index.less';
import { useModel } from '@umijs/max';
const UseCurrentClassTitle = () => {
  const { initialState } = useModel('@@initialState');
  const currentClassId = initialState?.currentUser?.currentClassId;
  const title = initialState?.userClass?.find((item: any) => item.id === currentClassId)?.title;
  return <span className="title">{title}</span>;
};
export default UseCurrentClassTitle;
