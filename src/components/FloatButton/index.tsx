import { CommentOutlined, CustomerServiceOutlined, FileOutlined } from '@ant-design/icons';
import { Button, FloatButton } from 'antd';
import './index.less';
import React from 'react';

const Float: React.FC = () => (
  <>
    <FloatButton.Group
      trigger="click"
      className="floatButton"
      type="primary"
      // style={{ insetInlineEnd: 24 }}
      icon={
        <div>
          <CustomerServiceOutlined />
          <div className='floatButtonText'>我要吐槽</div>
        </div>
      }
    >
      <Button
        size="small"
        type="link"
        key="detail"
        style={{ height: 48, padding: 0 }}
        href={`/feedback/list`}
        target="_blank"
        className="link-ellipsis"
      >
        <FloatButton tooltip={<div>反馈列表</div>} icon={<FileOutlined />} />
      </Button>
      <Button
        size="small"
        type="link"
        style={{ height: 48, padding: 0 }}
        key="detail"
        href={`/feedback/add`}
        target="_blank"
        className="link-ellipsis"
      >
        <FloatButton tooltip={<div>新增反馈</div>} icon={<CommentOutlined />} />
      </Button>
    </FloatButton.Group>
  </>
);

export default Float;
