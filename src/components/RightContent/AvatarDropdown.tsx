import { clearToken } from '@/services/api';
import {
  LogoutOutlined,
  SettingOutlined,
  UserOutlined,
  EditOutlined,
  FormOutlined,
  CustomerServiceOutlined
} from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import { Spin } from 'antd';
import { createStyles } from 'antd-style';
import { stringify } from 'querystring';
import type { MenuInfo } from 'rc-menu/lib/interface';
import React, { useCallback } from 'react';
import HeaderDropdown from '../HeaderDropdown';
import UseCurrentClassTitle from '@/components/UseCurrentClassTitle';

export type GlobalHeaderRightProps = {
  menu?: boolean;
  children?: React.ReactNode;
};

export const AvatarName = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  return (
    <span className="anticon">
      {currentUser?.name || currentUser?.nickname || currentUser?.mobile}
    </span>
  );
};

const useStyles = createStyles(({ token }) => {
  return {
    action: {
      display: 'flex',
      height: '48px',
      marginLeft: 'auto',
      overflow: 'hidden',
      alignItems: 'center',
      padding: '0 8px',
      cursor: 'pointer',
      borderRadius: token.borderRadius,
      '&:hover': {
        backgroundColor: token.colorBgTextHover,
      },
    },
  };
});

export const loginOut = async (isNoRedirect?: boolean) => {
  // await outLogin();
  await clearToken();
  const { search, pathname } = window.location;
  const urlParams = new URL(window.location.href).searchParams;
  /** 此方法会跳转到 redirect 参数所在的位置 */
  const redirect = urlParams.get('redirect');
  // Note: There may be security issues, please note
  if (window.location.pathname !== '/user/login' && !redirect) {
    history.replace({
      pathname: '/user/login',
      ...(isNoRedirect
        ? {}
        : {
            search: stringify({
              redirect: pathname + search,
            }),
          }),
    });
  }
};

export const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({ menu, children }) => {
  /**
   * 退出登录，并且将当前的 url 保存
   */

  const { styles } = useStyles();

  const { initialState, setInitialState } = useModel('@@initialState');

  const onMenuClick = useCallback(
    (event: MenuInfo) => {
      const { key } = event;
      if (key === 'logout') {
        setInitialState((s) => ({ ...s, currentUser: undefined } as any));
        loginOut();
        return;
      }
      if (key === 'changePassword') {
        history.push(`/account/password`);
        return;
      }
      if (key === 'changeNickname') {
        history.push(`/account/NickName`);
        return;
      }
      if(key === 'CustomerServiceOutlined'){
        window.open('https://work.weixin.qq.com/kfid/kfc33433ad794129a42')
        return;
      }
      history.push(`/account/${key}`);
    },
    [setInitialState],
  );

  const loading = (
    <span className={styles.action}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    </span>
  );

  if (!initialState) {
    return loading;
  }

  const { currentUser } = initialState;

  if (!currentUser || !currentUser.mobile) {
    return loading;
  }

  const menuItems = [
    ...(menu
      ? [
          {
            key: 'center',
            icon: <UserOutlined />,
            label: '个人中心',
          },
          {
            key: 'settings',
            icon: <SettingOutlined />,
            label: '个人设置',
          },
          {
            type: 'divider' as const,
          },
        ]
      : []),
    {
      key: 'changePassword',
      icon: <EditOutlined />,
      label: '修改密码',
    },
    {
      key: 'changeNickname',
      icon: <FormOutlined />,
      label: '修改昵称',
    },
    {
      key: 'CustomerServiceOutlined',
      icon: <CustomerServiceOutlined />,
      label: '联系客服',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  return (
    <>
      <UseCurrentClassTitle />
      <HeaderDropdown
        menu={{
          selectedKeys: [],
          onClick: onMenuClick,
          items: menuItems,
        }}
      >
        {children}
      </HeaderDropdown>
    </>
  );
};
