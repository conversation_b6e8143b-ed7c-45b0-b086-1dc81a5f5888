import {
  postJavaRequest,
  getJavaRequest
} from './common';

/** 查询 - 教师列表 */
export async function getTeacherList(data: object) {
  return await postJavaRequest<API.CaptchaResult>('/api/global/user/list/teacher', data);
}

/** 查询 - 教师手机号 */
export async function getTeacherPhone(params: object) {
  return await getJavaRequest<API.CaptchaResult>('/api/business/class/moments/teacher/getInfo', params);
}
