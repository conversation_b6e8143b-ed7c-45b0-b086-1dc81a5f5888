import { postJavaRequest, getJavaRequest } from './common';

// 获取 班级区域列表
export async function getRegionClassList(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(
    `/api/business/areaManagement/listByClass`,
    params,
  );
}

// 获取 班级区域名称列表
// https://jsapi.mypacelab.com/api/business/areaManagement/getAreaList?classId=1499
export async function getRegionClassNameList(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(
    `/api/business/areaManagement/getAreaList`,
    params,
  );
}

// 添加班级区域
export async function addRegionClassNameItem(data: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(`/api/business/areaManagement/addArea`, data);
}

// 编辑班级区域
export async function editRegionClassNameItem(data: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(`/api/business/areaManagement/editArea`, data);
}

// 删除班级区域
export async function deleteRegionClassNameItem(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(`/api/business/areaManagement/deleteArea`, params);
}

// 获取 户外区域名称列表
export async function getRegionOutdoorsList(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(
    `/api/business/areaManagement/listOutSideArea`,
    params,
  );
}