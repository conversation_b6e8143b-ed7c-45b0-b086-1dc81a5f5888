import { postJavaRequest, getRequest, getJavaRequest } from './common';

/** 查询 - 观察记录列表 */
export async function getObsList(data: object) {
  return await postJavaRequest<API.CaptchaResult>('/api/business/observation/pageList', data);
}

/** 删除 - 删除一个观察记录 */
export async function delObsItem(id: number) {
  return await postJavaRequest<API.CaptchaResult>(`/api/business/observation/delete/${id}`);
}

/** 查询 - 观察记录详情 */
export async function getObsItemDetails(id: number) {
  return await postJavaRequest<API.CaptchaResult>(`/api/business/observation/detail/${id}`);
}

/** 查询 - 当前班级 */
export async function getObsClassList() {
  return await getRequest<API.CaptchaResult>(`/pts/business/class/my_list_v3`);
}

/** 查询 - 儿童列表 */
export async function getObsChildList(params: object) {
  return await getRequest<API.CaptchaResult>(`/pts/business/child/list`, params);
}
/** 获取 - 核心经验数据 */
export async function getObsMatrixList(params: { relId: number; matchType: string }) {
  return await getJavaRequest<API.CaptchaResult>(`/api/business/coreExp/getCoreExpById`, params);
}

/** 修改 - 观察记录 */
export async function updateObsItem(data: object) {
  return await postJavaRequest<API.CaptchaResult>(`/api/business/observation/updateSubject`, data);
}

/** 保存 - 核心经验 */
export async function updateCoreExpList(datas: { relId: number; matchType: string; list: any[]}) {
  return await postJavaRequest<API.CaptchaResult>(`/api/business/coreExp/updateCoreExpBatch/${datas?.matchType}/${datas?.relId}`, datas.list );
}

/** AI生成观察记录 */
export async function addAICoreExpList(data: object) {
  return await postJavaRequest<API.CaptchaResult>(`/api/business/observation/submitAiObservationRecord`, data );
}

/** 新建观察记录 */
export async function addObservation(data: object) {
  return await postJavaRequest<API.CaptchaResult>(`/api/business/observation/manualRecord`, data );
}