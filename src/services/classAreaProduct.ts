import { getRequest, postRequest, putRequest, getJavaRequest, postJavaRequest } from './common';

// 区域列表接口参数类型
export interface FetchAreaListParams {
  schoolId: number;
  classId: number;
}

// 区域材料列表接口参数类型
export interface FetchAreaProductListParams {
  classId: number;
  areaId: number;
  schoolId: number;
}

// 查询材料接口参数类型
export interface QueryAreaProductListParams {
  classId: number;
  areaId: number;
  schoolId: number;
  materialName?: string;
}

// 区域列表返回数据类型
export interface AreaItem {
  area: string;
  id: number;
  areaAlias?: string;
  classId?: number;
  schoolId?: number;
  teacherId?: number;
}

// 材料详情模板类型
export interface MaterialDetailTemplate {
  documentTemplateCategory: string;
  fileName: string;
  id: number;
  name: string;
  state: number;
  subjectId: number;
  subjectType: string;
  templateResourceId: number;
  url: string;
}

// 材料项类型
export interface MaterialItem {
  area: string;
  areaAlias: string;
  areaId: number;
  createTime: string;
  displayedImage: string;
  id: number;
  materialDetailTemplate: MaterialDetailTemplate[];
  name: string;
  playCount: number;
  playStatus: number;
  quantity: number;
  retryTime: number;
  selectedImg: string;
  sourceType: string;
  unAdoptPlayCount: number;
}

// 区域列表 business/material/getClassMaterialArea
// 传参: schoolId, classId
export async function fetchAreaList(params: FetchAreaListParams) {
  return await getJavaRequest(`/api/business/material/getClassMaterialArea`, params);
}

/** 区域材料列表 */
// 传参: classId, areaId, schoolId
export async function fetchAreaProductList(params: FetchAreaProductListParams) {
  return await getJavaRequest(`/api/business/material/getClassCombinedMaterial`, params);
}

// 查询材料  /business/material/queryClassCombinedMaterial
// 传参: classId, areaId, materialName schoolId
export async function queryAreaProductList(params: QueryAreaProductListParams) {
  return await getJavaRequest(`/api/business/material/queryClassCombinedMaterial`, params);
}

// 删除材料 /business/material/deleteMaterialById
// 传参: id classId  sourceType = toy || book
export async function deleteAreaProduct(params: any) {
  // 将参数作为查询参数传递
  const queryParams = new URLSearchParams();
  if (params.id) queryParams.append('id', params.id.toString());
  if (params.classId) queryParams.append('classId', params.classId.toString());
  if (params.sourceType) queryParams.append('sourceType', params.sourceType);

  return await postJavaRequest(`/api/business/material/deleteMaterialById?${queryParams.toString()}`, {});
}

// old

/** 查询 - 区域材料统计详情 */
export async function fetchAreaProductDetail(id?: number) {
  return await getRequest(`/pts/business/class_area_product/detail/${id}`);
}

/** 新增 - 区域材料统计 */
export async function addAreaProduct(data: API.ClassAreaProductProps) {
  return await postRequest(`/pts/business/class_area_product`, data);
}

/** 编辑 - 区域材料统计 */
export async function updateAreaProduct(data: API.ClassAreaProductProps) {
  return await putRequest(`/pts/business/class_area_product/u`, data);
}
/** 查询 - 区域材料统计 */
export async function fetchAreaProductStatistics(data: any) {
  return await getRequest(`/pts/business/class_area_product/statistics`, data);
}
