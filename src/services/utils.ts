import { fetchProductExport } from '@/services/api';
import { message } from 'antd';
import CryptoJS from 'crypto-js';
import { debounce } from 'lodash';

export const handleCopy = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    message.success('复制成功');
  } catch (err) {
    message.success('复制失败，请重试');
  }
};

// type ActionFunction = {
//   actionBefore: () => void;
//   actionFun: () => void;
//   actionAfter: () => void;
// };

const emptyFun = () => {};

/**
 * 事件监听 - 封装
 * @param msg 加载信息
 * @param actionFuns.actionBefore 操作前
 * @param actionFuns.actionFun  操作
 * @param actionFuns.actionAfter  操作后
 */
const __handleAction = async (msg: string, actionFuns: () => void) => {
  const hide = message.loading(`正在${msg}`);
  try {
    let result = null;
    if (typeof actionFuns !== 'function') {
      const { actionBefore = emptyFun, actionFun = emptyFun, actionAfter = emptyFun } = actionFuns;
      await actionBefore();
      result = await actionFun();
      hide();
      message.success(`${msg}成功`);
      await actionAfter();
    } else {
      result = await actionFuns();
      hide();
      message.success(`${msg}成功`);
    }
    return result;
  } catch (error: any) {
    hide();
    message.error(error.message || `${msg}失败，请重试!`);
  }
};

export const handleAction = async (msg: string, actionFun: () => void) => {
  return await __handleAction(msg, actionFun);
};

// export const handleCreate = async (values, actionFuns: ActionFunction | Function) => {
//   await handleAction(0, async ()=>{await actionFuns(values););
// };

/**
 * @description 获取文件的 MD5 值
 * @param {Blob || File} file 待处理文件(可以为 input 上传的文件或者 blob 对象)
 * @return Promise<string>
 * @see https://www.yeliulee.com/archives/142.html
 */
export const file2Md5 = (file: any): Promise<string> => {
  return new Promise((resolve) => {
    const fileReader = new FileReader();
    fileReader.onloadend = (event: any) => {
      resolve(
        CryptoJS.MD5(CryptoJS.enc.Latin1.parse(event.target.result)).toString(CryptoJS.enc.Hex),
      );
    };
    fileReader.readAsBinaryString(file);
  });
};

/**
 * @description 获取字符串的 MD5 值
 * @param {string} str
 * @returns
 */
export const str2Md5 = (str: string): string => {
  return CryptoJS.MD5(CryptoJS.enc.Latin1.parse(str)).toString(CryptoJS.enc.Hex);
};

/**
 * 整数校验
 * @param _
 * @param value
 * @returns
 */
export const checkPositiveInteger = (_, value) => {
  if (value && !Number.isInteger(value)) {
    return Promise.reject('请输入正整数');
  }
  // if (value < 1) {
  //     return Promise.reject('请输入正整数');
  // }
  // if (value && value > 9999) {
  //     return Promise.reject('最大值为9999');
  // }
  return Promise.resolve();
};

/**
 * 金额校验
 * @param _
 * @param value
 * @returns
 */
export const checkPrice = (_, value) => {
  const result = value + '';
  if (result === '0' || Number(result) <= 0) {
    return Promise.reject('金额必须大于0');
  }
  if (result?.length - result?.indexOf('.') > 5 && result?.indexOf('.') !== -1) {
    return Promise.reject('小数点后最多四位');
  }
  if (result?.length > 10) {
    return Promise.reject('金额位数最多10位');
  }
  return Promise.resolve();
};

/**
 * 重量校验
 * @param _
 * @param value
 * @returns
 */
export const checkWeight = (_, value) => {
  const result = value + '';
  if (result === '0' || Number(result) <= 0) {
    return Promise.reject('重量必须大于0');
  }
  if (result?.length - result?.indexOf('.') > 4 && result?.indexOf('.') !== -1) {
    return Promise.reject('小数点后最多三位');
  }
  if (result?.length > 10) {
    return Promise.reject('重量位数最多10位');
  }
  return Promise.resolve();
};

/**
 * 面积校验
 * @param _
 * @param value
 * @returns
 */
export const checkArea = (_, value) => {
  const result = value + '';
  if (result === '0' || Number(result) <= 0) {
    return Promise.reject('面积必须大于0');
  }
  if (result?.length - result?.indexOf('.') > 4 && result?.indexOf('.') !== -1) {
    return Promise.reject('小数点后最多三位');
  }
  if (result?.length > 10) {
    return Promise.reject('面积位数最多10位');
  }
  return Promise.resolve();
};

/**
 * 判断文件是否为图片类型
 * @param fileName
 * @returns
 */
export const isImageFile = (fileName: string) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp'];
  const extension = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();
  return imageExtensions.includes(extension);
};

/**
 * 手机号校验
 * @param rule
 * @param value
 * @returns
 */
export const phoneValidator = (rule, value) => {
  if (value && !/^1[3456789]\d{9}$/.test(value)) {
    return Promise.reject('请输入有效的手机号码');
  }
  return Promise.resolve();
};

/**
 * 搜索防抖处理
 */
export const debounceSearch = debounce(async (value, func, callback, params) => {
  const { data } = await func(
    params || {
      current: 1,
      pageSize: 20,
      title: value?.keyWords,
      state: 1,
    },
  );
  const options = data.map((item) => {
    return {
      ...item,
      label: item.title,
      value: item.id,
    };
  });
  callback(options);
}, 300); // 设置防抖时间间隔为300毫秒

/**
 * 处理字典数据防抖搜索时的option
 * @param data
 * @returns
 */
export const formatOption = (data) => {
  const options = data.map((item) => {
    return {
      label: item.value,
      value: item.id,
    };
  });
  return options;
};

/**
 * 文件导出
 */
export const exportFile = async (params) => {
  const res = await fetchProductExport(params);
  console.log(res);

  const link = document.createElement('a');
  link.href = res?.data?.fileLink;
  link.download = res?.data?.fileName;

  document.body.appendChild(link);

  link.click();

  document.body.removeChild(link);

  // // 创建一个新的 Blob 对象
  // const blob = new Blob([res], { type: 'application/octet-stream' });

  // // 创建一个临时的 URL，用于在浏览器中访问这个 Blob 对象
  // const url = window.URL.createObjectURL(blob);

  // // 创建一个 <a> 元素，设置其 href 属性为这个临时 URL，以及 download 属性为文件名
  // const link = document.createElement('a');
  // link.href = url;
  // link.download = `${name}-${moment().format('YYYYMMDD_HHmmss')}.xlsx`; // 设置要保存的文件名和扩展名

  // // 将这个 <a> 元素添加到页面中
  // document.body.appendChild(link);

  // // 触发点击事件，以便浏览器开始下载文件
  // link.click();

  // // 下载完成后，删除这个 <a> 元素
  // document.body.removeChild(link);
};

export function downloadWithUrl(fileLink: string, fileName: string) {
  const link = document.createElement('a');
  link.href = fileLink;
  link.download = fileName;

  document.body.appendChild(link);

  link.click();

  document.body.removeChild(link);
}
const isEmpty = (val: any) => val === undefined || val === null || val === '';

// 导出一个函数，用于过滤掉对象中的空值
export function filterEmptyObject(obj: any) {
  const newObj: any = {};

  Object.keys(obj).forEach((key) => {
    const val = obj[key];
    if (!isEmpty(val)) {
      newObj[key] = val;
    }
  });

  return newObj;
}
