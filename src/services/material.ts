import { postJavaRequest, getJavaRequest } from './common';

// 获取 班级材料列表
export async function getMaterialOutdoorsList(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(
    `/api/business/material/getOutSideCombinedMaterial`,
    params,
  );
}

// 获取 户外材料区域列表
export async function fetchOutList(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(
    `/api/business/material/getOutSideMaterialArea`,
    params,
  );
}

// 根据参数查询 户外材料
export async function queryOutProductList(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(
    `/api/business/material/queryOutSideCombinedMaterial`,
    params,
  );
}

// 根据id 删除 户外材料
export async function deleteOutProduct(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(
    `/api/business/material/deleteMaterialById`,
    params,
  );
}

// 添加材料 -保存-淘宝链接and自行编辑 /business/material/saveMaterialInfo
export async function saveMaterialInfo(data: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(`/api/business/material/saveMaterialInfo`, data);
}

// 生成组合材料玩法 /business/material/generatePlayList
export async function generatePlayList(data: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(`/api/business/material/generatePlayList`, data);
}

// 导出清单
// /business/material/exportMaterialInfo?combinedIds=61&fields=productionMethod&combinedIds=62

export async function exportMaterialInfo(data: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(
    `/api/business/material/exportMaterialInfo`,
    data,
  );
}

// 导出清单
// /business/material/exportMaterialInfo?combinedIds=61&fields=productionMethod&combinedIds=62

export async function exportMaterialDetail(data: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(
    `/api/business/material/exportMaterialDetail`,
    data,
  );
}

// 获取材料基本详情
export async function getMaterialInfoDetails(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(
    `/api/business/material/getCombinedDetail`,
    params,
  );
}

// 获取材料分析
export async function getMaterialAnalysis(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(
    `/api/business/material/getMaterialAnalysis`,
    params,
  );
}

// 获取玩法推荐
export async function getMaterialPlayList(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(
    `/api/business/material/getMaterialPlayList`,
    params,
  );
}

// 请求字典
export const getDictList = (params: Record<string, any>) => {
  return getJavaRequest('/api/business/material/getDictByCode', params);
};

// 编辑材料基本信息
export const editMaterialInfo = (data: Record<string, any>) => {
  return postJavaRequest('/api/business/material/editMaterBaseInfo', data);
}

// 编辑材料分析
export const editMaterialAnalysis = (data: Record<string, any>) => {
  return postJavaRequest('/api/business/material/editMaterialAnalysis', data);
}

// 获取AI玩法推荐详情
export const getAIPlayDetail = (params: Record<string, any>) => {
  return getJavaRequest('/api/business/material/getPlayDetail', params);
}

// 采用AI玩法推荐
export const UseAIPlay = (params: Record<string, any>) => {
  return getJavaRequest('/api/business/material/adoptPlay', params);
}

// 删除玩法
export const deletePlay = (params: Record<string, any>) => {
  return getJavaRequest('/api/business/material/deletePlay', params);
}
// 保存已采纳玩法
export const saveUsePlay = (data: Record<string, any>) => {
  return postJavaRequest('/api/business/material/editPlayDetail', data);
}

// 获取材料
export const getCoreExperience = (params: Record<string, any>) => {
  return getJavaRequest('/api/business/material/getCoreExperience', params);
}