import { deleteRequest, getRequest, postRequest, putRequest, postJavaRequest } from './common';

/** 新增 - 课程 */
export async function addSubject(params: Record<string, any>) {
  return await postRequest<API.CaptchaResult>('/pts/business/subject', params);
}

/** 查询 - 课程详情 */
export async function fetchSubjectDetail(id: string | number) {
  return await getRequest(`/pts/business/subject/detail/${id}`);
}

/** 查询 - 课程统计 */
export async function fetchSubjectStatistics(id: string | number) {
  return await getRequest(`/pts/business/subject/statistics/${id}`);
}


/** 更新 - 课程信息 */
export async function updateSubject(params: Record<string, any>) {
  return await putRequest('/pts/business/subject/u', params);
}

/** 更新 - 课程状态信息 */
export async function updateSubjectState(params: Record<string, any>) {
  return await putRequest('/pts/business/subject/s', params);
}

/** 新增 - 课程列表 */
export async function getClassList(params: Record<string, any>) {
  return await getRequest<API.CaptchaResult>('/pts/business/subject/list_v2', params);
}


/** 新增 - 阶段 */
export async function addStage(params: Record<string, any>) {
  return await postRequest<API.CaptchaResult>('/pts/business/subject-stage', params);
}

/** 新增 - 阶段列表 */
export async function getStateList(params: Record<string, any>) {
  return await getRequest<API.CaptchaResult>('/pts/business/subject-stage/list', params);
}

/** 更新 - 阶段信息 */
export async function updateStage(params: Record<string, any>) {
  return await putRequest('/pts/business/subject-stage/u', params);
}

/** 更新 - 阶段信息 */
export async function updateStageSort(params: Record<string, any>) {
  return await putRequest('/pts/business/subject-stage/u-sort', params);
}

/** 删除规则 DELETE /api/rule */
export async function delStage(options?: { [key: string]: any }) {
  return deleteRequest<Record<string, any>>(`/pts/business/subject-stage/${options?.id}`);
}

/** 查询活动列表 */
export async function getActivityList(params: Record<string, any>) {
  return await getRequest<API.CaptchaResult>('/pts/business/subject-activity/list', params);
}

/** 新增活动信息 */
export async function addActivity(params: Record<string, any>) {
  return await postRequest<API.CaptchaResult>('/pts/business/subject-activity', params);
}

/** 修改活动信息 */
export async function updateActivity(params: Record<string, any>) {
  return await putRequest<API.CaptchaResult>('/pts/business/subject-activity/u', params);
}

/** 删除活动 DELETE /api/rule */
export async function delActivity(options?: { [key: string]: any }) {
  return deleteRequest<Record<string, any>>(`/pts/business/subject-activity/${options?.id}`);
}

/** 更新 - 阶段信息 */
export async function updateActivitySort(params: Record<string, any>) {
  return await putRequest('/pts/business/subject-activity/u-sort', params);
}

/** 查询矩阵 */
export async function getMatrix(params: Record<string, any>) {
  return await getRequest<API.CaptchaResult>('/pts/business/matrix/list', params);
}

/** 查询指标 */
export async function getTarget(params: Record<string, any>) {
  return await getRequest<API.CaptchaResult>('/pts/business/target/list', params);
}

/** 修改评价 */
export async function updateEvaluate(params: Record<string, any>) {
  return await putRequest<API.CaptchaResult>(
    '/pts/business/subject-activity/u-additional-questions',
    params,
  );
}

/** 新增活动信息 */
export async function addResourceByTimer(params: Record<string, any>) {
  return await postRequest<API.CaptchaResult>(
    '/pts/business/subject-activity/create-resource-ids-by-timestamp',
    params,
  );
}

/** 新增活动信息 */
export async function getResourceByTimer(params: Record<string, any>) {
  return await getRequest<API.CaptchaResult>(
    '/pts/business/subject-activity/fetch-resources-by-timestamp',
    params,
  );
}

/** 查询活动详情 */
export async function getClassDetail(params: Record<string, any>) {
  return await getRequest<API.CaptchaResult>(`/pts/business/subject-activity/detail/${params.id}`);
}

/** 查询活动上下文 */
export async function getClassContext(params: Record<string, any>) {
  return await getRequest<API.CaptchaResult>(`/pts/business/subject-activity/context`, params);
}

/** 查询是否完成 ai生成 */
export async function getAICreate(params: Record<string, any>) {
  return await postJavaRequest<API.AdminResponse>(`/api/business/subject/detail/${params}`);
}

/** java 课程列表  */
export async function getJavaClassList(data: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(`/api/business/subject/pageList`, data);
}

/** 修改课程状态  */
export async function updataActState(data: Record<string, any>) {
  return await putRequest<API.CaptchaResult>(`/pts/business/subject/s`, data);
}

/** 请求AI主题书列表  */ 
export async function getTaskList(data: Record<string, any>) {
  return await getRequest<API.CaptchaResult>(`/pts/business/task/list`, data);
  
}

/** 请求模板  */ 
export async function getTemplateList(data: Record<string, any>) {
	return await getRequest<API.CaptchaResult>(`/pts/business/document-template/list`, data);
}