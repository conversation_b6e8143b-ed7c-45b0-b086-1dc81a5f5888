import { postJavaRequest } from './common';

/** 查询 - 周报列表
 * @param {string} attendanceDate - 当前日期 yyyy-MM-dd
 * @param {number} schoolId - 学校ID
 */
export async function getStatisticsList(data: object) {
  return await postJavaRequest<API.CaptchaResult>('/api/business/attendance/summary/classList', data);
}

/** 查询 - 周报列表
 * @param {string} attendanceDate - 当前日期 yyyy-MM-dd
 * @param {number} schoolId - 学校ID
 */
export async function exportStatisticsExcel(data: object) {
  return await postJavaRequest<API.CaptchaResult>('/api/business/attendance/summary/export', data, { getResponse: true, responseType: 'blob' },);
}
