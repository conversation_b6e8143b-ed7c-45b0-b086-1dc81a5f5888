import {
  deleteRequest,
  getRequest,
  postRequest,
  putRequest,
  postJavaRequest,
  getJavaRequest,
} from './common';

/** 查询 - 儿童列表 */
export async function getChildList(params: Record<string, any>) {
  return await getRequest<API.CaptchaResult>('/pts/business/child/list', params);
}

/** 查询 - 儿童详情 */
export async function fetchChildDetail(id: string | number) {
  return await getRequest(`/pts/business/child/detail/${id}`);
}

/** 新增 - 儿童 */
export async function addChild(params: Record<string, any>) {
  return await postRequest<API.CaptchaResult>('/pts/business/child', params);
}

/** 更新 - 儿童信息 */
export async function updateChild(params: Record<string, any>) {
  return await putRequest('/pts/business/child/u', params);
}

/** 更新 - 儿童状态信息 */
export async function updateChildState(params: Record<string, any>) {
  return await putRequest('/pts/business/child/s', params);
}

/** 删除 - 儿童信息 */
export async function delChild(options?: { [key: string]: any }) {
  return deleteRequest<Record<string, any>>(`/pts/business/child/${options?.id}`);
}

/** 从文件url导入儿童和家长信息 */
export async function importChildFromUrl(params: Record<string, any>) {
  return postJavaRequest(
    `/api/business/child/import/v2?fileUrl=${params.fileUrl}&classId=${params.classId}`,
  );
}

/** 下载模板 */
export async function getDownladTemplate(params: Record<string, any>) {
  return postJavaRequest(
    `/api/business/child/export/template?classId=${params.classId}`,
    {},
    { getResponse: true, responseType: 'blob' },
  );
}

/** 获取导入任务状态 */
export async function getImportTask(params: string) {
  return getJavaRequest(`/api/business/child/import/task/${params}`);
}

/** 获取人脸列表 */
export async function getFaceList(id: number) {
  return postJavaRequest(`/api/business/childFace/list?childId=${id}`);
}

/** 添加人脸列表 */
export async function addFace(data: object) {
  return postJavaRequest(`/api/business/childFace/add`, data);
}

/** 删除人脸列表 */
export async function deleteFace(params: number) {
  return postJavaRequest(`/api/business/childFace/delete/${params}`);
}

/** 家长列表 */
export async function getParentList(params: number) {
  return getJavaRequest(`/api/business/parent/listByChildId/${params}`);
}

/** 新增家长列表 */
export async function addParent(data: object) {
  return postJavaRequest(`/api/business/parent/add`, data);
}

/** 根据详情获取家长详情 */
export async function getParentDetail(data: object) {
  return getJavaRequest(`/api/business/parent/detail`, data);
}

/** 修改家长 */
export async function updateParentDetail(data: object) {
  return postJavaRequest(`/api/business/parent/update`, data);
}


/** 删除家长 */
export async function deleteParentDetail(id: number) {
  return postJavaRequest(`/api/business/parent/delete/${id}`);
}

