import { postJavaRequest, getJavaRequest } from './common';

// 获取班级儿童考勤列表 儿童+基本数据  参数 attendanceDate + classId
export const getChildrenAttendanceList = (params: any) => {
  return postJavaRequest('/api/business/attendance/class', params);
};
// /business/attendance/manualSave
// 参数
// userType 人员类型，儿童-0，非儿童-1
// userIdList childId
// state 考勤状态 1-出勤 2-离园 3-事假 4-病假
// sickLeaveTypeCode  病假类型编码，病假类型时必填
// remark  备注：
export const saveChildrenAttendance = (params: any) => {
  return postJavaRequest('/api/business/attendance/manualSave', params);
};

// 病假类型 business/material/getDictByCode
export const getSickLeaveTypeList = () => {
  return getJavaRequest('/api/business/material/getDictByCode', { code: 'sickLeaveType' });
};

// 查询本班级的心情 /moodDictionary/queryClassMoodDictionary Query 参数  classId
export const getChildrenMoodList = (classId: number, params: any = {}) => {
  return postJavaRequest(`/api/business/moodDictionary/queryClassMoodDictionary?classId=${classId}`, params);
};

// 保存儿童心情记录 /business/moodRecord/saveOrUpdate
// 参数: childId, classId, schoolId, moodId
export const saveChildrenMoodRecord = (params: any) => {
  return postJavaRequest('/api/business/moodRecord/saveOrUpdate', params);
};

// 心情统计 按班级统计柱状图 /business/moodRecord/statsClassMoodBar
export const getChildrenMoodBarStats = (params: any) => {
  return postJavaRequest('/api/business/moodRecord/statsClassMoodBar', params);
};
// 心情统计 按班级统计柱雷达图 /business/moodRecord/statsClassMoodRadar
export const getChildrenMoodRadarStats = (params: any) => {
  return postJavaRequest('/api/business/moodRecord/statsClassMoodRadar', params);
};

// 心情统计 按儿童统计点状图 /business/moodRecord/statsChildMoodSeries
export const getChildrenMoodSeriesStats = (params: any) => {
  return postJavaRequest('/api/business/moodRecord/statsChildMoodSeries', params);
};

// 心情统计 按儿童统计雷达图 /business/moodRecord/statsChildMoodRadar
export const getChildrenMoodRadarSeriesStats = (params: any) => {
  return postJavaRequest('/api/business/moodRecord/statsChildMoodRadar', params);
};

// 查询儿童心情记录 /business/moodRecord/pageList
export const getChildrenMoodRecordList = (params: any) => {
  return postJavaRequest('/api/business/moodRecord/pageList', params);
};
