import { postJavaRequest } from './common';

// 分页列表查询
export async function queryMoodDictionaryPageList(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(`/api/business/moodDictionary/pageList`, params);
}

//   新增或修改心情字典
export async function saveOrUpdateMoodDictionary(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(
    '/api/business/moodDictionary/saveOrUpdate',
    params,
  );
}

//   删除心情
export async function deleteMoodDictionary(id: number) {
  return await postJavaRequest<API.CaptchaResult>(`/api/business/moodDictionary/delete/${id}`, {});
}
