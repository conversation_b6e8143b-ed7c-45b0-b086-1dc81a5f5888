import { getRequest, postRequest, putRequest } from './common';

/** 查询 - 反馈列表 */
export async function getFeedbackList(params: Record<string, any>) {
  return await getRequest<API.CaptchaResult>('/pts/business/feedback/list', params);
}

/** 查询 - 反馈详情 */
export async function getFeedbackDetail(params: Record<string, any>) {
  return await getRequest<API.CaptchaResult>(`/pts/business/feedback/detail/${params.id}`);
}

/** 新增 - 反馈 */
export async function addFeedback(params: Record<string, any>) {
  return await postRequest<API.CaptchaResult>('/pts/business/feedback', params);
}

/** 更新 - 反馈信息 */
export async function updateFeedback(params: Record<string, any>) {
  return await putRequest('/pts/business/feedback/u', params);
}
