import { getRequest, postRequest } from './common';

/** 查询 - 儿童列表 */
export async function getEvalutaionList(params: Record<string, any>) {
  return await getRequest<API.CaptchaResult>('/pts/business/child_evaluation/list', {
    term: 1,
    ...params,
  });
}

export interface EvalutaionDetailParams {
  evaluationId?: number;
  matrixId?: number;
  childId?: number;
}
/** 查询 - 儿童详情 */
export async function fetchEvalutaionDetail(params: EvalutaionDetailParams) {
  return await getRequest(`/pts/business/child_evaluation/detail`, params);
}

/** 更新 - 儿童信息 */
export async function updateEvalutaionDetail(params: Record<string, any>) {
  return await postRequest('/pts/business/child_evaluation/save', params);
}

/** 获取 - 批量查询儿童评估 */
export async function batchEvalutaionList(params: Record<string, any>) {
  return await getRequest('/pts/business/child_evaluation/batch', params);
}

export function batchEvalutaionSave(params: any) {
  return postRequest('/pts/business/child_evaluation/batch_save', params);
}

/** 获取 - 统计信息 */

export function evalutaionStatistics(evaluationId: any) {
  return getRequest('/pts/business/child_evaluation/statistics', { evaluationId });
}
