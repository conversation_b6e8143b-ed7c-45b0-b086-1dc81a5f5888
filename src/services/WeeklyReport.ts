import {
  getJavaRequest
} from './common';

/** 查询 - 周报列表
 * @params {object} week - 周报时间 本周 上周，选择周数，格式：2025-07-07 - 2025-07-13
 * @params {object} schoolId - 学校ID
 */
export async function getWooklyReportList(params: object) {
  return await getJavaRequest<API.CaptchaResult>('/api/business/statistics/listWeeklyReports', params);
}

/** 查询 - 获取周报数据
 * @params {object} week - 周报时间 本周 上周，选择周数，格式：2025-07-07 - 2025-07-13
 * @params {object} schoolId - 学校ID
 */
export async function getListWeek() {
  return await getJavaRequest<API.CaptchaResult>('/api/business/statistics/listWeek');
}
