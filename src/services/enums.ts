import { useModel } from '@umijs/max';

function parseEnumToOptions(enums: EnumValue<string | number>) {
  const obj: EnumOption[] = [];
  Object.keys(enums).forEach((key) => {
    obj.push({
      value: /\d+/.test(key) ? Number(key) : key,
      label: enums[key] as string,
    });
  });

  return obj;
}
export function useGetEnumsByKey(key: keyof Enums | (keyof Enums)[]) {
  const { initialState } = useModel('@@initialState');

  const enums: EnumsMap = initialState!.enums;

  if (typeof key === 'string') {
    return [enums[key]];
  }
  return key.map((e) => enums[e]);
}

/** 通过key获取字典 EnumOption 形式返回*/
export function useGetEnumOptionsByKey(key?: keyof Enums | (keyof Enums)[]) {
  const { initialState } = useModel('@@initialState');

  if (!key) {
    return [];
  }
  const enums: EnumsMap = initialState!.enums;

  if (typeof key === 'string') {
    return [parseEnumToOptions(enums[key])];
  }
  return key.map((e) => parseEnumToOptions(enums[e]));
}
