// @ts-ignore
/* eslint-disable */

declare namespace API {
  type AdminResponse = {
    message?: string;
    metadata?: any;
    data?: any;
    status?: number;
    flag?: boolean;
    code?: number;
  };

  type RequestOptions = {
    /**
     * 列表类型
     * 1 - 分页，即有 data+page+pageNo
     * 5 - 列表，但只要第 1 个，即 detail 接口
     * 10 - 全部，即只有 data
     * 15 - 字典值列表，转 Select 值列表
     */
    listType?: number;
    banAfter?: boolean; // 是否禁止自动解析请求结果，默认为false
    getResponse?: boolean; // 是否返回整个响应对象，默认为false
    responseType?: 'json' | 'text' | 'blob' | 'arraybuffer' | 'document'; // 响应类型
  };

  type CurrentUser = {
    id?: number;
    nickname?: string;
    headerId?: number;
    createdAt?: string;
    updatedAt?: string;
    deleteAt?: string;
    mobile?: string;
    state?: number;
    currentTerm: number;
    currentClassId: number;
    currentSchoolId: number;
    name?: string
  };

  type CaptchaResult = {
    data?: {
      code: string;
    };
  };

  type LoginResult = {
    data?: {
      token: string;
      user: CurrentUser;
    };
  };

  type PageParams = {
    current?: number;
    pageSize?: number;
  };

  // == 资源结构
  type ResourceDTO = {
    filepath: string;
    hash: string;
    filename: string;
    fileCategory: number;
    category: number;
  };
  type ResourceItem = BaseResultItem & ResourceDTO;

  type FileItem = {
    id: number;
    uri: string;
    filename: string;
  };

  type SyncFileData = {
    uid: string;
    filename: string;
    uri: string;
    id: number;
    hash: string;
  };

  /** 字典类型 */
  enum DictionaryCategory {
    // 分类
    category = 31,
    // 标签
    Tag = 32,
    // 年级
    // Grade = 3,
    // 品牌
    Brand = 5,
    // 标签
    Tag = 7,
    // // 品质
    // Quality = 8,
    // // 安全性
    // Safety = 9,
    // // 区域
    // Area = 11,
    // // 规格
    // Specification = 13,
    // // 老师学历
    // TeacherEducation = 15,
    // 材质
    Material = 17,
    // // 角色
    // Role = 21,
  }

  /** 状态 */
  enum StateStatus {
    // 停用
    Off = 0,
    // 启用
    On = 1,
  }

  /** 资源类型 */
  enum SourceType {
    // 图片
    Image = 1,
    // 视频
    Video = 5,
    // 文件
    File = 10,
  }

  /** 资源业务类型 */
  enum SourceBusinessType {
    // 通用类型
    Common = 1,
    // 用户 - 头像
    UserAvatar = 10,
    // 商品 - 预览图
    ProductHeader = 15,
    // 商品 - 轮播图
    ProductBanner = 16,
    // 商品 - 详情图
    ProductDetail = 17,
    // 商品 - 使用说明
    ProductInstruction = 19,
    // 学校 - 详情图
    SchoolDetail = 21,
    // 采购单
    Order = 101,
  }

  /** 地区类型 */
  enum AreaType {
    // 国家
    Country = 0,
    // 省
    Province = 1,
    // 城市
    City = 2,
    // 区县
    District = 3,
    // 街道
    Street = 4,
  }

  /** 维度 */
  enum GradeEnum {
    // 领域
    Field = 1,
    // 大维度
    ParentGrade = 2,
    // 维度
    Grade = 3,
    // 子维度
    ChildrenGrade = 4,
  }

  type RuleListItem = {
    key?: number;
    disabled?: boolean;
    href?: string;
    avatar?: string;
    name?: string;
    owner?: string;
    desc?: string;
    callNo?: number;
    status?: number;
    updatedAt?: string;
    createdAt?: string;
    progress?: number;
  };

  type MatrixeProps = {
    matrix1Id: number;
    matrix1Title: string;
    matrix2Id: number;
    matrix2Title: string;
    matrix3Id: number;
    matrix3Title: string;
    targetId: number;
    targetTitle: string;
    matrixesSet: any
}
  /** 区域材料 */
  type  ClassAreaProductProps = {
    title: string;
    classId: number;
    areaId: number;
    headerId: string;
    suitable: string;
    maxAge: string;
    minAge: string;
    taobaoLink: string;
    purpose: string;
    change: string;
    interest: string;
    info: string;
    gameplay: string;
    note: string;
    header?: any;
    id?: number;
    state: number;
    schoolClass: {
      title: string,
      id: number
    },
    extra?: {
      matrixes: MatrixeProps[],
      matrixesSet: MatrixeProps[],
    }
  }

  type RuleList = {
    data?: RuleListItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type LoginParams = {
    mobile?: string;
    countryCode?: string;
    code?: string;
    autoLogin?: boolean;
  };

  type ErrorResponse = {
    /** 业务约定的错误码 */
    errorCode: string;
    /** 业务上的错误信息 */
    errorMessage?: string;
    /** 业务上的请求是否成功 */
    success?: boolean;
  };

  type NoticeIconList = {
    data?: NoticeIconItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type NoticeIconItemType = 'notification' | 'message' | 'event';

  type NoticeIconItem = {
    id?: string;
    extra?: string;
    key?: string;
    read?: boolean;
    avatar?: string;
    title?: string;
    status?: string;
    datetime?: string;
    description?: string;
    type?: NoticeIconItemType;
  };

  type TPrivilege = {
    id?: string;
    value: string;
  };
  type TUserManager = {
    id?: string;
    name: string;
    mobile: string;
    state: number;
  };

  type EnumProps = {
    [key: number]: string | { desc: string; key: number; prefix: string };
  };
}
