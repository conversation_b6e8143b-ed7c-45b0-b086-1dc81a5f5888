// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';
import { message } from 'antd';

enum EEnv {
  local = 'local',
  localhost = 'localhost',
  development = 'development',
  staging = 'staging',
  production = 'production',
}

export const generateUrl = (url: string) => {
  let param = process.env.APP_ENV === "production" ? 'prodjava' : 'devjava';
  // 判断是否是Java API请求

  const baseUrl = url.startsWith('/api')
    ? serveUrlMap[param]
    : serveUrlMap[param];
  console.log(process.env.APP_ENV);

  return `${baseUrl}`;
};

const serveUrlMap = {
  [EEnv.local]: 'http://************:3582',  
  [EEnv.development]: 'https://dev-api.mypacelab.com',
  devjava: 'https://jsapi.mypacelab.com', // JAVA API dev
  [EEnv.staging]: 'https://dev-api.mypacelab.com',
  [EEnv.production]: 'https://api.mypacelab.com',
  prodjava: 'https://japi.mypacelab.com', // JAVA API prod
};

export const BASE_URL = serveUrlMap[process.env.APP_ENV || 'development'];

/** 本地存储 - 读写token */
export function setToken(token: string): void {
  localStorage.setItem('token', token);
}

/** 本地存储 - 清空token */
export function clearToken() {
  localStorage.removeItem('token');
}

function getQueryParam(paramName: string) {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(paramName); // 如果不存在，返回 null
}

export function getToken(): string {
  const tk = localStorage.getItem('token') || getQueryParam('token')
  if (tk) {
    return tk as string;
  }
  return '';
}

/**
 * 通用 - post方法
 * @param url
 * @param body
 * @param options
 * @returns
 */
export async function postRequest<T extends API.AdminResponse>(
  url: string,
  body?: any,
  options?: API.RequestOptions,
) {
  const { token: urlToken, headers, ...rest } = body || {};
  const token = urlToken || (await getToken());

  const result: API.AdminResponse = await request<T>(`${BASE_URL}${url}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json', Authorization: token },
    data: rest,
    ...options,
  });

  if (result.status !== 0 && result.status !== 1) {
    const error: any = new Error(result.message);
    throw error;
  }

  return result;
}

/**
 * 通用 - put方法
 * @param url
 * @param body
 * @param options
 * @returns
 */
export async function putRequest<T extends API.AdminResponse>(
  url: string,
  body?: any,
  options?: API.RequestOptions,
) {
  const token = await getToken();

  const result: API.AdminResponse = await request<T>(`${BASE_URL}${url}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json', Authorization: token },
    data: body,
    ...options,
  });

  if (result.status !== 0 && result.status !== 1) {
    const error: any = new Error(result.message);
    throw error;
  }

  return result;
}

/**
 * 通用 - get方法
 * @param url
 * @param params
 * @param options
 * @returns
 */
export async function getRequest<T extends API.AdminResponse>(
  url: string,
  params: any = {},
  options?: API.RequestOptions,
) {
  const token = await getToken();

  const result: API.AdminResponse = await request<T>(`${BASE_URL}${url}`, {
    method: 'GET',
    headers: { Authorization: token },
    params,
    ...options,
  });

  if (result.status !== 0 && result.status !== 1) {
    const error: any = new Error(result.message);
    throw error;
  }

  return result;
}

/**
 * 通用 - delete方法
 * @param url
 * @param body
 * @param options
 * @returns
 */
export async function deleteRequest<T extends API.AdminResponse>(
  url: string,
  body?: any,
  options?: API.RequestOptions,
) {
  const token = await getToken();

  const result: API.AdminResponse = await request<T>(`${BASE_URL}${url}`, {
    method: 'DELETE',
    headers: { 'Content-Type': 'application/json', Authorization: token },
    data: body,
    ...options,
  });

  if (result.status !== 0 && result.status !== 1) {
    const error: any = new Error(result.message);
    throw error;
  }

  return result;
}

/**
 * java后台 - post方法
 * @param url
 * @param body
 * @param options
 * @returns
 */
export async function postJavaRequest<T extends API.AdminResponse>(
  url: string,
  body?: any,
  options?: API.RequestOptions,
) {
  const { token: urlToken, ...rest } = body || {};
  const token = urlToken || (await getToken());
  // "X-Instance-Selector": "host=*************" 解决接口响应变慢的问题，注意测试版有效，生产未测试
  const result: API.AdminResponse = await request<T>(`${generateUrl(url)}${url}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json', Authorization: token, "X-Instance-Selector": "host=*************" },
    data: body,
    ...options,
  });

  if (result.status !== 0 && result.status !== 1 && result.status !== 200) {
    const error: any = new Error(result.message);
    console.log(error);
     message.error(result?.message || result?.error?.stack);
    // throw error;
  }

  return result;
}

/**
 * java后台 - get方法
 * @param url
 * @param body
 * @param options
 * @returns
 */
export async function getJavaRequest<T extends API.AdminResponse>(
  url: string,
  params: any = {},
  body: any = {},
  options?: API.RequestOptions,
) {
  const token = await getToken();
  // "X-Instance-Selector": "host=*************" 解决接口响应变慢的问题，注意测试版有效，生产未测试
  const result: API.AdminResponse = await request<T>(`${generateUrl(url)}${url}`, {
    method: 'GET',
    headers: { Authorization: token, "X-Instance-Selector": "host=*************" },
    params,
    body,
    ...options,
  });

  if (result.status !== 0 && result.status !== 1) {
    const error: any = new Error(result.message);
    throw error;
  }

  return result;
}


