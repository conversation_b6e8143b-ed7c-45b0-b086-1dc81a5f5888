import { getRequest } from './common';

// 学校数据 /pts/business/data_show/school_data

export async function getSchoolData(params: any) {
  return getRequest('/pts/business/data_show/school_data', params);
}

// // 获取未进区儿童列表（男/女）
// export async function listChild(params: any) {
//   return getJavaRequest('/api/business/childAreaEntry/listChildNoEntry', params);
// }

// 当日数据
export async function getTodayData(params: any) {
  return getRequest('/pts/business/data_show/getAttendanceStats', params);
}

// 学习活动统计周数据  三个图表
export async function getLearnActivityStatsWeek(params: any) {
  return getRequest('/pts/business/data_show/getLearnActivityStatsWeek', params);
}

// 游戏活动统计数据
export async function getGameActivityStats(params: any) {
  return getRequest('/pts/business/data_show/getGameActivityStats', params);
}

// 查询 - 游戏活动统计数据-按周统计（观察记录和一对一倾听）
export async function getGameActivityStatsByWeek(params: any) {
  return getRequest('/pts/business/data_show/getGameActivityStatsWeek', params);
}
