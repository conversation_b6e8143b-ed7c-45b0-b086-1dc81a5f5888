import { postJavaRequest, getJavaRequest } from './common';

// 获取未进区儿童列表（男/女）
export async function listChild(params: any) {
  return getJavaRequest('/api/business/childAreaEntry/listChildNoEntry', params);
}

// 获取区域及区域儿童列表
export async function listArea(params: any) {
  return getJavaRequest('/api/business/childAreaEntry/listChildEntryByArea', params);
}

// 幼儿进区
export async function entry(params: any) {
  return postJavaRequest('/api/business/childAreaEntry/entry', params);
}

// 出区
// 支持三种参数: classId(班级所有儿童出区), areaId(该区域所有儿童出区), childId(单个儿童出区)
export async function exitArea(params: any) {
  return postJavaRequest('/api/business/childAreaEntry/exitAllAreas', params);
}



// 幼儿进区-历史操作 /business/childAreaEntry/history/entry
// 参数 childId areaId entryDate
export async function entryHistory(params: any) {
  return postJavaRequest('/api/business/childAreaEntry/history/entry', params);
}

// 获取班级/区域儿童列表-历史操作 /business/childAreaEntry/history/listChild
// 参数 classId areaId entryDate
export async function listChildHistory(params: any) {
  return getJavaRequest('/api/business/childAreaEntry/history/listChild', params);
}

// 获取班级区域列表（带实时人数-历史操作） /business/childAreaEntry/history/listArea
// 参数 classId entryDate
export async function listAreaHistory(params: any) {
  return getJavaRequest('/api/business/childAreaEntry/history/listArea', params);
}
