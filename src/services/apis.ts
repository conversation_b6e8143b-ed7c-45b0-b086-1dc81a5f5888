import { getMatrix } from './class';
import { getRequest, postRequest, putRequest, postJavaRequest } from './common';
import { DictionaryCategory } from './constants';

/** 发送验证码 POST /pts/sms */
export async function getCaptcha(body: {
  /** 手机号 */
  mobile?: string;
  /** 国别号 */
  countryCode?: string;
}) {
  return await postRequest<API.CaptchaResult>('/pts/sms', body);
}

export async function adminLogin(body: {
  /** 手机号 */
  mobile?: string;
  /** 国别号 */
  countryCode?: string;
  /** 验证码 */
  code?: string;
}) {
  return await postRequest<API.LoginResult>('/pts/business/user/phone', body);
}

export async function adminLoginPwd(body: {
  /** 手机号 */
  mobile?: string;
  /** 密码 */
  pwd?: string;
}) {
  return await postRequest<API.LoginResult>('/pts/business/user/pwd', body);
}

/** 微信二维码登录 */
export async function getLoginQr(body: {
  appId: string; // 'wx7f6f7f7f7f7f7f7f 小程序的APPID'
}) {
  return await postJavaRequest<any>('/api/uaa/wa/generateAssistLoginQr', body);
}

/** 轮询二维码状态 */
export async function getQrcodeStatus(body: { payload: string }) {
  return await postJavaRequest<any>('/api/uaa/wa/wxQrcodeStatus', body);
}

/** 查询 - 配置项 */
export async function getDictionary(params: {
  current: number;
  pageSize: number;
  offset?: number;
  category?: API.DictionaryCategory;
  // fileCategory?: API.SourceBusinessType;
}) {
  return await getRequest('/pts/business/dictionary/list', params);
}

/** 新增 - 配置项 */
export async function addDictionary(params: { category: number; value: string; sort: number }) {
  return await postRequest('/pts/business/dictionary', params);
}

/** 修改 - 配置项 */
export async function updateDictionary(params: { id: number; value: string; sort: number }) {
  return await putRequest('/pts/business/dictionary/u', params);
}

/** 修改 - 密码 */
export async function updatePwd(params: { pwd: string; newPwd: string }) {
  return await putRequest('/pts/business/user/pwd', params);
}

/** 重置 - 密码 */
export async function resetPwd() {
  return await postRequest('/pts/business/user/reset_pwd');
}

/** 修改 - 配置项状态 */
export async function updateDictionaryState(params: { id: number; state: number }) {
  return await putRequest('/pts/business/dictionary/s', params);
}

/** 更新权限管理 GET /pts/business/permission/save */
export async function updatePermission(params: { id: string }) {
  return postRequest<API.RuleListItem>('/pts/business/permission/save', params);
}

/** 权限管理列表 GET /pts/business/permission/api/list */
export async function getPermission() {
  return getRequest<Record<string, any>>('/pts/business/permission/api/list');
}

/** 查询权限详情 GET /pts/business/permission/detail */
export async function getPermissionDetail(params: { id: string }) {
  return getRequest<Record<string, any>>(`/pts/business/permission/detail/${params.id}`);
}

/** 获取 - 资源列表 */
export async function fetchSourceList(params: {
  current: number;
  pageSize: number;
  offset?: number;
  category?: API.SourceType;
  fileCategory?: API.SourceBusinessType;
}) {
  return await getRequest('/pts/business/resource/list', params);
}

/** 获取 - 用户信息 */
export async function fetchCurrentUser() {
  return await getRequest('/pts/business/user/me');
}

/** 获取 - 当前用户已绑定的学校 */
export async function fetchUserSchools() {
  return await getRequest('/pts/business/school/my_list');
}
/** 获取 - 当前用户已绑定的班级 */
export async function fetchUserClasses() {
  try {
    return await getRequest('/pts/business/class/my_list');
  } catch (_) {
    return { data: [] };
  }
}

/** 更新 - 当前用户班级学期 */
export async function updateUserClassesTerm(data: any) {
  return await putRequest('/pts/admin/class/u', data);
}

/** 获取 - 当前用户 选择学校+班级 操作 */
export async function selectUserSchoolsAndClasses(body: {
  currentSchoolId?: number | null;
  currentClassId?: number | null;
  currentTerm?: number | null;
}) {
  return await putRequest('/pts/business/user/change', body);
}

export async function updateResource(resource: API.ResourceItem) {
  return await postRequest('/api/oss/editImgInfo', resource);
}

/** ==== 图片上传 */
export async function fetchUploadToken() {
  return await getRequest('/pts/business/resource/auth');
}

export async function createResource(resource: API.ResourceDTO) {
  return await postRequest('/pts/business/resource/sync', resource);
}

export async function fetchResourceByHash(body: { hash: string; filename: string }) {
  return await postRequest(`/pts/business/resource/hash`, body);
}

/** 商品导出excel */
export async function fetchProductExport(params: any) {
  return await getRequest('/pts/business/excel/product', params);
}

/** 获取 - 当前用户 信息 */
export async function refreshState() {
  return await putRequest('/pts/business/user/refresh');
}

/** ==== 班级管理 */
/** 获取 - 班级列表 */
export async function fetchClassList(params: {
  current: number;
  pageSize: number;
  offset?: number;
  title?: string;
  state?: API.StateStatus;
}) {
  return await getRequest('/pts/business/class/list', params);
}

/** ==== 学校管理 */
/** 获取 - 学校列表 */
export async function fetchSchoolList(params: {
  current: number;
  pageSize: number;
  offset?: number;
  title?: string;
  state?: API.StateStatus;
}) {
  return await getRequest('/pts/business/school/list', params);
}

/** 获取 - 通用枚举值 */
export async function fetchEnum() {
  return await getRequest('/pts/enum');
}

/** ==== 配置项 */
/** 获取 - 配置项列表 */
export async function fetchDictionaryList(params: {
  current: number;
  pageSize: number;
  offset?: number;
  category?: DictionaryCategory;
  state?: API.StateStatus;
  value?: string;
}) {
  // TODO 修改为business
  return await getRequest('/pts/business/dictionary/list', params);
}

export const getLists = async (params: {
  pid?: number;
  pageSize?: number;
  current?: number;
  matrix3Id?: number;
}) => {
  try {
    const res = await getMatrix(params);
    return res.data?.map((item: any) => ({ label: item.title, value: item.id }));
  } catch (e) {
    return [];
  }
};

//

/** 获取 - 通用枚矩阵 */
export async function fetchMatrix() {
  return await getRequest('/pts/business/matrix/tree');
}

// 获取-模版列表
export async function getTemplateList(params: Record<string, any>) {
  return await getRequest('/pts/business/document-template/list', params);
}

// 下载模板
export async function exportTemplate(params: Record<string, any>) {
  return await postRequest('/pts/business/document-template/export-document', params);
}

// 修改昵称
export async function updateAvatarOrNickName(params: { id: number; nickname: string }) {
  return await postJavaRequest('/api/global/user/updateAvatarOrNickName', params);
}
