// business/child_report

import { getRequest, postRequest, putRequest } from './common';

export interface ChildAddEntity {
  classId: number;
  term: number;
  childId: number;
}
/** 查询 - 儿童报告统计列表 */
export async function fetchChildReportStatistic(data: any) {
  return await getRequest(`/pts/business/child_report/statistics`, data);
}

/** 查询 - 儿童报告详情列表 */
export async function fetchChildReportList(id: any) {
  return await getRequest(`/pts/business/child_report/list?childId=` + id);
}

/** 查询 - 儿童报告详情 */
export async function fetchChildReportDetail(id: any) {
  return await getRequest(`/pts/business/child_report/detail/` + id);
}
export async function updateChildReportDetail(data: any) {
  return await putRequest(`/pts/business/child_report/u`, data);
}

/** 新增 - 儿童报告生成 */
export async function addChildReportList(data: ChildAddEntity) {
  return await postRequest(`/pts/business/child_report`, data);
}
