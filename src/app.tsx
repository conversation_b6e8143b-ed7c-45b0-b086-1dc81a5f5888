import { AvatarDropdown, AvatarName, Float<PERSON><PERSON>on, Footer } from '@/components';
import GlobalProvider from '@/components/GlobalProvider';
import {
  fetchCurrentUser,
  fetchDictionaryList,
  fetchEnum,
  fetchUserClasses,
  setToken,
} from '@/services/api';
import type { Settings as LayoutSettings } from '@ant-design/pro-components';
// import { SettingDrawer } from '@ant-design/pro-components';
import type { RunTimeLayoutConfig } from '@umijs/max';
import { history } from '@umijs/max';
import defaultSettings from '../config/defaultSettings';
import { errorConfig } from './requestErrorConfig';

const loginPath = '/user/login';
const staticPaths = ['/temp/ClassAreaProduct'];

function parseEnumToOptions(enums: Enums) {
  const obj: EnumsMap = {} as any;
  Object.keys(enums).forEach((key) => {
    const e = enums[key as keyof Enums];
    obj[key as keyof Enums] = {};

    Object.keys(e).forEach((k) => {
      const val = e[k];
      if (typeof val === 'string' || typeof val === 'number') {
        obj[key as keyof Enums][k] = val;
      } else {
        if ('desc' in val) {
          obj[key as keyof Enums][k] = val.desc;
        } else {
          obj[key as keyof Enums][k] = val;
        }
      }
    });
  });

  return obj;
}

const commonEnums = {
  YesOrNoEnum: {
    0: '否',
    1: '是',
  },
};

const fetchUserInfo = async () => {
  try {
    const urlInstance = new URL(window.location.href);
    const params = new URLSearchParams(urlInstance.search);
    const token = params.get('token');
    if (token) {
      setToken(token);
    }
    const res = await fetchCurrentUser();
    return res.data;
  } catch (error) {
    history.push(loginPath);
  }
  return undefined;
};

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  currentUser?: API.CurrentUser;
  userClass?: any[];
  loading?: boolean;
  currentClass?: any;
  dictionaryList?: Array<{ id: number; value: string; category: number; label: string }>;
  enums: EnumsMap;
}> {
  // 如果不是登录页面，执行
  const { location } = history;
  // console.log(staticPaths, history.location.pathname)
  if (staticPaths.includes(history.location.pathname)) {
    return {
      settings: defaultSettings as Partial<LayoutSettings>,
    } as any;
  }

  const enumsRes = await fetchEnum();
  if (location.pathname !== loginPath) {
    const currentUser: API.CurrentUser = await fetchUserInfo();
    // TODO:
    const dictionaryData = await fetchDictionaryList({ current: 1, pageSize: 0, state: 1 });
    const userClass = (await fetchUserClasses()).data || [];
    const currentClass = userClass.find((u: any) => u.id === currentUser.currentClassId) || {};
    return {
      enums: parseEnumToOptions({ ...enumsRes.data, ...commonEnums }),
      userClass,
      currentUser,
      currentClass: [currentClass.id, currentClass.term],
      settings: defaultSettings as Partial<LayoutSettings>,
      dictionaryList: dictionaryData?.data.map((v: any) => ({ ...v, value: v.id, label: v.value })),
    };
  }

  return {
    enums: parseEnumToOptions({ ...enumsRes.data, ...commonEnums }),

    settings: defaultSettings as Partial<LayoutSettings>,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState }) => {
  
  const currentClassId = initialState?.currentUser?.currentClassId;
  const title = initialState?.userClass?.find((item: any) => item.id === currentClassId)?.schoolTitle;
  
  const waterMarkContent= `${initialState?.currentUser?.name}@${title}`;
  return {
    avatarProps: {
      src: '',
      title: <AvatarName />,
      render: (_, avatarChildren) => {
        return <AvatarDropdown>{avatarChildren}</AvatarDropdown>;
      },
    },

    waterMarkProps: {
      content: waterMarkContent,
      fontSize: 13,
      fontColor: 'rgba(127,127,127,0.2)',
    },
    footerRender: () => <Footer />,
    onPageChange: () => {
      const { location } = history;
      // 如果没有登录，重定向到 login
      if (!initialState?.currentUser && location.pathname !== loginPath) {
        history.push(loginPath);
      }
    },
    links: [],
    menuHeaderRender: undefined,
    style: { width: '100%', height: '100%' },
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    // 增加一个 loading 的状态
    childrenRender: (children: any) => {
      // if (initialState?.loading) return <PageLoading />;
      return (
        <GlobalProvider>
          {children} <FloatButton />
        </GlobalProvider>
      );
    },
    ...initialState?.settings,
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request = {
  ...errorConfig,
};

export function onRouteChange({ location }) {
  console.log(history, location);

  // 如果要访问的页面不是登录页，且用户还没有登录，则跳转到登录页
}
