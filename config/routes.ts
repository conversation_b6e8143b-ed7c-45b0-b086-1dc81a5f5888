/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn， 注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @param hideInMenu 配置为 true 后此路由将不会出现在侧边栏导航菜单中（默认 false）
 * @doc https://umijs.org/docs/guides/routes
 */
export default [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: 'login',
        path: '/user/login',
        component: './User/Login',
      },
    ],
  },
  {
    name: 'acount',
    path: '/account/password',
    component: './Account/password',
    hideInMenu: true,
  },
  {
    name: '修改昵称',
    path: '/account/NickName',
    component: './Account/NickName',
    hideInMenu: true,
  },
  {
    path: '/welcome',
    name: '欢迎',
    icon: 'smile',
    component: './Welcome',
  },

  {
    name: '教学研AI助手',
    flatMenu: true,
    hideInMenu: true,
    routes: [
      {
        name: '教学研AI助手',
        icon: 'FontColorsOutlined',
        path: '/AI/AI',
        component: './AI/index',
      },
    ],
  },
  // 常用功能
  {
    path: '/InCommonUse',
    name: '常用功能',
    icon: 'SketchOutlined',
    routes: [
      {
        path: '/InCommonUse/index',
        name: '幼儿进区计划',
        icon: 'SketchOutlined',
        component: './ChildrenInArea/index',
        footerRender: false,
      },
      // 班级幼儿心情管理
      {
        path: '/InCommonUse/childrenMood/list',
        name: '班级幼儿心情管理',
        icon: 'SmileOutlined',
        component: './ChildrenMood/index',
        flatMenu: true,
        hideInMenu: false,
      },
    ],
  },
  // 幼儿管理
  {
    path: '/child',
    name: '幼儿管理',
    routes: [
      {
        path: '/child/list',
        name: '幼儿列表',
        icon: 'UsergroupAddOutlined',
        component: './Child/index',
      },
      // { name: '家长管理' }
      {
        path: '/child/assess/list',
        name: '儿童核心经验评估',
        icon: 'HighlightOutlined',
        component: './Evaluation/index',
      },
      {
        path: '/child/edit/:id',
        name: '儿童新增/编辑',
        hideInMenu: true,
        component: './Child/Edit',
      },
      {
        path: '/child/detail/:id',
        name: '儿童详情',
        hideInMenu: true,
        component: './Child/Detail',
      },
      {
        path: '/child/parent/list/:id',
        name: '家长列表',
        icon: 'TeamOutlined',
        component: './Parent/index',
        hideInMenu: true,
      },
    ],
  },
  // 课程管理
  {
    path: '/course',
    name: '课程管理',
    icon: 'editOutlined',
    routes: [
      {
        path: '/course/draft',
        name: '草稿箱',
        icon: 'DropboxOutlined',
        component: './Class/draft',
      },
      {
        path: '/course/list',
        name: '班级课程',
        icon: 'DropboxOutlined',
        component: './Class/index',
      },
      {
        path: '/course/alllist',
        name: '园本课程库',
        icon: '',
        component: './Class/index',
      },
    ],
  },
  // 班级管理
  {
    path: '/classman',
    name: '班级管理',
    icon: 'BuildOutlined',
    routes: [
      // 班级幼儿心情管理
      // {
      //   path: '/InCommonUse/childrenMood/list',
      //   name: '班级幼儿心情管理',
      //   icon: 'SmileOutlined',
      //   component: './ChildrenMood/index',
      //   flatMenu: true,
      //   hideInMenu: false,
      // },
      // TODO: 请按照对应名称填写对应的路径
      // { name: '班级教师管理' },
      // { name: '家长班级圈' },
      // { name: '师幼互动评价' },
      // { name: '班级幼儿日记' },
      {
        path: '/classman/childrenAttendance',
        name: '幼儿出勤',
        icon: 'ContactsOutlined',
        component: './ChildrenAttendance/index',
        // 不展示页脚
        footerRender: false,
        hideInMenu: false,
      },
      // { name: '幼儿心情记录' },
      // { name: '幼儿作品列表' },
    ],
  },
  // 课程管理
  {
    path: '/class',
    name: '课程管理',
    icon: 'read',
    flatMenu: true,
    routes: [
      {
        path: '/class/evaluate/:id',
        name: '课程评价',
        component: './Class/EvaluatePage',
        hideInMenu: true,
      },
      {
        path: '/class/edit/:id',
        name: '编辑活动',
        component: './Class/UpdateActivity',
        hideInMenu: true,
      },
      {
        path: '/class/detail/:id',
        name: '课程详情',
        hideInMenu: true,
        component: './Class/List',
        // component: './Class/Detail',
      },
      {
        path: '/class/activity/detail/:id',
        name: '活动详情',
        hideInMenu: true,
        component: './Class/ActivityDetail',
        // component: './Class/Detail',
      },
      {
        path: '/class/upload',
        name: '文件上传',
        hideInMenu: true,
        component: './Class/AddUpload',
        // component: './Class/Detail',
      },
      {
        path: '/class',
        redirect: '/class/list',
        hideInMenu: true,
        // component: './Class/Detail',
      },
      // {

      //   path: '/class/stage/:id',
      //   name: '阶段列表',
      //   hideInMenu: true,
      //   component: './Class/StageList',
      // },
    ],
  },
  // 游戏管理
  {
    path: '/GameManager',
    name: '游戏管理',
    icon: 'LikeOutlined',
    routes: [
      {
        path: '/GameManager/region',
        name: '区域设置',
        icon: '',
        routes: [
          {
            path: '/GameManager/region/class',
            name: '班级区域',
            icon: '',
            component: './region/Class/index',
            hideInMenu: false,
          },
          {
            path: '/GameManager/region/outdoors',
            name: '户外区域',
            icon: '',
            component: './region/Outdoors/index',
            hideInMenu: false,
          },
        ],
      },
      {
        path: '/GameManager/material',
        name: '材料管理',
        icon: '',
        routes: [
          {
            path: '/GameManager/material/class',
            name: '班级材料',
            icon: '',
            component: './material/Class/index',
            hideInMenu: false,
          },
          {
            path: '/GameManager/material/edit',
            name: '材料详情',
            component: './material/components/edit',
            hideInMenu: true,
          },
          {
            path: '/GameManager/material/outdoors',
            name: '户外材料',
            icon: '',
            component: './material/Outdoors/index',
            hideInMenu: false,
          },
        ],
      },
      {
        path: '/GameManager/childrenAnalysis',
        name: '幼儿进区记录',
        icon: '',
        component: './childrenAnalysis/index',
        hideInMenu: false,
      },
      {
        path: '/GameManager/Observation',
        name: '观察记录',
        icon: 'SmileOutlined',
        component: './Observation/index',
        hideInMenu: false,
      },
      {
        path: '/GameManager/observation/Detail/:id',
        name: '观察记录详情',
        icon: 'SmileOutlined',
        component: './Observation/Detail',
        hideInMenu: true,
      },
    ],
  },

  {
    path: '/classAreaProduct',
    name: '区域材料属性',
    icon: 'BgColorsOutlined',
    hideInMenu: false,
    flatMenu: true,
    routes: [
      {
        path: '/classAreaProduct/list',
        name: '区域材料',
        icon: 'BgColorsOutlined',
        component: './ClassAreaProduct/index',
      },
      {
        path: '/classAreaProduct/edit/:id',
        name: '区域材料编辑',
        component: './ClassAreaProduct/ClassAreaProductEdit',
        hideInMenu: true,
      },
      {
        path: '/classAreaProduct/detail/:id',
        name: '区域材料详情',
        component: './ClassAreaProduct/ClassAreaProductDetail',
        hideInMenu: true,
      },
      {
        path: '/classAreaProduct/statictics',
        name: '区域材料统计',
        hideInMenu: true,
        component: './ClassAreaProduct/ClassAreaProductStatictics',
      },
    ],
  },

  // 报表
  {
    path: '/journaling',
    name: '报表',
    icon: 'BookOutlined',
    routes: [
      {
        path: '/journaling/AttendanceStatistics',
        name: '全园出勤统计',
        icon: '',
        component: './AttendanceStatistics/index',
        hideInMenu: false,
      },
      {
        path: '/journaling/WeeklyReport',
        name: '周报',
        icon: '',
        component: './WeeklyReport/index',
      },
    ],
  },

  {
    path: '/evaluation',
    name: '课程评估',
    flatMenu: true,
    routes: [
      {
        path: '/evaluation/edit/:id',
        name: '课程评估新增/编辑',
        hideInMenu: true,
        component: './Evaluation/Edit',
      },
      {
        path: '/evaluation/evaluateDetailBatch/:id',
        name: '批量评估',
        hideInMenu: true,
        component: './Evaluation/EvaluationDetailBatch',
      },
      {
        path: '/evaluation/evalutaionStatistics/:id',
        name: '儿童核心经验评估结果统计',
        hideInMenu: true,
        component: './Evaluation/EvalutaionStatistics',
      },
    ],
  },

  {
    path: '/admin',
    name: 'admin',
    icon: 'crown',
    access: 'canAdmin',
    routes: [
      {
        path: '/admin',
        redirect: '/admin/sub-page',
      },
      {
        path: '/admin/sub-page',
        name: 'sub-page',
        component: './Admin',
      },
    ],
  },
  {
    path: '/',
    redirect: '/welcome',
  },
  {
    path: '*',
    layout: false,
    component: './404',
  },
  {
    path: '/classCoreExperience',
    name: '班级核心经验统计',
    icon: 'BgColorsOutlined',
    flatMenu: true,
    routes: [
      {
        path: '/classCoreExperience/list',
        name: '班级核心经验统计',
        icon: 'BarChartOutlined',
        component: './ClassCoreExperience/index',
      },
    ],
  },
  {
    name: '临时页面',
    icon: 'smile',
    flatMenu: true,
    layout: false,
    hideInMenu: true,
    routes: [
      {
        name: '区域材料',
        layout: false,
        path: '/temp/ClassAreaProduct',
        component: './Temp/ClassAreaProductEdit',
        // 不展示顶栏
        headerRender: false,
        // 不展示页脚
        footerRender: false,
        // 不展示菜单
        menuRender: false,
      },
    ],
  },

  {
    path: '/childrenReport',
    name: '儿童报告',
    icon: 'BgColorsOutlined',
    flatMenu: true,
    routes: [
      {
        path: '/childrenReport/list',
        name: '儿童报告',
        icon: 'ContactsOutlined',
        component: './ChildrenReport/index',
      },
      {
        path: '/childrenReport/ChildrenReportEdit/:id',
        name: '儿童报告查看',
        component: './ChildrenReport/ChildrenReportEdit',
        hideInMenu: true,
      },
      {
        path: '/childrenReport/ChildrenReportView/:id',
        name: '儿童报告查看',
        component: './ChildrenReport/ChildrenReportView',
        hideInMenu: true,
      },
      {
        path: '/childrenReport/ChildrenReportList/:id',
        name: '儿童报告列表',
        component: './ChildrenReport/ChildrenReportList',
        hideInMenu: true,
      },
      // {
      //   path: '/childrenReport/detail/:id',
      //   name: '儿童报告详情',
      //   component: './ChildrenReport/ChildrenReportDetail',
      //   hideInMenu: true,
      // },
    ],
  },
  {
    path: '/dataDashboard',
    name: '数据大屏',
    icon: 'DashboardOutlined',
    // flatMenu: true,
    // hideInMenu: true,
    routes: [
      {
        path: '/dataDashboard/index',
        name: '园所数据大屏',
        icon: 'DashboardOutlined',
        component: './DataDashboard/index',
        // 不展示页脚
        footerRender: false,
        hideInMenu: false,
      },
      {
        path: '/dataDashboard/children',
        name: '儿童数据大屏',
        icon: 'DashboardOutlined',
        component: './ChildrenData/index',
        // 不展示页脚
        footerRender: false,
        hideInMenu: false,
      },
    ],
  },

  {
    path: '/feedback',
    name: '反馈列表',
    icon: 'feedback',
    flatMenu: true,
    hideInMenu: true,
    routes: [
      {
        path: '/feedback/list',
        name: '反馈列表',
        icon: 'editOutlined',
        hideInMenu: true,
        component: './Feedback/index',
      },
      {
        path: '/feedback/add',
        name: '新增反馈',
        hideInMenu: true,
        component: './Feedback/Edit',
      },
      {
        path: '/feedback/detail/:id',
        name: '反馈详情',
        hideInMenu: true,
        component: './Feedback/Detail',
      },
      {
        path: '/feedback',
        redirect: '/feedback/list',
        hideInMenu: true,
      },
    ],
  },
];
