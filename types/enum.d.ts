type EnumValue<T> = {
  [key: string | number]: T;
};

// type FeedbackStateValue = {
//   adminDesc: string;
//   businessDesc: string;
// };

type Enums = {
  Area: EnumValue<string>;
  ResourceCategoryEnum: EnumValue<string>;
  ResourceFileCategoryEnum: EnumValue<string>;
  YesOrNoEnum: EnumValue<string>;
  SexEnum: EnumValue<string>;
  EducationalEnum: EnumValue<string>;
  ProductCategoryEnum: EnumValue<string>;
  CommonStateEnum: EnumValue<string>;
  ChildStateEnum: EnumValue<string>;
  FeedbackCategoryEnum: EnumValue<string>;
  // FeedbackStateEnum: any;
  SubjectActivityOrganizationFormEnumDesc: EnumValue<string>;
  FeedbackUrgencyEnum: EnumValue<string>;
  EvaluationCategoryEnum: EnumValue<string>;
  DictionaryCategoryEnum: Record<
    string,
    {
      key: number;
      desc: string;
      prefix: string;
    }
  >;
  DictionaryCategory: Record<
    string,
    {
      key: number;
      desc: string;
      prefix: string;
    }
  >;
  SubjectTermEnum: EnumValue<string>;
  StatisticsCategoryEnum: EnumValue<string>;
  SubjectMatrixCategoryEnum: EnumValue<{
    value: number;
    desc: string;
  }>;
  SubjectActivityInterestScoreEnumDesc: EnumValue<string>;
  SubjectActivityDifficultyScoreEnumDesc: EnumValue<string>;
  SubjectActivityOrganizationFormEnum: EnumValue<string>;
  SubjectActivityPlannedOrGenerationEnum: EnumValue<string>;
  SubjectActivityInterestScoreEnum: EnumValue<number | string>;
  SubjectActivityDifficultyScoreEnum: EnumValue<number | string>;
  SubjectActivityPlannedOrGenerationEnumDesc: EnumValue<string>;
  DocumentTemplateCategoryEnumDesc: EnumValue<number | string>;
  SubjectTypeEnumDesc: EnumValue<number | string>;
};

type EnumsMap = {
  [key in keyof Enums]: EnumValue<string | number>;
};

type EnumOption = {
  label: string;
  value: any;
};
